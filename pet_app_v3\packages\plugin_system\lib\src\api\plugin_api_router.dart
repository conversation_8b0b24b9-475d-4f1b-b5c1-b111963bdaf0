/*
---------------------------------------------------------------
File name:          plugin_api_router.dart
Author:             lgnorant-lu
Date created:       2025-07-27
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件系统API路由处理模块
---------------------------------------------------------------
Change History:
    2025-07-27: 从plugin_rest_api.dart重构拆分出API路由处理功能;
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:plugin_system/src/api/plugin_api_interface.dart';

/// API路由处理器
/// 
/// 负责API端点注册、路由匹配、中间件处理等功能
class PluginApiRouter {
  PluginApiRouter();

  /// 端点注册表
  final Map<String, ApiEndpoint> _endpoints = <String, ApiEndpoint>{};

  /// 中间件列表
  final List<ApiMiddleware> _middleware = <ApiMiddleware>[];

  /// API版本
  ApiVersion get version => const ApiVersion(major: 1, minor: 0, patch: 0);

  /// 支持的版本列表
  List<ApiVersion> get supportedVersions => <ApiVersion>[
        const ApiVersion(major: 1, minor: 0, patch: 0),
      ];

  /// 注册端点
  void registerEndpoint(ApiEndpoint endpoint) {
    final key = '${endpoint.method.name}:${endpoint.path}';
    _endpoints[key] = endpoint;
  }

  /// 注销端点
  void unregisterEndpoint(HttpMethod method, String path) {
    final key = '${method.name}:$path';
    _endpoints.remove(key);
  }

  /// 添加中间件
  void addMiddleware(ApiMiddleware middleware) {
    _middleware.add(middleware);
  }

  /// 移除中间件
  void removeMiddleware(String name) {
    _middleware.removeWhere((middleware) => middleware.name == name);
  }

  /// 处理请求
  Future<ApiResponse<dynamic>> handleRequest(ApiRequest request) async {
    try {
      // 处理中间件
      ApiRequest? processedRequest = request;
      for (final middleware in _middleware) {
        processedRequest = await middleware.processRequest(processedRequest!);
        if (processedRequest == null) {
          return ApiResponse.error<dynamic>(
            statusCode: 400,
            message: 'Request blocked by middleware: ${middleware.name}',
          );
        }
      }

      // 查找端点
      final endpoint = _findEndpoint(request);
      if (endpoint == null) {
        return ApiResponse.error<dynamic>(
          statusCode: 404,
          message: 'Endpoint not found: ${request.method.name} ${request.path}',
        );
      }

      // 验证版本
      if (!validateVersion(request.version)) {
        return ApiResponse.error<dynamic>(
          statusCode: 400,
          message: 'Unsupported API version: ${request.version}',
        );
      }

      // 执行处理函数
      var response = await endpoint.handler(processedRequest!);

      // 处理响应中间件
      for (final middleware in _middleware.reversed) {
        response = await middleware.processResponse(processedRequest, response);
      }

      return response;
    } catch (e, stackTrace) {
      // 处理错误中间件
      var errorResponse = ApiResponse.error<dynamic>(
        statusCode: 500,
        message: 'Internal server error',
        errors: <String>[e.toString()],
      );

      for (final middleware in _middleware.reversed) {
        errorResponse = await middleware.processError(request, e, stackTrace);
      }

      return errorResponse;
    }
  }

  /// 获取API文档
  Map<String, dynamic> getApiDocumentation() {
    final endpoints = <Map<String, dynamic>>[];

    for (final endpoint in _endpoints.values) {
      endpoints.add(<String, dynamic>{
        'method': endpoint.method.name,
        'path': endpoint.path,
        'description': endpoint.description,
        'parameters': endpoint.parameters
            .map(
              (ApiParameter p) => <String, Object?>{
                'name': p.name,
                'type': p.type.toString(),
                'location': p.location.name,
                'required': p.required,
                'description': p.description,
              },
            )
            .toList(),
        'responses': endpoint.responses,
        'deprecated': endpoint.deprecated,
      });
    }

    return <String, dynamic>{
      'version': version.version,
      'supportedVersions':
          supportedVersions.map((ApiVersion v) => v.version).toList(),
      'endpoints': endpoints,
    };
  }

  /// 获取端点列表
  List<ApiEndpoint> getEndpoints() => _endpoints.values.toList();

  /// 验证API版本
  bool validateVersion(ApiVersion requestVersion) => supportedVersions
      .any((ApiVersion v) => v.isCompatibleWith(requestVersion));

  /// 查找端点
  ApiEndpoint? _findEndpoint(ApiRequest request) {
    // 精确匹配
    final exactKey = '${request.method.name}:${request.path}';
    if (_endpoints.containsKey(exactKey)) {
      return _endpoints[exactKey];
    }

    // 参数化路径匹配
    for (final endpoint in _endpoints.values) {
      if (endpoint.method == request.method &&
          _matchParameterizedPath(endpoint.path, request.path)) {
        return endpoint;
      }
    }

    return null;
  }

  /// 匹配参数化路径
  bool _matchParameterizedPath(String pattern, String path) {
    final patternSegments = pattern.split('/').where((s) => s.isNotEmpty).toList();
    final pathSegments = path.split('/').where((s) => s.isNotEmpty).toList();

    if (patternSegments.length != pathSegments.length) {
      return false;
    }

    for (int i = 0; i < patternSegments.length; i++) {
      final patternSegment = patternSegments[i];
      final pathSegment = pathSegments[i];

      // 如果是参数占位符，跳过检查
      if (patternSegment.startsWith('{') && patternSegment.endsWith('}')) {
        continue;
      }

      // 精确匹配
      if (patternSegment != pathSegment) {
        return false;
      }
    }

    return true;
  }

  /// 提取路径参数
  String? extractPathParameter(String path, String paramName) {
    try {
      // 实现真实的路径参数提取逻辑
      // 支持 RESTful 路径模式，如 /api/plugins/{id}

      final pathSegments = path.split('/').where((s) => s.isNotEmpty).toList();

      // 定义路由模式映射
      final routePatterns = <String, List<String>>{
        'plugins': ['api', 'plugins', '{id}'],
        'plugin-config': ['api', 'plugins', '{id}', 'config'],
        'plugin-enable': ['api', 'plugins', '{id}', 'enable'],
        'plugin-disable': ['api', 'plugins', '{id}', 'disable'],
        'plugin-update': ['api', 'plugins', '{id}', 'update'],
      };

      // 尝试匹配每个模式
      for (final pattern in routePatterns.values) {
        if (pathSegments.length == pattern.length) {
          bool matches = true;
          String? paramValue;

          for (int i = 0; i < pattern.length; i++) {
            if (pattern[i] == '{$paramName}') {
              paramValue = pathSegments[i];
            } else if (pattern[i] != pathSegments[i]) {
              matches = false;
              break;
            }
          }

          if (matches && paramValue != null) {
            return paramValue;
          }
        }
      }

      return null;
    } catch (e) {
      print('提取路径参数失败: $e');
      return null;
    }
  }

  /// 注册默认端点
  void registerDefaultEndpoints() {
    // 这里可以注册一些默认的端点
    // 具体的业务端点由各个管理器注册
  }
}
