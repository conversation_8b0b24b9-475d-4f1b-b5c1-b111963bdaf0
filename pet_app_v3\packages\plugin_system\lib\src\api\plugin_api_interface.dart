/*
---------------------------------------------------------------
File name:          plugin_api_interface.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        插件API接口定义 - Phase 4.3 API设计优化
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 4.3 - API接口定义实现;
---------------------------------------------------------------
*/

import 'dart:async';

/// API版本
class ApiVersion {
  const ApiVersion({
    required this.major,
    required this.minor,
    required this.patch,
    this.preRelease,
  });

  /// 主版本号
  final int major;

  /// 次版本号
  final int minor;

  /// 补丁版本号
  final int patch;

  /// 预发布版本
  final String? preRelease;

  /// 版本字符串
  String get version {
    final base = '$major.$minor.$patch';
    return preRelease != null ? '$base-$preRelease' : base;
  }

  /// 是否兼容
  bool isCompatibleWith(ApiVersion other) =>
      major == other.major && minor >= other.minor;

  @override
  String toString() => version;

  @override
  bool operator ==(Object other) =>
      other is ApiVersion &&
      major == other.major &&
      minor == other.minor &&
      patch == other.patch &&
      preRelease == other.preRelease;

  @override
  int get hashCode => Object.hash(major, minor, patch, preRelease);
}

/// HTTP方法
enum HttpMethod {
  get,
  post,
  put,
  patch,
  delete,
  head,
  options,
}

/// API响应状态
enum ApiResponseStatus {
  success,
  error,
  warning,
  info,
}

/// API请求
class ApiRequest {
  const ApiRequest({
    required this.method,
    required this.path,
    required this.version,
    this.headers = const <String, String>{},
    this.queryParameters = const <String, dynamic>{},
    this.body,
    this.timeout = const Duration(seconds: 30),
  });

  /// HTTP方法
  final HttpMethod method;

  /// 请求路径
  final String path;

  /// API版本
  final ApiVersion version;

  /// 请求头
  final Map<String, String> headers;

  /// 查询参数
  final Map<String, dynamic> queryParameters;

  /// 请求体
  final dynamic body;

  /// 超时时间
  final Duration timeout;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'method': method.name,
        'path': path,
        'version': version.version,
        'headers': headers,
        'queryParameters': queryParameters,
        'body': body,
        'timeout': timeout.inMilliseconds,
      };
}

/// API响应
class ApiResponse<T> {
  const ApiResponse({
    required this.status,
    required this.statusCode,
    this.data,
    this.message,
    this.errors = const <String>[],
    this.metadata = const <String, dynamic>{},
    this.headers = const <String, String>{},
  });

  /// 响应状态
  final ApiResponseStatus status;

  /// HTTP状态码
  final int statusCode;

  /// 响应数据
  final T? data;

  /// 响应消息
  final String? message;

  /// 错误列表
  final List<String> errors;

  /// 元数据
  final Map<String, dynamic> metadata;

  /// 响应头
  final Map<String, String> headers;

  /// 是否成功
  bool get isSuccess => status == ApiResponseStatus.success;

  /// 是否有错误
  bool get hasErrors => errors.isNotEmpty;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'status': status.name,
        'statusCode': statusCode,
        'data': data,
        'message': message,
        'errors': errors,
        'metadata': metadata,
        'headers': headers,
      };

  /// 创建成功响应
  static ApiResponse<T> success<T>({
    T? data,
    String? message,
    Map<String, dynamic> metadata = const <String, dynamic>{},
    Map<String, String> headers = const <String, String>{},
  }) =>
      ApiResponse<T>(
        status: ApiResponseStatus.success,
        statusCode: 200,
        data: data,
        message: message,
        metadata: metadata,
        headers: headers,
      );

  /// 创建错误响应
  static ApiResponse<T> error<T>({
    required int statusCode,
    String? message,
    List<String> errors = const <String>[],
    Map<String, dynamic> metadata = const <String, dynamic>{},
    Map<String, String> headers = const <String, String>{},
  }) =>
      ApiResponse<T>(
        status: ApiResponseStatus.error,
        statusCode: statusCode,
        message: message,
        errors: errors,
        metadata: metadata,
        headers: headers,
      );
}

/// API端点定义
class ApiEndpoint {
  const ApiEndpoint({
    required this.method,
    required this.path,
    required this.handler,
    this.version,
    this.description,
    this.parameters = const <ApiParameter>[],
    this.responses = const <int, String>{},
    this.middleware = const <ApiMiddleware>[],
    this.deprecated = false,
  });

  /// HTTP方法
  final HttpMethod method;

  /// 路径
  final String path;

  /// 处理函数
  final Future<ApiResponse<dynamic>> Function(ApiRequest request) handler;

  /// 支持的版本
  final ApiVersion? version;

  /// 描述
  final String? description;

  /// 参数定义
  final List<ApiParameter> parameters;

  /// 响应定义
  final Map<int, String> responses;

  /// 中间件
  final List<ApiMiddleware> middleware;

  /// 是否已废弃
  final bool deprecated;
}

/// API参数定义
class ApiParameter {
  const ApiParameter({
    required this.name,
    required this.type,
    required this.location,
    this.required = false,
    this.description,
    this.defaultValue,
    this.validation,
  });

  /// 参数名
  final String name;

  /// 参数类型
  final Type type;

  /// 参数位置
  final ParameterLocation location;

  /// 是否必需
  final bool required;

  /// 描述
  final String? description;

  /// 默认值
  final dynamic defaultValue;

  /// 验证规则
  final ParameterValidation? validation;
}

/// 参数位置
enum ParameterLocation {
  path,
  query,
  header,
  body,
}

/// 参数验证
class ParameterValidation {
  const ParameterValidation({
    this.minLength,
    this.maxLength,
    this.pattern,
    this.minimum,
    this.maximum,
    this.allowedValues,
  });

  /// 最小长度
  final int? minLength;

  /// 最大长度
  final int? maxLength;

  /// 正则表达式
  final String? pattern;

  /// 最小值
  final num? minimum;

  /// 最大值
  final num? maximum;

  /// 允许的值
  final List<dynamic>? allowedValues;

  /// 验证值
  bool validate(dynamic value) {
    if (value == null) return true;

    if (value is String) {
      if (minLength != null && value.length < minLength!) return false;
      if (maxLength != null && value.length > maxLength!) return false;
      if (pattern != null && !RegExp(pattern!).hasMatch(value)) return false;
    }

    if (value is num) {
      if (minimum != null && value < minimum!) return false;
      if (maximum != null && value > maximum!) return false;
    }

    if (allowedValues != null && !allowedValues!.contains(value)) return false;

    return true;
  }
}

/// API中间件
abstract class ApiMiddleware {
  /// 中间件名称
  String get name;

  /// 处理请求
  Future<ApiRequest?> processRequest(ApiRequest request);

  /// 处理响应
  Future<ApiResponse<dynamic>> processResponse(
    ApiRequest request,
    ApiResponse<dynamic> response,
  );

  /// 处理错误
  Future<ApiResponse<dynamic>> processError(
    ApiRequest request,
    Object error,
    StackTrace stackTrace,
  );
}

/// 插件API接口
abstract class IPluginApi {
  /// API版本
  ApiVersion get version;

  /// 支持的版本列表
  List<ApiVersion> get supportedVersions;

  /// 注册端点
  void registerEndpoint(ApiEndpoint endpoint);

  /// 注销端点
  void unregisterEndpoint(HttpMethod method, String path);

  /// 添加中间件
  void addMiddleware(ApiMiddleware middleware);

  /// 移除中间件
  void removeMiddleware(String name);

  /// 处理请求
  Future<ApiResponse<dynamic>> handleRequest(ApiRequest request);

  /// 获取API文档
  Map<String, dynamic> getApiDocumentation();

  /// 获取端点列表
  List<ApiEndpoint> getEndpoints();

  /// 验证API版本
  bool validateVersion(ApiVersion requestVersion);

  /// 启动API服务
  Future<void> start();

  /// 停止API服务
  Future<void> stop();
}

/// 插件REST API接口
abstract class IPluginRestApi extends IPluginApi {
  // 插件管理端点

  /// 获取插件列表
  Future<ApiResponse<List<Map<String, dynamic>>>> getPlugins({
    int? page,
    int? limit,
    String? search,
    List<String>? tags,
  });

  /// 获取插件详情
  Future<ApiResponse<Map<String, dynamic>>> getPlugin(String pluginId);

  /// 安装插件
  Future<ApiResponse<Map<String, dynamic>>> installPlugin(
    String pluginId, {
    String? version,
    Map<String, dynamic>? config,
  });

  /// 卸载插件
  Future<ApiResponse<Map<String, dynamic>>> uninstallPlugin(String pluginId);

  /// 更新插件
  Future<ApiResponse<Map<String, dynamic>>> updatePlugin(
    String pluginId, {
    String? version,
  });

  /// 启用插件
  Future<ApiResponse<Map<String, dynamic>>> enablePlugin(String pluginId);

  /// 禁用插件
  Future<ApiResponse<Map<String, dynamic>>> disablePlugin(String pluginId);

  /// 获取插件配置
  Future<ApiResponse<Map<String, dynamic>>> getPluginConfig(String pluginId);

  /// 更新插件配置
  Future<ApiResponse<Map<String, dynamic>>> updatePluginConfig(
    String pluginId,
    Map<String, dynamic> config,
  );

  // 批量操作端点

  /// 批量安装插件
  Future<ApiResponse<Map<String, dynamic>>> batchInstallPlugins(
    List<String> pluginIds,
  );

  /// 批量卸载插件
  Future<ApiResponse<Map<String, dynamic>>> batchUninstallPlugins(
    List<String> pluginIds,
  );

  /// 批量更新插件
  Future<ApiResponse<Map<String, dynamic>>> batchUpdatePlugins(
    List<String> pluginIds,
  );

  // 系统管理端点

  /// 获取系统状态
  Future<ApiResponse<Map<String, dynamic>>> getSystemStatus();

  /// 获取系统配置
  Future<ApiResponse<Map<String, dynamic>>> getSystemConfig();

  /// 更新系统配置
  Future<ApiResponse<Map<String, dynamic>>> updateSystemConfig(
    Map<String, dynamic> config,
  );

  /// 获取系统日志
  Future<ApiResponse<List<Map<String, dynamic>>>> getSystemLogs({
    int? page,
    int? limit,
    String? level,
    DateTime? startTime,
    DateTime? endTime,
  });

  /// 获取性能指标
  Future<ApiResponse<Map<String, dynamic>>> getPerformanceMetrics({
    Duration? timeWindow,
  });
}
