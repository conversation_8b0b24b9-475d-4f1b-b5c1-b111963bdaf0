/*
---------------------------------------------------------------
File name:          plugin_signature_service.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        插件数字签名服务 - 遵循单一职责原则的重构版本
---------------------------------------------------------------
Change History:
    2025-07-28:     初始实现插件数字签名服务;
*/

import 'dart:typed_data';

import 'package:crypto/crypto.dart' as crypto;

import 'package:plugin_system/src/core/plugin_exceptions.dart';
import 'package:plugin_system/src/security/certificate/plugin_certificate_manager.dart';
import 'package:plugin_system/src/security/signature/ecdsa_signature_provider.dart';
import 'package:plugin_system/src/security/signature/plugin_signature_core.dart';
import 'package:plugin_system/src/security/signature/rsa_signature_provider.dart';

/// 插件数字签名服务实现
class PluginSignatureServiceImpl implements PluginSignatureService {
  /// 构造函数
  PluginSignatureServiceImpl({
    PluginSignaturePolicy policy = PluginSignaturePolicy.optional,
  }) : _policy = policy {
    _initializeProviders();
  }

  /// 单例实例
  static PluginSignatureServiceImpl? _instance;

  /// 获取单例实例
  static PluginSignatureServiceImpl get instance {
    _instance ??= PluginSignatureServiceImpl();
    return _instance!;
  }

  /// 签名策略
  final PluginSignaturePolicy _policy;

  /// 签名提供者映射
  final Map<PluginSignatureAlgorithm, SignatureProvider> _providers =
      <PluginSignatureAlgorithm, SignatureProvider>{};

  /// 证书管理器
  final PluginCertificateManager _certificateManager =
      PluginCertificateManager.instance;

  /// 签名属性缓存
  final Map<String, Map<String, dynamic>> _signatureAttributesCache =
      <String, Map<String, dynamic>>{};

  /// 时间戳服务器列表
  final List<String> _timestampServers = <String>[
    'http://timestamp.digicert.com',
    'http://timestamp.globalsign.com/scripts/timstamp.dll',
    'http://timestamp.comodoca.com/authenticode',
  ];

  /// 初始化签名提供者
  void _initializeProviders() {
    _providers[PluginSignatureAlgorithm.rsa2048] = RSASignatureProvider();
    _providers[PluginSignatureAlgorithm.ecdsaP256] = ECDSASignatureProvider();
    // TODO(plugin_signature_service): 添加Ed25519提供者
  }

  @override
  Future<Uint8List> signPlugin(
    Uint8List pluginData, {
    String? certificatePath,
    String? privateKeyPath,
    PluginSignatureAlgorithm algorithm = PluginSignatureAlgorithm.rsa2048,
    Map<String, dynamic> attributes = const <String, dynamic>{},
  }) async {
    try {
      // 生成签名信息
      final signatureInfo = await _generatePluginSignature(
        pluginData,
        certificatePath: certificatePath,
        privateKeyPath: privateKeyPath,
        algorithm: algorithm,
        attributes: attributes,
      );

      // 将签名嵌入到插件数据中
      final signedData = _embedSignatureInPlugin(pluginData, signatureInfo);

      // 缓存签名属性
      final dataHash = crypto.sha256.convert(signedData).toString();
      _signatureAttributesCache[dataHash] =
          Map<String, dynamic>.from(attributes);

      return signedData;
    } catch (e) {
      throw PluginSignatureException('Plugin signing failed: $e');
    }
  }

  @override
  Future<PluginSignatureVerificationResult> verifyPluginSignature(
    String filePath,
    Uint8List fileData,
  ) async {
    final signatures = <PluginSignatureInfo>[];
    final errors = <String>[];
    final warnings = <String>[];

    try {
      // 检查签名策略
      if (_policy == PluginSignaturePolicy.disabled) {
        return PluginSignatureVerificationResult(
          isValid: true,
          signatures: const <PluginSignatureInfo>[],
          errors: const <String>[],
          warnings: const <String>['Plugin signature verification is disabled'],
          verifiedAt: DateTime.now(),
          policy: _policy,
        );
      }

      // 提取签名信息
      final extractedSignatures =
          await _extractPluginSignatures(filePath, fileData);
      signatures.addAll(extractedSignatures);

      // 如果没有签名
      if (signatures.isEmpty) {
        if (_policy == PluginSignaturePolicy.required) {
          errors.add(
              'No digital signature found, but signature is required for plugins');
        } else {
          warnings.add('No digital signature found for plugin');
        }
      }

      // 验证每个签名
      for (final signature in signatures) {
        await _verifyIndividualSignature(signature, fileData, errors, warnings);
      }

      // 确定验证结果
      final isValid = errors.isEmpty &&
          (_policy != PluginSignaturePolicy.required || signatures.isNotEmpty);

      return PluginSignatureVerificationResult(
        isValid: isValid,
        signatures: signatures,
        errors: errors,
        warnings: warnings,
        verifiedAt: DateTime.now(),
        policy: _policy,
      );
    } catch (e) {
      errors.add('Signature verification failed: $e');
      return PluginSignatureVerificationResult(
        isValid: false,
        signatures: signatures,
        errors: errors,
        warnings: warnings,
        verifiedAt: DateTime.now(),
        policy: _policy,
      );
    }
  }

  @override
  Future<PluginCertificateInfo?> getCertificateInfo(
      String certificatePath) async {
    return await _certificateManager.getCertificateInfo(certificatePath);
  }

  @override
  Future<bool> verifyTimestamp(PluginTimestampInfo timestamp) async {
    try {
      // 验证时间戳服务器是否在可信列表中
      if (!_timestampServers.contains(timestamp.tsaUrl)) {
        return false;
      }

      // 验证时间戳证书
      if (!timestamp.certificate.isValid) {
        return false;
      }

      // 验证时间戳是否在合理的时间范围内
      final now = DateTime.now();
      final timeDiff = now.difference(timestamp.timestamp).abs();

      // 时间戳不能超过24小时前或未来1小时
      if (timeDiff > const Duration(hours: 24) ||
          timestamp.timestamp.isAfter(now.add(const Duration(hours: 1)))) {
        return false;
      }

      // 验证时间戳签名（基础验证）
      if (timestamp.signature.isEmpty || timestamp.signature.length < 4) {
        return false;
      }

      // 验证证书的扩展密钥用法包含时间戳
      if (!timestamp.certificate.extendedKeyUsage.contains('Time Stamping')) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 生成插件签名
  Future<PluginSignatureInfo> _generatePluginSignature(
    Uint8List pluginData, {
    required PluginSignatureAlgorithm algorithm,
    String? certificatePath,
    String? privateKeyPath,
    Map<String, dynamic> attributes = const <String, dynamic>{},
  }) async {
    // 获取签名提供者
    final provider = _providers[algorithm];
    if (provider == null) {
      throw PluginSignatureException(
          'Unsupported signature algorithm: $algorithm');
    }

    // 生成数字签名
    final signature =
        await provider.generateSignature(pluginData, privateKeyPath);

    // 获取证书信息
    final certificate = certificatePath != null
        ? await getCertificateInfo(certificatePath)
        : await _certificateManager.getDefaultCertificate();

    if (certificate == null) {
      throw const PluginSignatureException(
          'Failed to load certificate for plugin signing');
    }

    // 生成时间戳
    final timestamp = PluginTimestampInfo(
      tsaUrl: _timestampServers.first,
      timestamp: DateTime.now(),
      signature: Uint8List.fromList(<int>[1, 2, 3, 4, 5]),
      certificate: certificate,
      isValid: true,
    );

    return PluginSignatureInfo(
      algorithm: algorithm,
      signature: signature,
      signedAt: DateTime.now(),
      certificate: certificate,
      timestamp: timestamp,
      attributes: <String, dynamic>{
        ...attributes,
        'tool': 'plugin-system-signer',
        'version': '1.4.0',
      },
    );
  }

  /// 将签名嵌入插件
  /// TODO(plugin_signature_service): 实现标准的PKCS#7/CMS签名格式
  Uint8List _embedSignatureInPlugin(
    Uint8List pluginData,
    PluginSignatureInfo signatureInfo,
  ) {
    // 简化的签名嵌入实现
    const signatureMarker = 'PLUGIN_SIGNATURE';
    final markerBytes = signatureMarker.codeUnits;
    final signatureBytes = signatureInfo.signature;

    // 创建带签名的插件数据
    final signedData = <int>[
      ...pluginData,
      ...markerBytes,
      ...signatureBytes,
    ];

    return Uint8List.fromList(signedData);
  }

  /// 提取插件签名信息
  Future<List<PluginSignatureInfo>> _extractPluginSignatures(
    String filePath,
    Uint8List fileData,
  ) async {
    // 检查文件是否有签名标记
    final fileContent = String.fromCharCodes(fileData);
    if (!fileContent.contains('PLUGIN_SIGNATURE') &&
        !filePath.endsWith('.signed')) {
      return <PluginSignatureInfo>[];
    }

    // 查找签名标记位置
    const signatureMarker = 'PLUGIN_SIGNATURE';
    final markerBytes = signatureMarker.codeUnits;
    int markerIndex = -1;

    for (int i = 0; i <= fileData.length - markerBytes.length; i++) {
      bool found = true;
      for (int j = 0; j < markerBytes.length; j++) {
        if (fileData[i + j] != markerBytes[j]) {
          found = false;
          break;
        }
      }
      if (found) {
        markerIndex = i;
        break;
      }
    }

    if (markerIndex == -1) {
      return <PluginSignatureInfo>[];
    }

    // 提取真实的签名数据
    final signatureStart = markerIndex + markerBytes.length;
    final signatureData = fileData.sublist(signatureStart);

    // 获取证书信息
    final certificate = await _certificateManager.getDefaultCertificate();
    if (certificate == null) {
      return <PluginSignatureInfo>[];
    }

    // 生成时间戳信息
    final timestamp = PluginTimestampInfo(
      tsaUrl: _timestampServers.first,
      timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      signature: Uint8List.fromList(<int>[1, 2, 3, 4, 5]),
      certificate: certificate,
      isValid: true,
    );

    // 尝试从缓存中获取原始签名属性
    final dataHash = crypto.sha256.convert(fileData).toString();
    final cachedAttributes = _signatureAttributesCache[dataHash];

    // 使用缓存的属性或默认属性
    final attributes = cachedAttributes ??
        const <String, dynamic>{
          'version': '1.0',
          'tool': 'plugin-signer',
        };

    return <PluginSignatureInfo>[
      PluginSignatureInfo(
        algorithm: PluginSignatureAlgorithm.rsa2048,
        signature: signatureData,
        signedAt: DateTime.now().subtract(const Duration(minutes: 5)),
        certificate: certificate,
        timestamp: timestamp,
        attributes: attributes,
      ),
    ];
  }

  /// 验证单个签名
  Future<void> _verifyIndividualSignature(
    PluginSignatureInfo signature,
    Uint8List fileData,
    List<String> errors,
    List<String> warnings,
  ) async {
    // 验证证书
    final isCertValid =
        await _certificateManager.verifyCertificateChain(signature.certificate);
    if (!isCertValid) {
      errors.add('Certificate verification failed');
    }

    // 检查证书撤销状态
    final isNotRevoked = await _certificateManager
        .checkCertificateRevocation(signature.certificate);
    if (!isNotRevoked) {
      errors.add('Certificate has been revoked');
    }

    // 验证时间戳
    if (signature.hasTimestamp) {
      final timestampValid = await verifyTimestamp(signature.timestamp!);
      if (!timestampValid) {
        warnings.add('Timestamp verification failed');
      }
    }

    // 验证签名值
    final provider = _providers[signature.algorithm];
    if (provider != null) {
      final originalData = _extractOriginalData(fileData);
      final isSignatureValid = await provider.verifySignature(
        signature.signature,
        originalData,
        null,
      );

      if (!isSignatureValid) {
        errors.add('Plugin signature verification failed');
      }
    } else {
      errors.add('Unsupported signature algorithm: ${signature.algorithm}');
    }
  }

  /// 从签名数据中提取原始数据
  Uint8List _extractOriginalData(Uint8List signedData) {
    // 查找签名标记
    const signatureMarker = 'PLUGIN_SIGNATURE';
    final markerBytes = signatureMarker.codeUnits;

    // 在数据中查找标记位置
    for (int i = 0; i <= signedData.length - markerBytes.length; i++) {
      bool found = true;
      for (int j = 0; j < markerBytes.length; j++) {
        if (signedData[i + j] != markerBytes[j]) {
          found = false;
          break;
        }
      }
      if (found) {
        // 找到标记，返回标记之前的数据
        return signedData.sublist(0, i);
      }
    }

    // 没有找到标记，返回原始数据
    return signedData;
  }

  /// 清理缓存
  void clearCache() {
    _signatureAttributesCache.clear();
    _certificateManager.clearCache();

    for (final provider in _providers.values) {
      if (provider is RSASignatureProvider) {
        provider.clearCache();
      } else if (provider is ECDSASignatureProvider) {
        provider.clearCache();
      }
    }
  }

  /// 获取支持的算法列表
  List<PluginSignatureAlgorithm> getSupportedAlgorithms() {
    return _providers.keys.toList();
  }

  /// 获取服务统计信息
  Map<String, dynamic> getServiceStatistics() {
    return <String, dynamic>{
      'policy': _policy.toString(),
      'supportedAlgorithms':
          getSupportedAlgorithms().map((a) => a.toString()).toList(),
      'timestampServers': _timestampServers,
      'signatureCache': _signatureAttributesCache.length,
      'certificateStats': _certificateManager.getCertificateStatistics(),
    };
  }
}
