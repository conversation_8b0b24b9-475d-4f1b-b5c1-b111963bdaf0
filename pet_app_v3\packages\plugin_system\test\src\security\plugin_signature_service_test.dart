/*
---------------------------------------------------------------
File name:          plugin_signature_service_test.dart
Author:             lgnorant-lu
Date created:       2025-07-27
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件数字签名服务测试
---------------------------------------------------------------
*/

import 'dart:typed_data';

import 'package:flutter_test/flutter_test.dart';
import 'package:plugin_system/src/security/plugin_signature_service.dart';
import 'package:plugin_system/src/security/signature/plugin_signature_core.dart';

void main() {
  group('PluginSignatureService', () {
    late PluginSignatureServiceImpl service;

    setUp(() {
      service = PluginSignatureServiceImpl();
    });

    group('基础功能测试', () {
      test('应该创建服务实例', () {
        expect(service, isNotNull);
        expect(service.getSupportedAlgorithms(), isNotEmpty);
      });

      test('应该支持RSA和ECDSA算法', () {
        final algorithms = service.getSupportedAlgorithms();
        expect(algorithms, contains(PluginSignatureAlgorithm.rsa2048));
        expect(algorithms, contains(PluginSignatureAlgorithm.ecdsaP256));
      });

      test('应该获取服务统计信息', () {
        final stats = service.getServiceStatistics();
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats['supportedAlgorithms'], isNotEmpty);
      });
    });

    group('签名生成测试', () {
      test('应该成功生成RSA签名', () async {
        final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        final signedData = await service.signPlugin(
          testData,
          algorithm: PluginSignatureAlgorithm.rsa2048,
          attributes: <String, dynamic>{
            'test': 'value',
            'version': '1.0.0',
          },
        );

        expect(signedData.length, greaterThan(testData.length));
        expect(signedData, isNot(equals(testData)));
      });

      test('应该成功生成ECDSA签名', () async {
        final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        final signedData = await service.signPlugin(
          testData,
          algorithm: PluginSignatureAlgorithm.ecdsaP256,
        );

        expect(signedData.length, greaterThan(testData.length));
      });

      test('应该处理签名失败', () async {
        final testData = Uint8List.fromList([]);
        
        expect(
          () => service.signPlugin(testData),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('签名验证测试', () {
      test('应该验证有效的RSA签名', () async {
        final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        // 先生成签名
        final signedData = await service.signPlugin(
          testData,
          algorithm: PluginSignatureAlgorithm.rsa2048,
        );
        
        // 然后验证签名
        final result = await service.verifyPluginSignature(
          'test.signed',
          signedData,
        );

        expect(result.isValid, isTrue);
        expect(result.signatures, isNotEmpty);
        expect(result.errors, isEmpty);
      });

      test('应该验证有效的ECDSA签名', () async {
        final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        // 先生成签名
        final signedData = await service.signPlugin(
          testData,
          algorithm: PluginSignatureAlgorithm.ecdsaP256,
        );
        
        // 然后验证签名
        final result = await service.verifyPluginSignature(
          'test.signed',
          signedData,
        );

        expect(result.isValid, isTrue);
        expect(result.signatures, isNotEmpty);
        expect(result.errors, isEmpty);
      });

      test('应该处理禁用策略', () async {
        final disabledService = PluginSignatureServiceImpl(
          policy: PluginSignaturePolicy.disabled,
        );
        
        final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        final result = await disabledService.verifyPluginSignature(
          'test.plugin',
          testData,
        );

        expect(result.isValid, isTrue);
        expect(result.signatures, isEmpty);
        expect(result.warnings, contains('Plugin signature verification is disabled'));
      });

      test('应该拒绝无签名的文件（必需策略）', () async {
        final requiredService = PluginSignatureServiceImpl(
          policy: PluginSignaturePolicy.required,
        );
        
        final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
        
        final result = await requiredService.verifyPluginSignature(
          'test.plugin',
          testData,
        );

        expect(result.isValid, isFalse);
        expect(result.signatures, isEmpty);
        expect(result.errors, isNotEmpty);
        expect(result.errors.first, contains('No digital signature found'));
      });
    });

    group('证书管理测试', () {
      test('应该获取证书信息', () async {
        final certInfo = await service.getCertificateInfo('test.cert');
        
        expect(certInfo, isNotNull);
        expect(certInfo!.subject, isNotEmpty);
        expect(certInfo.issuer, isNotEmpty);
        expect(certInfo.status, equals(PluginCertificateStatus.valid));
      });
    });

    group('时间戳验证测试', () {
      test('应该验证有效时间戳', () async {
        final certificate = PluginCertificateInfo(
          subject: 'CN=Test',
          issuer: 'CN=Test CA',
          serialNumber: '123',
          notBefore: DateTime.now().subtract(const Duration(days: 1)),
          notAfter: DateTime.now().add(const Duration(days: 1)),
          fingerprint: 'test',
          status: PluginCertificateStatus.valid,
          keyUsage: const ['Digital Signature'],
          extendedKeyUsage: const ['Time Stamping'],
        );

        final timestamp = PluginTimestampInfo(
          tsaUrl: 'http://timestamp.digicert.com',
          timestamp: DateTime.now(),
          signature: Uint8List.fromList([1, 2, 3, 4, 5]),
          certificate: certificate,
          isValid: true,
        );

        final result = await service.verifyTimestamp(timestamp);
        expect(result, isTrue);
      });

      test('应该拒绝无效时间戳服务器', () async {
        final certificate = PluginCertificateInfo(
          subject: 'CN=Test',
          issuer: 'CN=Test CA',
          serialNumber: '123',
          notBefore: DateTime.now().subtract(const Duration(days: 1)),
          notAfter: DateTime.now().add(const Duration(days: 1)),
          fingerprint: 'test',
          status: PluginCertificateStatus.valid,
          keyUsage: const ['Digital Signature'],
          extendedKeyUsage: const ['Time Stamping'],
        );

        final timestamp = PluginTimestampInfo(
          tsaUrl: 'http://invalid-tsa.com',
          timestamp: DateTime.now(),
          signature: Uint8List.fromList([1, 2, 3, 4, 5]),
          certificate: certificate,
          isValid: true,
        );

        final result = await service.verifyTimestamp(timestamp);
        expect(result, isFalse);
      });
    });

    group('缓存管理测试', () {
      test('应该能够清理缓存', () {
        expect(() => service.clearCache(), returnsNormally);
      });
    });

    group('性能测试', () {
      test('签名生成应该在合理时间内完成', () async {
        final testData = Uint8List.fromList(List.generate(1000, (i) => i % 256));
        
        final stopwatch = Stopwatch()..start();
        await service.signPlugin(testData);
        stopwatch.stop();

        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5秒内完成
      });

      test('签名验证应该在合理时间内完成', () async {
        final testData = Uint8List.fromList(List.generate(1000, (i) => i % 256));
        final signedData = await service.signPlugin(testData);
        
        final stopwatch = Stopwatch()..start();
        await service.verifyPluginSignature('test.signed', signedData);
        stopwatch.stop();

        expect(stopwatch.elapsedMilliseconds, lessThan(3000)); // 3秒内完成
      });
    });
  });
}
