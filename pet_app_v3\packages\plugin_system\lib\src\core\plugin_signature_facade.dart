/*
---------------------------------------------------------------
File name:          plugin_signature_facade.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        插件数字签名系统门面接口 - 遵循单一职责原则
---------------------------------------------------------------
Change History:
    2025-07-28:     初始实现插件数字签名系统门面;
*/

import 'dart:typed_data';

import 'package:plugin_system/src/security/plugin_signature_service.dart';
import 'package:plugin_system/src/security/signature/plugin_signature_core.dart';

/// 插件数字签名系统门面
/// 
/// 这是一个门面模式的实现，将复杂的签名系统简化为简单的接口。
/// 内部委托给专门的服务类来处理具体的签名操作。
class PluginSignature {
  /// 构造函数
  PluginSignature({
    PluginSignaturePolicy policy = PluginSignaturePolicy.optional,
  }) : _service = PluginSignatureServiceImpl(policy: policy);

  /// 单例实例
  static PluginSignature? _instance;

  /// 获取单例实例
  static PluginSignature get instance {
    _instance ??= PluginSignature();
    return _instance!;
  }

  /// 内部签名服务
  final PluginSignatureServiceImpl _service;

  /// 签名插件文件
  /// 
  /// [pluginData] 插件数据
  /// [certificatePath] 证书路径
  /// [privateKeyPath] 私钥路径
  /// [algorithm] 签名算法
  /// [attributes] 签名属性
  Future<Uint8List> signPlugin(
    Uint8List pluginData, {
    String? certificatePath,
    String? privateKeyPath,
    PluginSignatureAlgorithm algorithm = PluginSignatureAlgorithm.rsa2048,
    Map<String, dynamic> attributes = const <String, dynamic>{},
  }) async {
    return await _service.signPlugin(
      pluginData,
      certificatePath: certificatePath,
      privateKeyPath: privateKeyPath,
      algorithm: algorithm,
      attributes: attributes,
    );
  }

  /// 验证插件文件签名
  /// 
  /// [filePath] 文件路径
  /// [fileData] 文件数据
  Future<PluginSignatureVerificationResult> verifyPluginSignature(
    String filePath,
    Uint8List fileData,
  ) async {
    return await _service.verifyPluginSignature(filePath, fileData);
  }

  /// 获取证书信息
  /// 
  /// [certificatePath] 证书路径
  Future<PluginCertificateInfo?> getCertificateInfo(
    String certificatePath,
  ) async {
    return await _service.getCertificateInfo(certificatePath);
  }

  /// 验证时间戳
  /// 
  /// [timestamp] 时间戳信息
  Future<bool> verifyTimestamp(PluginTimestampInfo timestamp) async {
    return await _service.verifyTimestamp(timestamp);
  }

  /// 清理缓存
  void clearCache() {
    _service.clearCache();
  }

  /// 获取支持的算法列表
  List<PluginSignatureAlgorithm> getSupportedAlgorithms() {
    return _service.getSupportedAlgorithms();
  }

  /// 获取服务统计信息
  Map<String, dynamic> getServiceStatistics() {
    return _service.getServiceStatistics();
  }

  /// 检查是否支持的算法
  bool _isSupportedAlgorithm(PluginSignatureAlgorithm algorithm) {
    return getSupportedAlgorithms().contains(algorithm);
  }
}

// 重新导出核心类型，保持向后兼容
export '../security/signature/plugin_signature_core.dart';
