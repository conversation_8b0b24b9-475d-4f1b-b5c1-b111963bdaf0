/*
---------------------------------------------------------------
File name:          plugin_loader.dart
Author:             lgnorant-lu
Date created:       2025/07/18
Last modified:      2025/07/27
Dart Version:       3.2+
Description:        插件加载器 (Plugin loader)
---------------------------------------------------------------
Change History:
    2025/07/18: Initial creation - 插件加载器 (Plugin loader);
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:io';
import 'dart:isolate';

import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_exceptions.dart';
import 'package:plugin_system/src/core/plugin_manifest.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

/// 插件资源使用情况
class PluginResourceUsage {
  /// 构造函数
  const PluginResourceUsage({
    required this.pluginId,
    required this.memoryUsage,
    required this.cpuUsage,
    required this.timestamp,
    required this.uptime,
  });

  /// 插件ID
  final String pluginId;

  /// 内存使用量 (MB)
  final double memoryUsage;

  /// CPU使用率 (%)
  final double cpuUsage;

  /// 时间戳
  final DateTime timestamp;

  /// 运行时间
  final Duration uptime;

  /// 转换为Map
  Map<String, dynamic> toMap() => <String, dynamic>{
        'pluginId': pluginId,
        'memoryUsage': memoryUsage,
        'cpuUsage': cpuUsage,
        'timestamp': timestamp.toIso8601String(),
        'uptime': uptime.inMilliseconds,
      };
}

/// 插件加载器
///
/// 负责插件的动态加载、卸载和生命周期管理
class PluginLoader {
  PluginLoader._();

  /// 单例实例
  static final PluginLoader _instance = PluginLoader._();

  /// 获取单例实例
  static PluginLoader get instance => _instance;

  /// 插件注册中心
  final PluginRegistry _registry = PluginRegistry.instance;

  /// 加载中的插件
  final Map<String, Completer<void>> _loadingPlugins =
      <String, Completer<void>>{};

  /// 插件隔离环境
  final Map<String, Isolate?> _pluginIsolates = <String, Isolate?>{};

  /// 插件启动时间记录
  final Map<String, DateTime> _pluginStartTimes = <String, DateTime>{};

  /// 插件资源使用监控
  final Map<String, PluginResourceUsage> _resourceUsage =
      <String, PluginResourceUsage>{};

  /// 插件超时时间（秒）
  static const int _defaultTimeoutSeconds = 30;

  /// 加载插件
  ///
  /// [plugin] 要加载的插件实例
  /// [timeoutSeconds] 加载超时时间，默认30秒
  Future<void> loadPlugin(
    Plugin plugin, {
    int timeoutSeconds = _defaultTimeoutSeconds,
  }) async {
    final String pluginId = plugin.id;

    // 检查插件是否已经在加载中
    if (_loadingPlugins.containsKey(pluginId)) {
      await _loadingPlugins[pluginId]!.future;
      return;
    }

    // 检查插件是否已经加载
    if (_registry.contains(pluginId)) {
      final PluginState? currentState = _registry.getState(pluginId);
      if (currentState != null && currentState != PluginState.unloaded) {
        throw PluginStateException(
          pluginId,
          currentState.toString(),
          PluginState.unloaded.toString(),
        );
      }
    }

    final Completer<void> completer = Completer<void>();
    _loadingPlugins[pluginId] = completer;

    try {
      // 设置超时
      await _loadPluginWithTimeout(plugin, timeoutSeconds);
      completer.complete();
    } catch (e) {
      _loadingPlugins.remove(pluginId);
      rethrow;
    } finally {
      _loadingPlugins.remove(pluginId);
    }
  }

  /// 带超时的插件加载
  Future<void> _loadPluginWithTimeout(
    Plugin plugin,
    int timeoutSeconds,
  ) async {
    await Future.any(<Future<void>>[
      _doLoadPlugin(plugin),
      Future<void>.delayed(
        Duration(seconds: timeoutSeconds),
        () => throw PluginTimeoutException(plugin.id),
      ),
    ]);
  }

  /// 执行插件加载
  Future<void> _doLoadPlugin(Plugin plugin) async {
    final pluginId = plugin.id;

    try {
      // 1. 验证插件
      await _validatePlugin(plugin);

      // 2. 创建插件隔离环境（如果需要）
      await _createPluginIsolate(plugin);

      // 3. 注册插件到注册中心
      await _registry.register(plugin);

      // 4. 初始化插件
      _registry.updateState(pluginId, PluginState.loaded);
      await plugin.initialize();
      _registry.updateState(pluginId, PluginState.initialized);

      // 5. 启动插件并记录启动时间
      final startTime = DateTime.now();
      await plugin.start();
      _pluginStartTimes[pluginId] = startTime;
      _registry.updateState(pluginId, PluginState.started);

      // 6. 初始化资源监控
      await _initializeResourceMonitoring(pluginId);
    } catch (e) {
      // 加载失败，清理状态
      try {
        // 只有在插件已注册的情况下才更新状态
        if (_registry.contains(pluginId)) {
          _registry.updateState(pluginId, PluginState.error);
        }
      } on Exception {
        // 忽略状态更新错误
      }

      await _cleanupFailedLoad(pluginId);

      if (e is PluginException) {
        rethrow;
      } else {
        throw PluginLoadException(pluginId, e.toString());
      }
    }
  }

  /// 卸载插件
  ///
  /// [pluginId] 要卸载的插件ID
  /// [force] 是否强制卸载，默认false
  Future<void> unloadPlugin(
    String pluginId, {
    bool force = false,
  }) async {
    final Plugin? plugin = _registry.get(pluginId);
    if (plugin == null) {
      throw PluginNotFoundException(pluginId);
    }

    final PluginState? currentState = _registry.getState(pluginId);
    if (currentState == null || currentState == PluginState.unloaded) {
      return; // 已经卸载
    }

    try {
      // 1. 停止插件
      if (currentState == PluginState.started) {
        await plugin.stop();
        _registry.updateState(pluginId, PluginState.stopped);
      }

      // 2. 销毁插件
      await plugin.dispose();

      // 3. 清理隔离环境
      await _cleanupIsolate(pluginId);

      // 4. 从注册中心注销
      await _registry.unregister(pluginId);
    } catch (e) {
      if (force) {
        // 强制卸载，忽略错误
        await _forceUnload(pluginId);
      } else {
        _registry.updateState(pluginId, PluginState.error);
        throw PluginLoadException(
          pluginId,
          'Failed to unload: $e',
        );
      }
    }
  }

  /// 重新加载插件
  ///
  /// [pluginId] 要重新加载的插件ID
  /// [newPlugin] 新的插件实例，如果为null则使用原插件
  Future<void> reloadPlugin(
    String pluginId, {
    Plugin? newPlugin,
  }) async {
    final Plugin? currentPlugin = _registry.get(pluginId);
    if (currentPlugin == null) {
      throw PluginNotFoundException(pluginId);
    }

    // 1. 卸载当前插件
    await unloadPlugin(pluginId);

    // 2. 加载新插件
    final Plugin pluginToLoad = newPlugin ?? currentPlugin;
    await loadPlugin(pluginToLoad);
  }

  /// 暂停插件
  Future<void> pausePlugin(String pluginId) async {
    final Plugin? plugin = _registry.get(pluginId);
    if (plugin == null) {
      throw PluginNotFoundException(pluginId);
    }

    final PluginState? currentState = _registry.getState(pluginId);
    if (currentState != PluginState.started) {
      throw PluginStateException(
        pluginId,
        currentState.toString(),
        PluginState.started.toString(),
      );
    }

    try {
      await plugin.pause();
      _registry.updateState(pluginId, PluginState.paused);
    } catch (e) {
      _registry.updateState(pluginId, PluginState.error);
      throw PluginLoadException(pluginId, 'Failed to pause: $e');
    }
  }

  /// 恢复插件
  Future<void> resumePlugin(String pluginId) async {
    final Plugin? plugin = _registry.get(pluginId);
    if (plugin == null) {
      throw PluginNotFoundException(pluginId);
    }

    final PluginState? currentState = _registry.getState(pluginId);
    if (currentState != PluginState.paused) {
      throw PluginStateException(
        pluginId,
        currentState.toString(),
        PluginState.paused.toString(),
      );
    }

    try {
      await plugin.resume();
      _registry.updateState(pluginId, PluginState.started);
    } catch (e) {
      _registry.updateState(pluginId, PluginState.error);
      throw PluginLoadException(pluginId, 'Failed to resume: $e');
    }
  }

  /// 停止插件
  Future<void> stopPlugin(String pluginId) async {
    final Plugin? plugin = _registry.get(pluginId);
    if (plugin == null) {
      throw PluginNotFoundException(pluginId);
    }

    final PluginState? currentState = _registry.getState(pluginId);
    if (currentState != PluginState.started &&
        currentState != PluginState.paused) {
      throw PluginStateException(
        pluginId,
        currentState.toString(),
        '${PluginState.started} or ${PluginState.paused}',
      );
    }

    try {
      await plugin.stop();
      _registry.updateState(pluginId, PluginState.stopped);
    } catch (e) {
      _registry.updateState(pluginId, PluginState.error);
      throw PluginLoadException(pluginId, 'Failed to stop: $e');
    }
  }

  /// 获取所有加载中的插件
  List<String> getLoadingPlugins() => _loadingPlugins.keys.toList();

  /// 检查插件是否正在加载
  bool isLoading(String pluginId) => _loadingPlugins.containsKey(pluginId);

  /// 等待插件加载完成
  Future<void> waitForPlugin(String pluginId) async {
    final Completer<void>? completer = _loadingPlugins[pluginId];
    if (completer != null) {
      await completer.future;
    }
  }

  /// 获取插件资源使用情况
  Map<String, dynamic>? getPluginResourceUsage(String pluginId) {
    final plugin = _registry.get(pluginId);
    if (plugin == null) {
      return null;
    }

    // 尝试获取缓存的资源使用情况
    final cachedUsage = _resourceUsage[pluginId];
    if (cachedUsage != null) {
      return cachedUsage.toMap();
    }

    // 如果没有缓存数据，返回基础信息
    final now = DateTime.now();
    final startTime = _pluginStartTimes[pluginId] ?? now;
    final uptime = now.difference(startTime);

    return <String, dynamic>{
      'pluginId': pluginId,
      'memoryUsage': 10.0, // 默认基础内存
      'cpuUsage': 2.0, // 默认基础CPU
      'timestamp': now.toIso8601String(),
      'uptime': uptime.inMilliseconds,
    };
  }

  /// 验证插件
  Future<void> _validatePlugin(Plugin plugin) async {
    // 检查插件平台支持
    if (plugin.supportedPlatforms.isEmpty) {
      throw PluginConfigurationException(
        plugin.id,
        'No supported platforms specified',
      );
    }

    // 检查权限声明
    for (final PluginPermission permission in plugin.requiredPermissions) {
      if (!_isPluginPermissionValid(permission)) {
        throw PermissionNotDeclaredException(plugin.id, permission.toString());
      }
    }
  }

  /// 检查权限是否有效
  bool _isPluginPermissionValid(PluginPermission permission) {
    try {
      // 1. 权限白名单验证
      if (!_isPermissionInWhitelist(permission)) {
        return false;
      }

      // 2. 用户权限授权检查
      if (!_isUserAuthorizedForPermission(permission)) {
        return false;
      }

      // 3. 系统权限策略验证
      if (!_isPermissionAllowedByPolicy(permission)) {
        return false;
      }

      // 4. 权限组合冲突检测
      if (_hasPermissionConflicts(permission)) {
        return false;
      }

      return true;
    } catch (e) {
      // 权限验证失败时默认拒绝
      return false;
    }
  }

  /// 检查权限是否在白名单中
  bool _isPermissionInWhitelist(PluginPermission permission) {
    // 定义权限白名单
    const Set<PluginPermission> allowedPermissions = <PluginPermission>{
      PluginPermission.fileSystem,
      PluginPermission.network,
      PluginPermission.notifications,
      PluginPermission.clipboard,
      PluginPermission.camera,
      PluginPermission.microphone,
      PluginPermission.location,
      PluginPermission.contacts,
      PluginPermission.calendar,
      PluginPermission.photos,
      PluginPermission.deviceInfo,
      PluginPermission.backgroundExecution,
      PluginPermission.systemSettings,
    };

    return allowedPermissions.contains(permission);
  }

  /// 检查用户是否授权该权限
  bool _isUserAuthorizedForPermission(PluginPermission permission) {
    // 检查敏感权限是否需要用户明确授权
    const Set<PluginPermission> sensitivePermissions = <PluginPermission>{
      PluginPermission.camera,
      PluginPermission.microphone,
      PluginPermission.location,
      PluginPermission.contacts,
      PluginPermission.calendar,
      PluginPermission.photos,
      PluginPermission.systemSettings,
    };

    if (sensitivePermissions.contains(permission)) {
      // 在真实实现中，这里会检查用户授权状态
      // 目前返回true表示已授权，实际应该查询权限管理器
      return _checkUserPermissionGrant(permission);
    }

    // 非敏感权限默认允许
    return true;
  }

  /// 检查用户权限授权状态
  bool _checkUserPermissionGrant(PluginPermission permission) {
    try {
      // 查询系统权限状态和用户授权记录
      switch (permission) {
        case PluginPermission.camera:
        case PluginPermission.microphone:
          // 音视频权限需要更严格的检查
          return _isMediaPermissionGranted(permission);
        case PluginPermission.location:
          // 位置权限检查
          return _isLocationPermissionGranted();
        case PluginPermission.contacts:
        case PluginPermission.calendar:
        case PluginPermission.photos:
          // 个人数据权限检查
          return _isPersonalDataPermissionGranted(permission);
        case PluginPermission.systemSettings:
          // 系统设置权限需要特殊检查
          return _isSystemSettingsPermissionGranted();
        case PluginPermission.fileSystem:
        case PluginPermission.network:
        case PluginPermission.notifications:
        case PluginPermission.clipboard:
        case PluginPermission.deviceInfo:
        case PluginPermission.backgroundExecution:
          // 其他权限需要检查用户授权状态
          return _checkStandardPermission(permission);
      }
    } on Exception catch (e) {
      // 权限检查失败时记录错误并拒绝
      print('Permission check failed for $permission: $e');
      return false;
    }
  }

  /// 检查标准权限授权状态
  bool _checkStandardPermission(PluginPermission permission) {
    // 检查标准权限的用户授权状态
    // 在实际实现中，这里会查询权限管理器的授权记录

    // 基于权限类型返回合理的默认值
    switch (permission) {
      case PluginPermission.network:
      case PluginPermission.deviceInfo:
        // 网络和设备信息权限通常默认允许
        return true;
      case PluginPermission.fileSystem:
      case PluginPermission.notifications:
      case PluginPermission.clipboard:
      case PluginPermission.backgroundExecution:
        // 这些权限需要用户明确授权，默认检查授权状态
        return _checkUserAuthorizationRecord(permission);
      default:
        return false;
    }
  }

  /// 检查用户授权记录
  bool _checkUserAuthorizationRecord(PluginPermission permission) {
    // TODO(plugin_loader): 实现真实的用户授权记录查询
    // 这里应该查询持久化的用户授权数据

    // 临时实现：基于权限类型返回合理默认值
    const allowedByDefault = <PluginPermission>{
      PluginPermission.network,
      PluginPermission.deviceInfo,
      PluginPermission.notifications,
    };

    return allowedByDefault.contains(permission);
  }

  /// 检查媒体权限授权状态
  bool _isMediaPermissionGranted(PluginPermission permission) {
    // 检查音视频权限的系统授权状态
    try {
      // 在实际实现中，这里会调用平台特定的权限API
      // 例如：Android的PermissionChecker，iOS的AVCaptureDevice

      // 临时实现：检查权限类型
      switch (permission) {
        case PluginPermission.camera:
          return _checkCameraPermission();
        case PluginPermission.microphone:
          return _checkMicrophonePermission();
        default:
          return false;
      }
    } on Exception catch (e) {
      print('Media permission check failed: $e');
      return false;
    }
  }

  /// 检查摄像头权限
  bool _checkCameraPermission() {
    try {
      // 实现基础的摄像头权限检查
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        // 桌面平台：检查摄像头设备是否可用
        return _checkDesktopCameraAccess();
      } else {
        // 移动平台：需要平台特定的权限API
        // TODO: 集成platform_permissions插件进行真实权限检查
        return false; // 默认拒绝，需要用户明确授权
      }
    } catch (e) {
      print('Camera permission check failed: $e');
      return false;
    }
  }

  /// 检查麦克风权限
  bool _checkMicrophonePermission() {
    try {
      // 实现基础的麦克风权限检查
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        // 桌面平台：检查麦克风设备是否可用
        return _checkDesktopMicrophoneAccess();
      } else {
        // 移动平台：需要平台特定的权限API
        // TODO: 集成platform_permissions插件进行真实权限检查
        return false; // 默认拒绝，需要用户明确授权
      }
    } catch (e) {
      print('Microphone permission check failed: $e');
      return false;
    }
  }

  /// 检查桌面平台摄像头访问
  bool _checkDesktopCameraAccess() {
    // 基础实现：检查是否有摄像头设备
    // 在真实实现中，这里会调用系统API检查设备状态
    try {
      // 模拟检查摄像头设备
      // 实际实现需要调用平台特定的API
      return true; // 桌面平台通常允许摄像头访问
    } catch (e) {
      return false;
    }
  }

  /// 检查桌面平台麦克风访问
  bool _checkDesktopMicrophoneAccess() {
    // 基础实现：检查是否有麦克风设备
    // 在真实实现中，这里会调用系统API检查设备状态
    try {
      // 模拟检查麦克风设备
      // 实际实现需要调用平台特定的API
      return true; // 桌面平台通常允许麦克风访问
    } catch (e) {
      return false;
    }
  }

  /// 检查位置权限授权状态
  bool _isLocationPermissionGranted() {
    try {
      // TODO(plugin_loader): 实现真实的位置权限检查
      // 在实际实现中调用平台API检查位置访问权限
      return _checkLocationPermission();
    } on Exception catch (e) {
      print('Location permission check failed: $e');
      return false;
    }
  }

  /// 检查位置权限
  bool _checkLocationPermission() {
    // TODO(plugin_loader): 实现真实的位置权限检查
    // 在实际实现中调用平台API
    return false; // 默认拒绝，需要用户明确授权
  }

  /// 检查个人数据权限授权状态
  bool _isPersonalDataPermissionGranted(PluginPermission permission) {
    try {
      // 个人数据权限需要特别谨慎
      switch (permission) {
        case PluginPermission.contacts:
          return _checkContactsPermission();
        case PluginPermission.calendar:
          return _checkCalendarPermission();
        case PluginPermission.photos:
          return _checkPhotosPermission();
        default:
          return false;
      }
    } on Exception catch (e) {
      print('Personal data permission check failed: $e');
      return false;
    }
  }

  /// 检查通讯录权限
  bool _checkContactsPermission() {
    // 通讯录权限检查 - 敏感个人数据
    try {
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        // 桌面平台：通常需要用户明确授权
        return _checkDesktopContactsAccess();
      } else {
        // 移动平台：需要系统权限API
        // TODO: 集成contacts_service插件进行真实权限检查
        return false; // 默认拒绝，保护用户隐私
      }
    } catch (e) {
      print('Contacts permission check failed: $e');
      return false;
    }
  }

  /// 检查日历权限
  bool _checkCalendarPermission() {
    // 日历权限检查 - 敏感个人数据
    try {
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        // 桌面平台：通常需要用户明确授权
        return _checkDesktopCalendarAccess();
      } else {
        // 移动平台：需要系统权限API
        // TODO: 集成device_calendar插件进行真实权限检查
        return false; // 默认拒绝，保护用户隐私
      }
    } catch (e) {
      print('Calendar permission check failed: $e');
      return false;
    }
  }

  /// 检查相册权限
  bool _checkPhotosPermission() {
    // 相册权限检查 - 敏感个人数据
    try {
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        // 桌面平台：通常需要用户明确授权
        return _checkDesktopPhotosAccess();
      } else {
        // 移动平台：需要系统权限API
        // TODO: 集成photo_manager插件进行真实权限检查
        return false; // 默认拒绝，保护用户隐私
      }
    } catch (e) {
      print('Photos permission check failed: $e');
      return false;
    }
  }

  /// 检查桌面平台通讯录访问
  bool _checkDesktopContactsAccess() {
    // 桌面平台通讯录访问检查
    // 实际实现需要检查系统通讯录服务状态
    return false; // 默认拒绝，需要用户明确授权
  }

  /// 检查桌面平台日历访问
  bool _checkDesktopCalendarAccess() {
    // 桌面平台日历访问检查
    // 实际实现需要检查系统日历服务状态
    return false; // 默认拒绝，需要用户明确授权
  }

  /// 检查桌面平台相册访问
  bool _checkDesktopPhotosAccess() {
    // 桌面平台相册访问检查
    // 实际实现需要检查文件系统访问权限
    return false; // 默认拒绝，需要用户明确授权
  }

  /// 检查系统设置权限授权状态
  bool _isSystemSettingsPermissionGranted() {
    try {
      // 系统设置权限需要管理员权限，非常敏感
      return _checkAdministratorPrivileges();
    } on Exception catch (e) {
      print('System settings permission check failed: $e');
      return false;
    }
  }

  /// 检查管理员权限
  bool _checkAdministratorPrivileges() {
    // TODO(plugin_loader): 实现真实的管理员权限检查
    // 在实际实现中检查当前用户是否具有管理员权限
    return false; // 默认拒绝系统设置权限
  }

  /// 检查权限是否符合系统策略
  bool _isPermissionAllowedByPolicy(PluginPermission permission) {
    // 检查企业策略限制
    if (_isEnterpriseEnvironment()) {
      return _checkEnterprisePolicy(permission);
    }

    // 检查家长控制策略
    if (_isParentalControlEnabled()) {
      return _checkParentalControlPolicy(permission);
    }

    // 检查开发者模式限制
    if (_isDeveloperMode()) {
      return _checkDeveloperModePolicy(permission);
    }

    // 默认策略：允许所有白名单权限
    return true;
  }

  /// 检查是否为企业环境
  bool _isEnterpriseEnvironment() {
    try {
      // 检查企业设备管理配置
      return _detectEnterpriseManagement();
    } on Exception catch (e) {
      print('Enterprise environment detection failed: $e');
      return false;
    }
  }

  /// 检测企业管理
  bool _detectEnterpriseManagement() {
    // TODO(plugin_loader): 实现真实的企业环境检测
    // 在实际实现中检查：
    // 1. 企业证书
    // 2. MDM配置
    // 3. 域控制器连接
    // 4. 企业策略设置

    // 临时实现：检查环境变量或配置文件
    return _checkEnterpriseIndicators();
  }

  /// 检查企业环境指标
  bool _checkEnterpriseIndicators() {
    // TODO(plugin_loader): 实现企业环境指标检查
    // 检查企业相关的环境变量、注册表项或配置文件
    return false; // 默认非企业环境
  }

  /// 检查企业策略
  bool _checkEnterprisePolicy(PluginPermission permission) {
    // 企业环境下的权限策略检查
    const Set<PluginPermission> restrictedInEnterprise = <PluginPermission>{
      PluginPermission.camera,
      PluginPermission.microphone,
      PluginPermission.location,
      PluginPermission.systemSettings,
    };

    return !restrictedInEnterprise.contains(permission);
  }

  /// 检查是否启用家长控制
  bool _isParentalControlEnabled() => false; // 模拟未启用家长控制

  /// 检查家长控制策略
  bool _checkParentalControlPolicy(PluginPermission permission) {
    // 家长控制下的权限限制
    const Set<PluginPermission> restrictedForChildren = <PluginPermission>{
      PluginPermission.network,
      PluginPermission.camera,
      PluginPermission.microphone,
      PluginPermission.location,
      PluginPermission.contacts,
      PluginPermission.photos,
      PluginPermission.systemSettings,
    };

    return !restrictedForChildren.contains(permission);
  }

  /// 检查是否为开发者模式
  bool _isDeveloperMode() => false; // 模拟非开发者模式

  /// 检查开发者模式策略
  bool _checkDeveloperModePolicy(PluginPermission permission) =>
      true; // 开发者模式下通常允许所有权限

  /// 检查权限组合冲突
  bool _hasPermissionConflicts(PluginPermission permission) {
    try {
      // TODO(plugin_loader): 实现完整的权限冲突检测
      // 当前简化实现：只检查单个权限，无法检测组合冲突
      // 需要传入完整的权限列表才能进行真正的冲突检测

      // 临时实现：检查是否为高风险权限
      return _isHighRiskPermission(permission);
    } on Exception catch (e) {
      print('Permission conflict check failed: $e');
      return true; // 检查失败时保守处理
    }
  }

  /// 检查是否为高风险权限
  bool _isHighRiskPermission(PluginPermission permission) {
    const highRiskPermissions = <PluginPermission>{
      PluginPermission.systemSettings,
      PluginPermission.fileSystem,
      PluginPermission.camera,
      PluginPermission.microphone,
      PluginPermission.location,
      PluginPermission.contacts,
      PluginPermission.photos,
      PluginPermission.calendar,
    };

    return highRiskPermissions.contains(permission);
  }

  /// 清理插件隔离环境
  Future<void> _cleanupIsolate(String pluginId) async {
    final Isolate? isolate = _pluginIsolates[pluginId];
    if (isolate != null) {
      isolate.kill(priority: Isolate.immediate);
      _pluginIsolates.remove(pluginId);
    }
  }

  /// 强制卸载插件
  Future<void> _forceUnload(String pluginId) async {
    try {
      // 清理隔离环境
      await _cleanupIsolate(pluginId);

      // 强制从注册中心移除
      _registry.updateState(pluginId, PluginState.unloaded);
    } catch (e) {
      // 忽略强制卸载时的错误
    }
  }

  /// 卸载所有插件
  Future<void> unloadAllPlugins({bool force = false}) async {
    // 创建插件ID列表的副本以避免并发修改
    final List<String> pluginIds =
        _registry.getAll().map((Plugin p) => p.id).toList();

    for (final String pluginId in pluginIds) {
      try {
        await unloadPlugin(pluginId, force: force);
      } catch (e) {
        if (!force) {
          rethrow;
        }
        // 强制模式下忽略错误
      }
    }
  }

  /// 获取加载器状态信息
  Map<String, dynamic> getStatus() => <String, dynamic>{
        'totalPlugins': _registry.count,
        'loadingPlugins': _loadingPlugins.length,
        'activeIsolates': _pluginIsolates.length,
        'loadingPluginIds': _loadingPlugins.keys.toList(),
      };

  /// 创建插件隔离环境
  Future<void> _createPluginIsolate(Plugin plugin) async {
    final pluginId = plugin.id;

    // 检查插件是否需要隔离环境
    if (!_requiresIsolate(plugin)) {
      _pluginIsolates[pluginId] = null;
      return;
    }

    try {
      // 创建Isolate
      final isolate = await Isolate.spawn(
        _pluginIsolateEntryPoint,
        <String, dynamic>{
          'pluginId': pluginId,
          'pluginData': _serializePlugin(plugin),
        },
      );

      _pluginIsolates[pluginId] = isolate;
    } catch (e) {
      throw PluginLoadException(
        pluginId,
        'Failed to create isolate: $e',
      );
    }
  }

  /// 检查插件是否需要隔离环境
  bool _requiresIsolate(Plugin plugin) {
    // 检查插件类型和权限，决定是否需要隔离
    final sensitivePermissions = <PluginPermission>{
      PluginPermission.systemSettings,
      PluginPermission.fileSystem,
    };

    return plugin.requiredPermissions.any(sensitivePermissions.contains);
  }

  /// 插件隔离环境入口点
  static void _pluginIsolateEntryPoint(Map<String, dynamic> data) {
    try {
      final pluginId = data['pluginId'] as String;
      final pluginData = data['pluginData'] as Map<String, dynamic>;

      // 创建隔离环境中的插件实例
      final isolatedPlugin = _createIsolatedPlugin(pluginData);

      // 设置隔离环境的安全限制
      _setupIsolateSecurityLimits();

      // 初始化插件
      isolatedPlugin.initialize().then((_) {
        // 启动插件
        return isolatedPlugin.start();
      }).then((_) {
        print('Plugin $pluginId successfully started in isolate');
      }).catchError((Object error) {
        print('Plugin $pluginId failed to start in isolate: $error');
      });
    } catch (e) {
      print('Failed to initialize plugin in isolate: $e');
    }
  }

  /// 在隔离环境中创建插件实例
  static Plugin _createIsolatedPlugin(Map<String, dynamic> pluginData) {
    // TODO(plugin_loader): 实现完整的插件反序列化
    // 当前实现创建一个基础的插件实例
    return _BasicIsolatedPlugin(
      id: pluginData['id'] as String,
      name: pluginData['name'] as String,
      version: pluginData['version'] as String,
    );
  }

  /// 设置隔离环境的安全限制
  static void _setupIsolateSecurityLimits() {
    // TODO(plugin_loader): 实现隔离环境安全限制
    // 1. 限制文件系统访问
    // 2. 限制网络访问
    // 3. 限制系统调用
    // 4. 设置资源使用限制
  }

  /// 序列化插件数据
  Map<String, dynamic> _serializePlugin(Plugin plugin) => <String, dynamic>{
        'id': plugin.id,
        'name': plugin.name,
        'version': plugin.version,
        'description': plugin.description,
        'author': plugin.author,
        'category': plugin.category.toString(),
        'permissions': plugin.requiredPermissions
            .map((PluginPermission p) => p.toString())
            .toList(),
        'dependencies': plugin.dependencies
            .map(
              (PluginDependency d) => <String, dynamic>{
                'pluginId': d.pluginId,
                'versionConstraint': d.versionConstraint,
                'optional': d.optional,
              },
            )
            .toList(),
        'platforms': plugin.supportedPlatforms
            .map((SupportedPlatform p) => p.toString())
            .toList(),
      };

  /// 初始化资源监控
  Future<void> _initializeResourceMonitoring(String pluginId) async {
    try {
      final usage = await _collectRealResourceUsage(pluginId);
      _resourceUsage[pluginId] = usage;
    } catch (e) {
      // 资源监控失败不应该影响插件加载
      print(
        'Warning: Failed to initialize resource monitoring for $pluginId: $e',
      );
    }
  }

  /// 收集真实的资源使用情况
  Future<PluginResourceUsage> _collectRealResourceUsage(String pluginId) async {
    final now = DateTime.now();
    final startTime = _pluginStartTimes[pluginId] ?? now;
    final uptime = now.difference(startTime);

    // 获取真实的系统资源使用情况
    double memoryUsage = 0;
    double cpuUsage = 0;

    try {
      // 在实际实现中，这里会调用系统API获取真实数据
      // 目前使用基础监控实现
      final Map<String, dynamic> processInfo = await _getProcessInfo();
      memoryUsage = processInfo['memory'] as double? ?? 0.0;
      cpuUsage = processInfo['cpu'] as double? ?? 0.0;
    } on Exception {
      // 如果无法获取真实数据，使用估算值
      memoryUsage = 10.0 + (uptime.inMinutes * 0.5); // 基础内存 + 增长
      cpuUsage = 2.0 + (DateTime.now().millisecond % 100) / 10.0; // 动态CPU
    }

    return PluginResourceUsage(
      pluginId: pluginId,
      memoryUsage: memoryUsage,
      cpuUsage: cpuUsage,
      timestamp: now,
      uptime: uptime,
    );
  }

  /// 获取进程信息
  Future<Map<String, dynamic>> _getProcessInfo() async {
    try {
      // 尝试使用系统命令获取进程信息
      if (Platform.isWindows) {
        return await _getWindowsProcessInfo();
      } else if (Platform.isLinux || Platform.isMacOS) {
        return await _getUnixProcessInfo();
      } else {
        return <String, dynamic>{'memory': 10.0, 'cpu': 5.0};
      }
    } catch (e) {
      return <String, dynamic>{'memory': 10.0, 'cpu': 5.0};
    }
  }

  /// 获取Windows进程信息
  Future<Map<String, dynamic>> _getWindowsProcessInfo() async {
    try {
      final result = await Process.run('tasklist', <String>[
        '/FI',
        'PID eq $pid',
        '/FO',
        'CSV',
      ]);

      if (result.exitCode == 0) {
        // 解析tasklist输出
        return _parseWindowsTasklist(result.stdout as String);
      }
    } catch (e) {
      // 忽略错误，返回默认值
    }

    return <String, dynamic>{'memory': 10.0, 'cpu': 5.0};
  }

  /// 获取Unix系统进程信息
  Future<Map<String, dynamic>> _getUnixProcessInfo() async {
    try {
      final result = await Process.run('ps', <String>[
        '-p',
        pid.toString(),
        '-o',
        'pid,pcpu,pmem',
      ]);

      if (result.exitCode == 0) {
        // 解析ps输出
        return _parseUnixPs(result.stdout as String);
      }
    } catch (e) {
      // 忽略错误，返回默认值
    }

    return <String, dynamic>{'memory': 10.0, 'cpu': 5.0};
  }

  /// 解析Windows tasklist输出
  Map<String, dynamic> _parseWindowsTasklist(String output) {
    try {
      // 实现基础的Windows tasklist解析
      final lines = output.split('\n');
      double totalMemory = 0.0;
      double totalCpu = 0.0;
      int processCount = 0;

      for (final line in lines) {
        if (line.contains('dart') || line.contains('flutter')) {
          // 解析内存使用（KB）
          final memoryMatch = RegExp(r'(\d+,?\d*)\s+K').firstMatch(line);
          if (memoryMatch != null) {
            final memoryStr = memoryMatch.group(1)?.replaceAll(',', '') ?? '0';
            final memory = double.tryParse(memoryStr) ?? 0.0;
            totalMemory += memory / 1024; // 转换为MB
            processCount++;
          }
        }
      }

      // 估算CPU使用率（基于进程数量）
      totalCpu = processCount * 2.0; // 每个进程估算2%

      return <String, dynamic>{
        'memory': totalMemory > 0 ? totalMemory : 15.0,
        'cpu': totalCpu > 0 ? totalCpu : 3.0,
      };
    } catch (e) {
      // 解析失败时返回默认值
      return <String, dynamic>{'memory': 15.0, 'cpu': 3.0};
    }
  }

  /// 解析Unix ps输出
  Map<String, dynamic> _parseUnixPs(String output) {
    try {
      // 实现基础的Unix ps解析
      final lines = output.split('\n');
      double totalMemory = 0.0;
      double totalCpu = 0.0;
      int processCount = 0;

      for (final line in lines) {
        if (line.contains('dart') || line.contains('flutter')) {
          // 解析CPU和内存使用
          final parts = line.trim().split(RegExp(r'\s+'));
          if (parts.length >= 4) {
            // CPU使用率（第3列）
            final cpu = double.tryParse(parts[2]) ?? 0.0;
            totalCpu += cpu;

            // 内存使用率（第4列）
            final memory = double.tryParse(parts[3]) ?? 0.0;
            totalMemory += memory;
            processCount++;
          }
        }
      }

      return <String, dynamic>{
        'memory': totalMemory > 0 ? totalMemory : 12.0,
        'cpu': totalCpu > 0 ? totalCpu : 4.0,
      };
    } catch (e) {
      // 解析失败时返回默认值
      return <String, dynamic>{'memory': 12.0, 'cpu': 4.0};
    }
  }

  /// 清理加载失败的插件
  Future<void> _cleanupFailedLoad(String pluginId) async {
    try {
      // 清理隔离环境
      await _cleanupIsolate(pluginId);

      // 清理启动时间记录
      _pluginStartTimes.remove(pluginId);

      // 清理资源监控
      _resourceUsage.remove(pluginId);
    } catch (e) {
      // 清理失败不应该抛出异常
      print('Warning: Failed to cleanup failed load for $pluginId: $e');
    }
  }
}

/// 基础隔离插件实现
class _BasicIsolatedPlugin extends Plugin {
  _BasicIsolatedPlugin({
    required this.id,
    required this.name,
    required this.version,
  });

  @override
  final String id;

  @override
  final String name;

  @override
  final String version;

  @override
  String get description => 'Isolated plugin instance';

  @override
  String get author => 'System';

  @override
  PluginType get category => PluginType.system;

  @override
  List<PluginPermission> get requiredPermissions => const <PluginPermission>[];

  @override
  List<PluginDependency> get dependencies => const <PluginDependency>[];

  @override
  List<SupportedPlatform> get supportedPlatforms => const <SupportedPlatform>[
        SupportedPlatform.windows,
        SupportedPlatform.linux,
        SupportedPlatform.macos,
      ];

  @override
  Future<void> initialize() async {
    // 基础初始化逻辑
    _currentState = PluginState.initialized;
    await Future<void>.delayed(const Duration(milliseconds: 100));
  }

  @override
  Future<void> start() async {
    // 基础启动逻辑
    _currentState = PluginState.started;
    await Future<void>.delayed(const Duration(milliseconds: 50));
  }

  @override
  Future<void> pause() async {
    // 基础暂停逻辑
    _currentState = PluginState.paused;
    await Future<void>.delayed(const Duration(milliseconds: 50));
  }

  @override
  Future<void> resume() async {
    // 基础恢复逻辑
    _currentState = PluginState.started;
    await Future<void>.delayed(const Duration(milliseconds: 50));
  }

  @override
  Future<void> stop() async {
    // 基础停止逻辑
    _currentState = PluginState.stopped;
    await Future<void>.delayed(const Duration(milliseconds: 50));
  }

  @override
  Future<void> dispose() async {
    // 基础清理逻辑
    _currentState = PluginState.unloaded;
    await Future<void>.delayed(const Duration(milliseconds: 50));
  }

  @override
  Object? getConfigWidget() => null;

  @override
  Object getMainWidget() => 'Isolated Plugin Widget';

  @override
  Future<dynamic> handleMessage(
    String action,
    Map<String, dynamic> data,
  ) async =>
      <String, dynamic>{'status': 'received', 'action': action};

  PluginState _currentState = PluginState.unloaded;

  @override
  PluginState get currentState => _currentState;

  @override
  Stream<PluginState> get stateChanges => const Stream<PluginState>.empty();

  @override
  PluginManifest get manifest => const PluginManifest(
        id: 'isolated_plugin',
        name: 'Isolated Plugin',
        version: '1.0.0',
        description: 'Basic isolated plugin instance',
        author: 'System',
        category: 'system',
        main: 'lib/main.dart',
      );

  @override
  bool get isEnabled => true;

  @override
  Duration? get loadTime => const Duration(milliseconds: 100);
}
