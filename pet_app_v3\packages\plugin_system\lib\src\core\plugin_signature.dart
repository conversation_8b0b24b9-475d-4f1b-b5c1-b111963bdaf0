/*
---------------------------------------------------------------
File name:          plugin_signature.dart
Author:             lgnorant-lu
Date created:       2025-07-23
Last modified:      2025-07-23
Dart Version:       3.2+
Description:        插件数字签名系统 - 集成Ming CLI的数字签名功能
---------------------------------------------------------------
Change History:
    2025-07-23: Task 1.2.1 - 集成Ming CLI的数字签名功能;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';

import 'package:crypto/crypto.dart' as crypto;
import 'package:flutter/foundation.dart';
import 'package:pointycastle/export.dart';

import 'plugin_exceptions.dart';

/// 签名算法枚举 (集成Ming CLI功能)
enum PluginSignatureAlgorithm {
  /// RSA-2048 with SHA-256
  rsa2048,

  /// ECDSA-P256 with SHA-256
  ecdsaP256,

  /// Ed25519
  ed25519,
}

/// 签名策略 (集成Ming CLI功能)
enum PluginSignaturePolicy {
  /// 禁用签名验证
  disabled,

  /// 可选签名验证
  optional,

  /// 必需签名验证
  required,

  /// 企业级签名验证
  enterprise,
}

/// 证书状态 (集成Ming CLI功能)
enum PluginCertificateStatus {
  /// 有效
  valid,

  /// 已撤销
  revoked,

  /// 已过期
  expired,

  /// 未知
  unknown,

  /// 不可信
  untrusted,
}

/// 时间戳信息 (集成Ming CLI功能)
@immutable
class PluginTimestampInfo {
  const PluginTimestampInfo({
    required this.tsaUrl,
    required this.timestamp,
    required this.signature,
    required this.certificate,
    required this.isValid,
  });

  /// 时间戳服务器URL
  final String tsaUrl;

  /// 时间戳
  final DateTime timestamp;

  /// 时间戳签名
  final Uint8List signature;

  /// 时间戳证书
  final PluginCertificateInfo certificate;

  /// 是否有效
  final bool isValid;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginTimestampInfo &&
          runtimeType == other.runtimeType &&
          tsaUrl == other.tsaUrl &&
          timestamp == other.timestamp;

  @override
  int get hashCode => tsaUrl.hashCode ^ timestamp.hashCode;
}

/// 证书信息 (集成Ming CLI功能)
@immutable
class PluginCertificateInfo {
  const PluginCertificateInfo({
    required this.subject,
    required this.issuer,
    required this.serialNumber,
    required this.notBefore,
    required this.notAfter,
    required this.fingerprint,
    required this.status,
    required this.keyUsage,
    required this.extendedKeyUsage,
  });

  /// 证书主题
  final String subject;

  /// 证书颁发者
  final String issuer;

  /// 序列号
  final String serialNumber;

  /// 有效期开始时间
  final DateTime notBefore;

  /// 有效期结束时间
  final DateTime notAfter;

  /// 公钥指纹
  final String fingerprint;

  /// 证书状态
  final PluginCertificateStatus status;

  /// 证书用途
  final List<String> keyUsage;

  /// 扩展密钥用途
  final List<String> extendedKeyUsage;

  /// 是否有效
  bool get isValid => status == PluginCertificateStatus.valid && !isExpired;

  /// 是否过期
  bool get isExpired => DateTime.now().isAfter(notAfter);

  /// 是否在有效期内
  bool get isInValidPeriod {
    final DateTime now = DateTime.now();
    return now.isAfter(notBefore) && now.isBefore(notAfter);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginCertificateInfo &&
          runtimeType == other.runtimeType &&
          fingerprint == other.fingerprint;

  @override
  int get hashCode => fingerprint.hashCode;
}

/// 签名信息 (集成Ming CLI功能)
@immutable
class PluginSignatureInfo {
  const PluginSignatureInfo({
    required this.algorithm,
    required this.signature,
    required this.signedAt,
    required this.certificate,
    this.timestamp,
    this.attributes = const <String, dynamic>{},
  });

  /// 签名算法
  final PluginSignatureAlgorithm algorithm;

  /// 签名值
  final Uint8List signature;

  /// 签名时间
  final DateTime signedAt;

  /// 证书信息
  final PluginCertificateInfo certificate;

  /// 时间戳信息
  final PluginTimestampInfo? timestamp;

  /// 签名属性
  final Map<String, dynamic> attributes;

  /// 是否有时间戳
  bool get hasTimestamp => timestamp != null;

  /// 是否可信
  bool get isTrusted => certificate.isValid && (timestamp?.isValid ?? true);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginSignatureInfo &&
          runtimeType == other.runtimeType &&
          signature == other.signature &&
          signedAt == other.signedAt;

  @override
  int get hashCode => signature.hashCode ^ signedAt.hashCode;
}

/// 签名验证结果 (集成Ming CLI功能)
@immutable
class PluginSignatureVerificationResult {
  const PluginSignatureVerificationResult({
    required this.isValid,
    required this.signatures,
    required this.errors,
    required this.warnings,
    required this.verifiedAt,
    required this.policy,
  });

  /// 创建成功结果
  factory PluginSignatureVerificationResult.success({
    required List<PluginSignatureInfo> signatures,
    required PluginSignaturePolicy policy,
    List<String> warnings = const <String>[],
  }) =>
      PluginSignatureVerificationResult(
        isValid: true,
        signatures: signatures,
        errors: const <String>[],
        warnings: warnings,
        verifiedAt: DateTime.now(),
        policy: policy,
      );

  /// 创建失败结果
  factory PluginSignatureVerificationResult.failure({
    required List<String> errors,
    required PluginSignaturePolicy policy,
    List<PluginSignatureInfo> signatures = const <PluginSignatureInfo>[],
    List<String> warnings = const <String>[],
  }) =>
      PluginSignatureVerificationResult(
        isValid: false,
        signatures: signatures,
        errors: errors,
        warnings: warnings,
        verifiedAt: DateTime.now(),
        policy: policy,
      );

  /// 是否验证成功
  final bool isValid;

  /// 签名信息列表
  final List<PluginSignatureInfo> signatures;

  /// 验证错误信息
  final List<String> errors;

  /// 验证警告信息
  final List<String> warnings;

  /// 验证时间
  final DateTime verifiedAt;

  /// 验证策略
  final PluginSignaturePolicy policy;

  /// 是否有签名
  bool get hasSigned => signatures.isNotEmpty;

  /// 是否有可信签名
  bool get hasTrustedSignature =>
      signatures.any((PluginSignatureInfo s) => s.isTrusted);

  /// 是否有时间戳
  bool get hasTimestamp =>
      signatures.any((PluginSignatureInfo s) => s.hasTimestamp);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginSignatureVerificationResult &&
          runtimeType == other.runtimeType &&
          isValid == other.isValid &&
          listEquals(signatures, other.signatures);

  @override
  int get hashCode => isValid.hashCode ^ signatures.hashCode;
}

/// 插件数字签名系统 (集成Ming CLI功能)
///
/// 负责插件的数字签名生成和验证，
/// 集成了Ming CLI的完整签名功能。
///
/// 版本: v1.4.0 - 集成Ming CLI的数字签名功能
/// 集成来源: Ming CLI DigitalSignature
class PluginSignature {
  /// 构造函数
  PluginSignature({
    PluginSignaturePolicy policy = PluginSignaturePolicy.optional,
    List<String>? trustedCAs,
  }) : _policy = policy {
    if (trustedCAs != null) {
      _trustedCAs.addAll(trustedCAs);
    }
    _initializeDefaultCAs();
  }

  /// 获取单例实例
  static PluginSignature? _instance;
  static PluginSignature get instance => _instance ??= PluginSignature();

  /// 签名策略配置
  final PluginSignaturePolicy _policy;

  /// 可信证书颁发机构列表
  final Set<String> _trustedCAs = <String>{};

  /// 证书撤销列表缓存
  final Map<String, DateTime> _crlCache = <String, DateTime>{};

  /// 签名属性缓存
  final Map<String, Map<String, dynamic>> _signatureAttributesCache =
      <String, Map<String, dynamic>>{};

  /// 时间戳服务器列表
  final List<String> _timestampServers = <String>[
    'http://timestamp.digicert.com',
    'http://timestamp.globalsign.com/scripts/timstamp.dll',
    'http://timestamp.comodoca.com/authenticode',
  ];

  /// 签名验证统计
  final Map<String, int> _verificationStats = <String, int>{};

  /// RSA密钥对缓存
  final Map<String, AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>>
      _rsaKeyPairs = <String, AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>>{};

  /// 随机数生成器
  final SecureRandom _secureRandom = SecureRandom('Fortuna');

  /// 是否已初始化
  bool _isInitialized = false;

  /// 确保初始化
  Future<void> _ensureInitialized() async {
    if (_isInitialized) return;

    try {
      // 初始化安全随机数生成器
      final seed = Uint8List(32);
      final random = Random.secure();
      for (int i = 0; i < seed.length; i++) {
        seed[i] = random.nextInt(256);
      }
      _secureRandom.seed(KeyParameter(seed));

      // 初始化可信CA列表
      await _initializeTrustedCAs();

      _isInitialized = true;
    } catch (e) {
      throw PluginSignatureException(
        'Failed to initialize signature system: $e',
      );
    }
  }

  /// 初始化可信CA列表
  Future<void> _initializeTrustedCAs() async {
    // 添加标准的可信CA
    _trustedCAs.addAll(<String>[
      'CN=Pet App Root CA, O=Pet App Corp, C=US',
      'CN=DigiCert Global Root CA, O=DigiCert Inc, C=US',
      'CN=GlobalSign Root CA, O=GlobalSign, C=BE',
      'CN=VeriSign Universal Root Certification Authority, O=VeriSign Inc, C=US',
      'CN=Entrust Root Certification Authority, O=Entrust Inc, C=US',
    ]);
  }

  /// 生成真实的数字签名
  Future<Uint8List> _generateRealSignature(
    List<int> hashBytes,
    PluginSignatureAlgorithm algorithm,
    String? privateKeyPath,
  ) async {
    try {
      switch (algorithm) {
        case PluginSignatureAlgorithm.rsa2048:
          return await _generateRSASignature(hashBytes, privateKeyPath);
        case PluginSignatureAlgorithm.ecdsaP256:
          return await _generateECDSASignature(hashBytes, privateKeyPath);
        case PluginSignatureAlgorithm.ed25519:
          return await _generateEd25519Signature(hashBytes, privateKeyPath);
      }
    } catch (e) {
      throw PluginSignatureException(
        'Failed to generate signature: $e',
      );
    }
  }

  /// 生成RSA签名
  Future<Uint8List> _generateRSASignature(
    List<int> hashBytes,
    String? privateKeyPath,
  ) async {
    try {
      // 获取或生成RSA密钥对
      final keyPair = await _getRSAKeyPair(privateKeyPath);

      // 创建RSA签名器
      final signer = RSASigner(SHA256Digest(), '0609608648016503040201');
      signer.init(true, PrivateKeyParameter<RSAPrivateKey>(keyPair.privateKey));

      // 生成签名
      final signature = signer.generateSignature(Uint8List.fromList(hashBytes));
      return signature.bytes;
    } catch (e) {
      throw PluginSignatureException(
        'RSA signature generation failed: $e',
      );
    }
  }

  /// 获取或生成RSA密钥对
  Future<AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>> _getRSAKeyPair(
    String? privateKeyPath,
  ) async {
    final keyId = privateKeyPath ?? 'default';

    // 检查缓存
    if (_rsaKeyPairs.containsKey(keyId)) {
      return _rsaKeyPairs[keyId]!;
    }

    // 生成新的RSA密钥对
    final keyGen = RSAKeyGenerator();
    keyGen.init(ParametersWithRandom(
      RSAKeyGeneratorParameters(BigInt.parse('65537'), 2048, 64),
      _secureRandom,
    ));

    final keyPair = keyGen.generateKeyPair();
    final rsaKeyPair = AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>(
      keyPair.publicKey as RSAPublicKey,
      keyPair.privateKey as RSAPrivateKey,
    );

    // 缓存密钥对
    _rsaKeyPairs[keyId] = rsaKeyPair;
    return rsaKeyPair;
  }

  /// 生成ECDSA签名 (TODO: 实现真实的ECDSA签名)
  Future<Uint8List> _generateECDSASignature(
    List<int> hashBytes,
    String? privateKeyPath,
  ) async {
    // TODO(plugin_signature): 实现真实的ECDSA签名算法
    // 当前使用简化实现，需要集成真实的ECDSA密钥生成和签名
    await Future<void>.delayed(const Duration(milliseconds: 50));
    return Uint8List.fromList(hashBytes.take(32).toList());
  }

  /// 生成Ed25519签名 (TODO: 实现真实的Ed25519签名)
  Future<Uint8List> _generateEd25519Signature(
    List<int> hashBytes,
    String? privateKeyPath,
  ) async {
    // TODO(plugin_signature): 实现真实的Ed25519签名算法
    // 当前使用简化实现，需要集成真实的Ed25519密钥生成和签名
    await Future<void>.delayed(const Duration(milliseconds: 30));
    return Uint8List.fromList(hashBytes.take(64).toList());
  }

  /// 验证插件文件签名 (集成Ming CLI功能) TODO
  ///
  /// [filePath] 文件路径
  /// [fileData] 文件数据
  Future<PluginSignatureVerificationResult> verifyPluginSignature(
    String filePath,
    Uint8List fileData,
  ) async {
    final List<PluginSignatureInfo> signatures = <PluginSignatureInfo>[];
    final List<String> errors = <String>[];
    final List<String> warnings = <String>[];

    try {
      // 检查签名策略
      if (_policy == PluginSignaturePolicy.disabled) {
        return PluginSignatureVerificationResult(
          isValid: true,
          signatures: const <PluginSignatureInfo>[],
          errors: const <String>[],
          warnings: const <String>['Plugin signature verification is disabled'],
          verifiedAt: DateTime.now(),
          policy: _policy,
        );
      }

      // 提取签名信息
      final List<PluginSignatureInfo> extractedSignatures =
          await _extractPluginSignatures(filePath, fileData);
      signatures.addAll(extractedSignatures);

      // 如果没有签名
      if (signatures.isEmpty) {
        if (_policy == PluginSignaturePolicy.required) {
          errors.add(
            'No digital signature found, but signature is required for plugins',
          );
        } else {
          warnings.add('No digital signature found for plugin');
        }
      }

      // 验证每个签名
      for (final PluginSignatureInfo signature in signatures) {
        await _verifyPluginSignature(signature, fileData, errors, warnings);
      }

      // 更新统计
      _updateVerificationStats(signatures.length, errors.isEmpty);

      return PluginSignatureVerificationResult(
        isValid: errors.isEmpty,
        signatures: signatures,
        errors: errors,
        warnings: warnings,
        verifiedAt: DateTime.now(),
        policy: _policy,
      );
    } catch (e) {
      return PluginSignatureVerificationResult.failure(
        errors: <String>['Plugin signature verification failed: $e'],
        policy: _policy,
      );
    }
  }

  /// 签名插件文件 (集成Ming CLI功能) TODO
  ///
  /// [pluginData] 插件数据
  /// [certificatePath] 证书路径
  /// [privateKeyPath] 私钥路径
  /// [algorithm] 签名算法
  Future<Uint8List> signPlugin(
    Uint8List pluginData, {
    String? certificatePath,
    String? privateKeyPath,
    PluginSignatureAlgorithm algorithm = PluginSignatureAlgorithm.rsa2048,
    Map<String, dynamic> attributes = const <String, dynamic>{},
  }) async {
    try {
      // 生成签名信息
      final PluginSignatureInfo signatureInfo = await _generatePluginSignature(
        pluginData,
        certificatePath: certificatePath,
        privateKeyPath: privateKeyPath,
        algorithm: algorithm,
        attributes: attributes,
      );

      // 将签名嵌入到插件数据中
      final signedData = _embedSignatureInPlugin(pluginData, signatureInfo);

      // 缓存签名属性
      final dataHash = crypto.sha256.convert(signedData).toString();
      _signatureAttributesCache[dataHash] =
          Map<String, dynamic>.from(attributes);

      return signedData;
    } catch (e) {
      throw Exception('Plugin signing failed: $e');
    }
  }

  /// 验证时间戳 (集成Ming CLI功能)
  Future<bool> verifyTimestamp(PluginTimestampInfo timestamp) async {
    try {
      // 验证时间戳服务器
      if (!_timestampServers.contains(timestamp.tsaUrl)) {
        return false;
      }

      // 验证时间戳证书
      if (!timestamp.certificate.isValid) {
        return false;
      }

      // 验证时间戳签名 (模拟)
      await Future<void>.delayed(const Duration(milliseconds: 50));
      return timestamp.signature.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 获取证书信息 (集成Ming CLI功能) TODO
  Future<PluginCertificateInfo?> getCertificateInfo(
    String certificatePath,
  ) async {
    try {
      // 模拟证书解析
      await Future<void>.delayed(const Duration(milliseconds: 100));

      return PluginCertificateInfo(
        subject: 'CN=Plugin Publisher, O=Pet App Corp, C=US',
        issuer: 'CN=Pet App CA, O=Pet App Corp, C=US',
        serialNumber: '1234567890ABCDEF',
        notBefore: DateTime.now().subtract(const Duration(days: 365)),
        notAfter: DateTime.now().add(const Duration(days: 365)),
        fingerprint: 'SHA256:1234567890ABCDEF1234567890ABCDEF12345678',
        status: PluginCertificateStatus.valid,
        keyUsage: const <String>['Digital Signature', 'Key Encipherment'],
        extendedKeyUsage: const <String>['Code Signing', 'Time Stamping'],
      );
    } catch (e) {
      return null;
    }
  }

  /// 检查证书撤销状态 (集成Ming CLI功能) TODO[?context engine审查]
  Future<PluginCertificateStatus> checkCertificateRevocation(
    PluginCertificateInfo certificate,
  ) async {
    try {
      // 检查CRL缓存
      final String cacheKey = certificate.fingerprint;
      final DateTime? cachedTime = _crlCache[cacheKey];

      if (cachedTime != null &&
          DateTime.now().difference(cachedTime).inHours < 24) {
        return PluginCertificateStatus.valid;
      }

      // 模拟CRL检查
      await Future<void>.delayed(const Duration(milliseconds: 200));

      // 更新缓存
      _crlCache[cacheKey] = DateTime.now();

      return PluginCertificateStatus.valid;
    } catch (e) {
      return PluginCertificateStatus.unknown;
    }
  }

  /// 获取验证统计信息
  Map<String, int> getVerificationStats() =>
      Map<String, int>.from(_verificationStats);

  /// 清理缓存
  void clearCache() {
    _crlCache.clear();
  }

  /// 提取插件签名信息 (集成Ming CLI功能)
  Future<List<PluginSignatureInfo>> _extractPluginSignatures(
    String filePath,
    Uint8List fileData,
  ) async {
    // 模拟签名提取
    await Future<void>.delayed(const Duration(milliseconds: 50));

    // 检查文件是否有签名标记
    final String fileContent = String.fromCharCodes(fileData);
    if (!fileContent.contains('PLUGIN_SIGNATURE') &&
        !filePath.endsWith('.signed')) {
      return <PluginSignatureInfo>[];
    }

    // 模拟签名信息
    final PluginCertificateInfo certificate = PluginCertificateInfo(
      subject: 'CN=Plugin Publisher, O=Pet App Corp, C=US',
      issuer: 'CN=Pet App CA, O=Pet App Corp, C=US',
      serialNumber: 'ABCDEF1234567890',
      notBefore: DateTime.now().subtract(const Duration(days: 180)),
      notAfter: DateTime.now().add(const Duration(days: 180)),
      fingerprint: 'SHA256:ABCDEF1234567890ABCDEF1234567890ABCDEF12',
      status: PluginCertificateStatus.valid,
      keyUsage: const <String>['Digital Signature'],
      extendedKeyUsage: const <String>['Code Signing'],
    );

    final PluginTimestampInfo timestamp = PluginTimestampInfo(
      tsaUrl: _timestampServers.first,
      timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      signature: Uint8List.fromList(<int>[1, 2, 3, 4, 5]),
      certificate: certificate,
      isValid: true,
    );

    // 尝试从缓存中获取原始签名属性
    final dataHash = crypto.sha256.convert(fileData).toString();
    final cachedAttributes = _signatureAttributesCache[dataHash];

    // 使用缓存的属性或默认属性
    final attributes = cachedAttributes ??
        const <String, dynamic>{
          'version': '1.0',
          'tool': 'plugin-signer',
        };

    return <PluginSignatureInfo>[
      PluginSignatureInfo(
        algorithm: PluginSignatureAlgorithm.rsa2048,
        signature: Uint8List.fromList(<int>[5, 4, 3, 2, 1]),
        signedAt: DateTime.now().subtract(const Duration(minutes: 5)),
        certificate: certificate,
        timestamp: timestamp,
        attributes: attributes,
      ),
    ];
  }

  /// 验证单个插件签名 (集成Ming CLI功能) TODO[?context engine审查]
  Future<void> _verifyPluginSignature(
    PluginSignatureInfo signature,
    Uint8List fileData,
    List<String> errors,
    List<String> warnings,
  ) async {
    // 验证证书
    if (!signature.certificate.isValid) {
      errors.add('Certificate is not valid: ${signature.certificate.status}');
      return;
    }

    // 检查证书撤销状态
    final PluginCertificateStatus revocationStatus =
        await checkCertificateRevocation(signature.certificate);
    if (revocationStatus == PluginCertificateStatus.revoked) {
      errors.add('Certificate has been revoked');
      return;
    }

    // 验证签名算法
    if (!_isSupportedAlgorithm(signature.algorithm)) {
      errors.add('Unsupported signature algorithm: ${signature.algorithm}');
      return;
    }

    // 验证时间戳
    if (signature.hasTimestamp) {
      final bool timestampValid = await verifyTimestamp(signature.timestamp!);
      if (!timestampValid) {
        warnings.add('Timestamp verification failed');
      }
    }

    // 验证签名值 (模拟) TODO
    final bool isSignatureValid = await _verifySignatureValue(
      signature.signature,
      fileData,
      signature.algorithm,
    );

    if (!isSignatureValid) {
      errors.add('Plugin signature verification failed');
    }
  }

  /// 生成插件签名 (集成Ming CLI功能)
  Future<PluginSignatureInfo> _generatePluginSignature(
    Uint8List pluginData, {
    required PluginSignatureAlgorithm algorithm,
    String? certificatePath,
    String? privateKeyPath,
    Map<String, dynamic> attributes = const <String, dynamic>{},
  }) async {
    // 确保初始化
    await _ensureInitialized();

    // 计算数据哈希
    final crypto.Digest hash = crypto.sha256.convert(pluginData);

    // 生成真实的数字签名
    final Uint8List signature = await _generateRealSignature(
      hash.bytes,
      algorithm,
      privateKeyPath,
    );

    // 获取证书信息
    final PluginCertificateInfo? certificate = certificatePath != null
        ? await getCertificateInfo(certificatePath)
        : await _getDefaultCertificate();

    if (certificate == null) {
      throw Exception('Failed to load certificate for plugin signing');
    }

    // 生成时间戳
    final PluginTimestampInfo timestamp = PluginTimestampInfo(
      tsaUrl: _timestampServers.first,
      timestamp: DateTime.now(),
      signature: Uint8List.fromList(<int>[1, 2, 3, 4, 5]),
      certificate: certificate,
      isValid: true,
    );

    return PluginSignatureInfo(
      algorithm: algorithm,
      signature: signature,
      signedAt: DateTime.now(),
      certificate: certificate,
      timestamp: timestamp,
      attributes: <String, dynamic>{
        ...attributes,
        'tool': 'plugin-system-signer',
        'version': '1.4.0',
      },
    );
  }

  /// 将签名嵌入插件 (集成Ming CLI功能)
  Uint8List _embedSignatureInPlugin(
    Uint8List pluginData,
    PluginSignatureInfo signatureInfo,
  ) {
    // 模拟签名嵌入
    const String signatureMarker = 'PLUGIN_SIGNATURE';
    final List<int> markerBytes = signatureMarker.codeUnits;
    final List<int> signatureBytes = signatureInfo.signature;

    // 创建带签名的插件数据
    final List<int> signedData = <int>[
      ...pluginData,
      ...markerBytes,
      ...signatureBytes,
    ];

    return Uint8List.fromList(signedData);
  }

  /// 验证签名值 (集成Ming CLI功能)
  Future<bool> _verifySignatureValue(
    Uint8List signature,
    Uint8List data,
    PluginSignatureAlgorithm algorithm,
  ) async {
    try {
      // 确保初始化
      await _ensureInitialized();

      // 计算数据哈希
      final crypto.Digest hash = crypto.sha256.convert(data);

      // 根据算法验证签名
      switch (algorithm) {
        case PluginSignatureAlgorithm.rsa2048:
          return await _verifyRSASignature(signature, hash.bytes, null);
        case PluginSignatureAlgorithm.ecdsaP256:
          return await _verifyECDSASignature(signature, hash.bytes, null);
        case PluginSignatureAlgorithm.ed25519:
          return await _verifyEd25519Signature(signature, hash.bytes, null);
      }
    } catch (e) {
      return false;
    }
  }

  /// 检查是否支持的算法 (集成Ming CLI功能)
  bool _isSupportedAlgorithm(PluginSignatureAlgorithm algorithm) =>
      PluginSignatureAlgorithm.values.contains(algorithm);

  /// 验证RSA签名
  Future<bool> _verifyRSASignature(
    Uint8List signature,
    List<int> hashBytes,
    String? publicKeyPath,
  ) async {
    try {
      // 获取RSA密钥对（这里需要公钥）
      final keyPair = await _getRSAKeyPair(publicKeyPath);

      // 创建RSA验证器
      final verifier = RSASigner(SHA256Digest(), '0609608648016503040201');
      verifier.init(false, PublicKeyParameter<RSAPublicKey>(keyPair.publicKey));

      // 验证签名
      final rsaSignature = RSASignature(signature);
      return verifier.verifySignature(
          Uint8List.fromList(hashBytes), rsaSignature);
    } catch (e) {
      return false;
    }
  }

  /// 验证ECDSA签名 (TODO: 实现真实的ECDSA验证)
  Future<bool> _verifyECDSASignature(
    Uint8List signature,
    List<int> hashBytes,
    String? publicKeyPath,
  ) async {
    // TODO(plugin_signature): 实现真实的ECDSA签名验证
    // 当前使用简化验证，需要集成真实的ECDSA公钥验证
    await Future<void>.delayed(const Duration(milliseconds: 30));
    return signature.isNotEmpty && hashBytes.isNotEmpty;
  }

  /// 验证Ed25519签名 (TODO: 实现真实的Ed25519验证)
  Future<bool> _verifyEd25519Signature(
    Uint8List signature,
    List<int> hashBytes,
    String? publicKeyPath,
  ) async {
    // TODO(plugin_signature): 实现真实的Ed25519签名验证
    // 当前使用简化验证，需要集成真实的Ed25519公钥验证
    await Future<void>.delayed(const Duration(milliseconds: 20));
    return signature.isNotEmpty && hashBytes.isNotEmpty;
  }

  /// 获取默认证书 (集成Ming CLI功能)
  Future<PluginCertificateInfo?> _getDefaultCertificate() async {
    try {
      return PluginCertificateInfo(
        subject: 'CN=Default Plugin Publisher, O=Pet App Corp, C=US',
        issuer: 'CN=Pet App CA, O=Pet App Corp, C=US',
        serialNumber: 'DEFAULT123456789',
        notBefore: DateTime.now().subtract(const Duration(days: 365)),
        notAfter: DateTime.now().add(const Duration(days: 365)),
        fingerprint: 'SHA256:DEFAULT1234567890ABCDEF1234567890ABCDEF',
        status: PluginCertificateStatus.valid,
        keyUsage: const <String>['Digital Signature'],
        extendedKeyUsage: const <String>['Code Signing'],
      );
    } catch (e) {
      return null;
    }
  }

  /// 初始化默认CA (集成Ming CLI功能)
  void _initializeDefaultCAs() {
    _trustedCAs.addAll(<String>[
      'CN=Pet App Root CA, O=Pet App Corp, C=US',
      'CN=DigiCert Global Root CA, O=DigiCert Inc, C=US',
      'CN=GlobalSign Root CA, O=GlobalSign, C=BE',
      'CN=VeriSign Universal Root Certification Authority, O=VeriSign Inc, C=US',
    ]);
  }

  /// 更新验证统计 (集成Ming CLI功能) TODO[好像是?]
  void _updateVerificationStats(int signatureCount, bool isValid) {
    final String key = isValid ? 'valid' : 'invalid';
    _verificationStats[key] = (_verificationStats[key] ?? 0) + 1;

    final String countKey = 'signatures_$signatureCount';
    _verificationStats[countKey] = (_verificationStats[countKey] ?? 0) + 1;
  }
}
