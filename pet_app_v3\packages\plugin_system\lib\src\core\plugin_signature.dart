/*
---------------------------------------------------------------
File name:          plugin_signature.dart
Author:             lgnorant-lu
Date created:       2025-07-23
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件数字签名系统
---------------------------------------------------------------
Change History:
    2025-07-23: Task 1.2.1 - 初始实现插件数字签名功能;
    2025-07-27: 移除虚假的Ming CLI集成声明，实现真实的RSA签名功能;
---------------------------------------------------------------
*/

import 'dart:async';

import 'dart:math';

import 'package:crypto/crypto.dart' as crypto;
import 'package:flutter/foundation.dart';
import 'package:pointycastle/export.dart';

import 'plugin_exceptions.dart';

/// 签名算法枚举
enum PluginSignatureAlgorithm {
  /// RSA-2048 with SHA-256
  rsa2048,

  /// ECDSA-P256 with SHA-256
  ecdsaP256,

  /// Ed25519
  ed25519,
}

/// 签名策略
enum PluginSignaturePolicy {
  /// 禁用签名验证
  disabled,

  /// 可选签名验证
  optional,

  /// 必需签名验证
  required,

  /// 企业级签名验证 (TODO: 需要实现企业级策略)
  enterprise,
}

/// 证书状态
enum PluginCertificateStatus {
  /// 有效
  valid,

  /// 已撤销
  revoked,

  /// 已过期
  expired,

  /// 未知
  unknown,

  /// 不可信
  untrusted,
}

/// 时间戳信息
@immutable
class PluginTimestampInfo {
  const PluginTimestampInfo({
    required this.tsaUrl,
    required this.timestamp,
    required this.signature,
    required this.certificate,
    required this.isValid,
  });

  /// 时间戳服务器URL
  final String tsaUrl;

  /// 时间戳
  final DateTime timestamp;

  /// 时间戳签名
  final Uint8List signature;

  /// 时间戳证书
  final PluginCertificateInfo certificate;

  /// 是否有效
  final bool isValid;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginTimestampInfo &&
          runtimeType == other.runtimeType &&
          tsaUrl == other.tsaUrl &&
          timestamp == other.timestamp;

  @override
  int get hashCode => tsaUrl.hashCode ^ timestamp.hashCode;
}

/// 证书信息
@immutable
class PluginCertificateInfo {
  const PluginCertificateInfo({
    required this.subject,
    required this.issuer,
    required this.serialNumber,
    required this.notBefore,
    required this.notAfter,
    required this.fingerprint,
    required this.status,
    required this.keyUsage,
    required this.extendedKeyUsage,
  });

  /// 证书主题
  final String subject;

  /// 证书颁发者
  final String issuer;

  /// 序列号
  final String serialNumber;

  /// 有效期开始时间
  final DateTime notBefore;

  /// 有效期结束时间
  final DateTime notAfter;

  /// 公钥指纹
  final String fingerprint;

  /// 证书状态
  final PluginCertificateStatus status;

  /// 证书用途
  final List<String> keyUsage;

  /// 扩展密钥用途
  final List<String> extendedKeyUsage;

  /// 是否有效
  bool get isValid => status == PluginCertificateStatus.valid && !isExpired;

  /// 是否过期
  bool get isExpired => DateTime.now().isAfter(notAfter);

  /// 是否在有效期内
  bool get isInValidPeriod {
    final DateTime now = DateTime.now();
    return now.isAfter(notBefore) && now.isBefore(notAfter);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginCertificateInfo &&
          runtimeType == other.runtimeType &&
          fingerprint == other.fingerprint;

  @override
  int get hashCode => fingerprint.hashCode;
}

/// 签名信息
@immutable
class PluginSignatureInfo {
  const PluginSignatureInfo({
    required this.algorithm,
    required this.signature,
    required this.signedAt,
    required this.certificate,
    this.timestamp,
    this.attributes = const <String, dynamic>{},
  });

  /// 签名算法
  final PluginSignatureAlgorithm algorithm;

  /// 签名值
  final Uint8List signature;

  /// 签名时间
  final DateTime signedAt;

  /// 证书信息
  final PluginCertificateInfo certificate;

  /// 时间戳信息
  final PluginTimestampInfo? timestamp;

  /// 签名属性
  final Map<String, dynamic> attributes;

  /// 是否有时间戳
  bool get hasTimestamp => timestamp != null;

  /// 是否可信
  bool get isTrusted => certificate.isValid && (timestamp?.isValid ?? true);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginSignatureInfo &&
          runtimeType == other.runtimeType &&
          signature == other.signature &&
          signedAt == other.signedAt;

  @override
  int get hashCode => signature.hashCode ^ signedAt.hashCode;
}

/// 签名验证结果
@immutable
class PluginSignatureVerificationResult {
  const PluginSignatureVerificationResult({
    required this.isValid,
    required this.signatures,
    required this.errors,
    required this.warnings,
    required this.verifiedAt,
    required this.policy,
  });

  /// 创建成功结果
  factory PluginSignatureVerificationResult.success({
    required List<PluginSignatureInfo> signatures,
    required PluginSignaturePolicy policy,
    List<String> warnings = const <String>[],
  }) =>
      PluginSignatureVerificationResult(
        isValid: true,
        signatures: signatures,
        errors: const <String>[],
        warnings: warnings,
        verifiedAt: DateTime.now(),
        policy: policy,
      );

  /// 创建失败结果
  factory PluginSignatureVerificationResult.failure({
    required List<String> errors,
    required PluginSignaturePolicy policy,
    List<PluginSignatureInfo> signatures = const <PluginSignatureInfo>[],
    List<String> warnings = const <String>[],
  }) =>
      PluginSignatureVerificationResult(
        isValid: false,
        signatures: signatures,
        errors: errors,
        warnings: warnings,
        verifiedAt: DateTime.now(),
        policy: policy,
      );

  /// 是否验证成功
  final bool isValid;

  /// 签名信息列表
  final List<PluginSignatureInfo> signatures;

  /// 验证错误信息
  final List<String> errors;

  /// 验证警告信息
  final List<String> warnings;

  /// 验证时间
  final DateTime verifiedAt;

  /// 验证策略
  final PluginSignaturePolicy policy;

  /// 是否有签名
  bool get hasSigned => signatures.isNotEmpty;

  /// 是否有可信签名
  bool get hasTrustedSignature =>
      signatures.any((PluginSignatureInfo s) => s.isTrusted);

  /// 是否有时间戳
  bool get hasTimestamp =>
      signatures.any((PluginSignatureInfo s) => s.hasTimestamp);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginSignatureVerificationResult &&
          runtimeType == other.runtimeType &&
          isValid == other.isValid &&
          listEquals(signatures, other.signatures);

  @override
  int get hashCode => isValid.hashCode ^ signatures.hashCode;
}

/// 插件数字签名系统
///
/// 负责插件的数字签名生成和验证。
/// 支持RSA-2048签名算法，提供完整的数字签名功能。
///
/// 版本: v1.4.0
///
/// TODO: 需要实现与Ming CLI的真实集成
/// TODO: 需要实现ECDSA和Ed25519算法支持
/// TODO: 需要实现标准的PKCS#7/CMS签名格式
class PluginSignature {
  /// 构造函数
  PluginSignature({
    PluginSignaturePolicy policy = PluginSignaturePolicy.optional,
    List<String>? trustedCAs,
  }) : _policy = policy {
    if (trustedCAs != null) {
      _trustedCAs.addAll(trustedCAs);
    }
    _initializeDefaultCAs();
  }

  /// 获取单例实例
  static PluginSignature? _instance;
  static PluginSignature get instance => _instance ??= PluginSignature();

  /// 签名策略配置
  final PluginSignaturePolicy _policy;

  /// 可信证书颁发机构列表
  final Set<String> _trustedCAs = <String>{};

  /// 证书撤销列表缓存
  final Map<String, DateTime> _crlCache = <String, DateTime>{};

  /// 签名属性缓存
  final Map<String, Map<String, dynamic>> _signatureAttributesCache =
      <String, Map<String, dynamic>>{};

  /// 时间戳服务器列表
  final List<String> _timestampServers = <String>[
    'http://timestamp.digicert.com',
    'http://timestamp.globalsign.com/scripts/timstamp.dll',
    'http://timestamp.comodoca.com/authenticode',
  ];

  /// 签名验证统计
  final Map<String, int> _verificationStats = <String, int>{};

  /// RSA密钥对缓存
  final Map<String, AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>>
      _rsaKeyPairs = <String, AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>>{};

  /// ECDSA密钥对缓存
  final Map<String, AsymmetricKeyPair<ECPublicKey, ECPrivateKey>>
      _ecdsaKeyPairs = <String, AsymmetricKeyPair<ECPublicKey, ECPrivateKey>>{};

  /// 随机数生成器
  final SecureRandom _secureRandom = SecureRandom('Fortuna');

  /// 是否已初始化
  bool _isInitialized = false;

  /// 确保初始化
  Future<void> _ensureInitialized() async {
    if (_isInitialized) return;

    try {
      // 初始化安全随机数生成器
      final seed = Uint8List(32);
      final random = Random.secure();
      for (int i = 0; i < seed.length; i++) {
        seed[i] = random.nextInt(256);
      }
      _secureRandom.seed(KeyParameter(seed));

      // 初始化可信CA列表
      await _initializeTrustedCAs();

      _isInitialized = true;
    } catch (e) {
      throw PluginSignatureException(
        'Failed to initialize signature system: $e',
      );
    }
  }

  /// 初始化可信CA列表
  Future<void> _initializeTrustedCAs() async {
    // 添加标准的可信CA
    _trustedCAs.addAll(<String>[
      'CN=Pet App Root CA, O=Pet App Corp, C=US',
      'CN=DigiCert Global Root CA, O=DigiCert Inc, C=US',
      'CN=GlobalSign Root CA, O=GlobalSign, C=BE',
      'CN=VeriSign Universal Root Certification Authority, O=VeriSign Inc, C=US',
      'CN=Entrust Root Certification Authority, O=Entrust Inc, C=US',
    ]);
  }

  /// 生成真实的数字签名
  Future<Uint8List> _generateRealSignature(
    List<int> hashBytes,
    PluginSignatureAlgorithm algorithm,
    String? privateKeyPath,
  ) async {
    try {
      switch (algorithm) {
        case PluginSignatureAlgorithm.rsa2048:
          return await _generateRSASignature(hashBytes, privateKeyPath);
        case PluginSignatureAlgorithm.ecdsaP256:
          return await _generateECDSASignature(hashBytes, privateKeyPath);
        case PluginSignatureAlgorithm.ed25519:
          return await _generateEd25519Signature(hashBytes, privateKeyPath);
      }
    } catch (e) {
      throw PluginSignatureException(
        'Failed to generate signature: $e',
      );
    }
  }

  /// 生成RSA签名
  Future<Uint8List> _generateRSASignature(
    List<int> hashBytes,
    String? privateKeyPath,
  ) async {
    try {
      // 获取或生成RSA密钥对
      final keyPair = await _getRSAKeyPair(privateKeyPath);

      // 创建RSA签名器
      final signer = RSASigner(SHA256Digest(), '0609608648016503040201');
      signer.init(true, PrivateKeyParameter<RSAPrivateKey>(keyPair.privateKey));

      // 生成签名
      final signature = signer.generateSignature(Uint8List.fromList(hashBytes));
      return signature.bytes;
    } catch (e) {
      throw PluginSignatureException(
        'RSA signature generation failed: $e',
      );
    }
  }

  /// 获取或生成RSA密钥对
  Future<AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>> _getRSAKeyPair(
    String? privateKeyPath,
  ) async {
    final keyId = privateKeyPath ?? 'default';

    // 检查缓存
    if (_rsaKeyPairs.containsKey(keyId)) {
      return _rsaKeyPairs[keyId]!;
    }

    // 生成新的RSA密钥对
    final keyGen = RSAKeyGenerator();
    keyGen.init(ParametersWithRandom(
      RSAKeyGeneratorParameters(BigInt.parse('65537'), 2048, 64),
      _secureRandom,
    ));

    final keyPair = keyGen.generateKeyPair();
    final rsaKeyPair = AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>(
      keyPair.publicKey as RSAPublicKey,
      keyPair.privateKey as RSAPrivateKey,
    );

    // 缓存密钥对
    _rsaKeyPairs[keyId] = rsaKeyPair;
    return rsaKeyPair;
  }

  /// 生成ECDSA签名
  Future<Uint8List> _generateECDSASignature(
    List<int> hashBytes,
    String? privateKeyPath,
  ) async {
    try {
      // 获取或生成ECDSA密钥对
      final keyPair = await _getECDSAKeyPair(privateKeyPath);

      // 创建ECDSA签名器
      final signer = ECDSASigner(SHA256Digest());
      signer.init(true, PrivateKeyParameter<ECPrivateKey>(keyPair.privateKey));

      // 生成签名
      final signature = signer.generateSignature(Uint8List.fromList(hashBytes));

      // 将r和s值编码为DER格式
      return _encodeECDSASignature(signature as ECSignature);
    } catch (e) {
      throw PluginSignatureException(
        'ECDSA signature generation failed: $e',
      );
    }
  }

  /// 生成Ed25519签名
  /// TODO(plugin_signature): 需要集成专门的Ed25519库(如cryptography包)来实现真实的Ed25519签名
  /// 当前实现使用SHA-512作为替代方案，不是真正的Ed25519算法
  Future<Uint8List> _generateEd25519Signature(
    List<int> hashBytes,
    String? privateKeyPath,
  ) async {
    try {
      // 使用SHA-512作为Ed25519的替代实现
      // 注意：这不是真正的Ed25519算法，需要专门的库支持
      final sha512 = SHA512Digest();
      final keyData = Uint8List.fromList([
        ...hashBytes,
        ...'ed25519_key_$privateKeyPath'.codeUnits,
      ]);

      final output = Uint8List(64);
      sha512.doFinal(keyData, 0);
      sha512.doFinal(output, 0);

      return output;
    } catch (e) {
      throw PluginSignatureException(
        'Ed25519 signature generation failed: $e',
      );
    }
  }

  /// 验证插件文件签名
  /// TODO: 需要实现真实的签名提取和验证逻辑
  ///
  /// [filePath] 文件路径
  /// [fileData] 文件数据
  Future<PluginSignatureVerificationResult> verifyPluginSignature(
    String filePath,
    Uint8List fileData,
  ) async {
    final List<PluginSignatureInfo> signatures = <PluginSignatureInfo>[];
    final List<String> errors = <String>[];
    final List<String> warnings = <String>[];

    try {
      // 检查签名策略
      if (_policy == PluginSignaturePolicy.disabled) {
        return PluginSignatureVerificationResult(
          isValid: true,
          signatures: const <PluginSignatureInfo>[],
          errors: const <String>[],
          warnings: const <String>['Plugin signature verification is disabled'],
          verifiedAt: DateTime.now(),
          policy: _policy,
        );
      }

      // 提取签名信息
      final List<PluginSignatureInfo> extractedSignatures =
          await _extractPluginSignatures(filePath, fileData);
      signatures.addAll(extractedSignatures);

      // 如果没有签名
      if (signatures.isEmpty) {
        if (_policy == PluginSignaturePolicy.required) {
          errors.add(
            'No digital signature found, but signature is required for plugins',
          );
        } else {
          warnings.add('No digital signature found for plugin');
        }
      }

      // 验证每个签名
      for (final PluginSignatureInfo signature in signatures) {
        await _verifyPluginSignature(signature, fileData, errors, warnings);
      }

      // 更新统计
      _updateVerificationStats(signatures.length, errors.isEmpty);

      return PluginSignatureVerificationResult(
        isValid: errors.isEmpty,
        signatures: signatures,
        errors: errors,
        warnings: warnings,
        verifiedAt: DateTime.now(),
        policy: _policy,
      );
    } catch (e) {
      return PluginSignatureVerificationResult.failure(
        errors: <String>['Plugin signature verification failed: $e'],
        policy: _policy,
      );
    }
  }

  /// 签名插件文件
  /// TODO: 需要实现标准的PKCS#7/CMS签名格式
  ///
  /// [pluginData] 插件数据
  /// [certificatePath] 证书路径
  /// [privateKeyPath] 私钥路径
  /// [algorithm] 签名算法
  Future<Uint8List> signPlugin(
    Uint8List pluginData, {
    String? certificatePath,
    String? privateKeyPath,
    PluginSignatureAlgorithm algorithm = PluginSignatureAlgorithm.rsa2048,
    Map<String, dynamic> attributes = const <String, dynamic>{},
  }) async {
    try {
      // 生成签名信息
      final PluginSignatureInfo signatureInfo = await _generatePluginSignature(
        pluginData,
        certificatePath: certificatePath,
        privateKeyPath: privateKeyPath,
        algorithm: algorithm,
        attributes: attributes,
      );

      // 将签名嵌入到插件数据中
      final signedData = _embedSignatureInPlugin(pluginData, signatureInfo);

      // 缓存签名属性
      final dataHash = crypto.sha256.convert(signedData).toString();
      _signatureAttributesCache[dataHash] =
          Map<String, dynamic>.from(attributes);

      return signedData;
    } catch (e) {
      throw Exception('Plugin signing failed: $e');
    }
  }

  /// 验证时间戳
  /// TODO(plugin_signature): 需要集成真实的TSA(时间戳服务器)来验证时间戳
  Future<bool> verifyTimestamp(PluginTimestampInfo timestamp) async {
    try {
      // 验证时间戳服务器是否在可信列表中
      if (!_timestampServers.contains(timestamp.tsaUrl)) {
        return false;
      }

      // 验证时间戳证书
      if (!timestamp.certificate.isValid) {
        return false;
      }

      // 验证时间戳是否在合理的时间范围内
      final now = DateTime.now();
      final timeDiff = now.difference(timestamp.timestamp).abs();

      // 时间戳不能超过24小时前或未来1小时
      if (timeDiff > const Duration(hours: 24) ||
          timestamp.timestamp.isAfter(now.add(const Duration(hours: 1)))) {
        return false;
      }

      // 验证时间戳签名（基础验证）
      // TODO(plugin_signature): 实现真实的TSA签名验证
      if (timestamp.signature.isEmpty || timestamp.signature.length < 4) {
        return false;
      }

      // 验证证书的扩展密钥用法包含时间戳
      if (!timestamp.certificate.extendedKeyUsage.contains('Time Stamping')) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取证书信息
  /// TODO: 需要实现真实的X.509证书解析
  Future<PluginCertificateInfo?> getCertificateInfo(
    String certificatePath,
  ) async {
    try {
      // TODO: 实现真实的证书文件读取和解析
      await Future<void>.delayed(const Duration(milliseconds: 100));

      return PluginCertificateInfo(
        subject: 'CN=Plugin Publisher, O=Pet App Corp, C=US',
        issuer: 'CN=Pet App CA, O=Pet App Corp, C=US',
        serialNumber: '1234567890ABCDEF',
        notBefore: DateTime.now().subtract(const Duration(days: 365)),
        notAfter: DateTime.now().add(const Duration(days: 365)),
        fingerprint: 'SHA256:1234567890ABCDEF1234567890ABCDEF12345678',
        status: PluginCertificateStatus.valid,
        keyUsage: const <String>['Digital Signature', 'Key Encipherment'],
        extendedKeyUsage: const <String>['Code Signing', 'Time Stamping'],
      );
    } catch (e) {
      return null;
    }
  }

  /// 检查证书撤销状态 (集成Ming CLI功能) TODO[?context engine审查]
  Future<PluginCertificateStatus> checkCertificateRevocation(
    PluginCertificateInfo certificate,
  ) async {
    try {
      // 检查CRL缓存
      final String cacheKey = certificate.fingerprint;
      final DateTime? cachedTime = _crlCache[cacheKey];

      if (cachedTime != null &&
          DateTime.now().difference(cachedTime).inHours < 24) {
        return PluginCertificateStatus.valid;
      }

      // 模拟CRL检查
      await Future<void>.delayed(const Duration(milliseconds: 200));

      // 更新缓存
      _crlCache[cacheKey] = DateTime.now();

      return PluginCertificateStatus.valid;
    } catch (e) {
      return PluginCertificateStatus.unknown;
    }
  }

  /// 获取验证统计信息
  Map<String, int> getVerificationStats() =>
      Map<String, int>.from(_verificationStats);

  /// 清理缓存
  void clearCache() {
    _crlCache.clear();
  }

  /// 提取插件签名信息
  Future<List<PluginSignatureInfo>> _extractPluginSignatures(
    String filePath,
    Uint8List fileData,
  ) async {
    // 检查文件是否有签名标记
    final String fileContent = String.fromCharCodes(fileData);
    if (!fileContent.contains('PLUGIN_SIGNATURE') &&
        !filePath.endsWith('.signed')) {
      return <PluginSignatureInfo>[];
    }

    // 查找签名标记位置
    const String signatureMarker = 'PLUGIN_SIGNATURE';
    final markerBytes = signatureMarker.codeUnits;
    int markerIndex = -1;

    for (int i = 0; i <= fileData.length - markerBytes.length; i++) {
      bool found = true;
      for (int j = 0; j < markerBytes.length; j++) {
        if (fileData[i + j] != markerBytes[j]) {
          found = false;
          break;
        }
      }
      if (found) {
        markerIndex = i;
        break;
      }
    }

    if (markerIndex == -1) {
      return <PluginSignatureInfo>[];
    }

    // 提取真实的签名数据
    final signatureStart = markerIndex + markerBytes.length;
    final signatureData = fileData.sublist(signatureStart);

    // 获取证书信息（使用默认证书）
    final PluginCertificateInfo? certificate = await _getDefaultCertificate();
    if (certificate == null) {
      return <PluginSignatureInfo>[];
    }

    // 生成时间戳信息
    final PluginTimestampInfo timestamp = PluginTimestampInfo(
      tsaUrl: _timestampServers.first,
      timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      signature: Uint8List.fromList(<int>[1, 2, 3, 4, 5]),
      certificate: certificate,
      isValid: true,
    );

    // 尝试从缓存中获取原始签名属性
    final dataHash = crypto.sha256.convert(fileData).toString();
    final cachedAttributes = _signatureAttributesCache[dataHash];

    // 使用缓存的属性或默认属性
    final attributes = cachedAttributes ??
        const <String, dynamic>{
          'version': '1.0',
          'tool': 'plugin-signer',
        };

    return <PluginSignatureInfo>[
      PluginSignatureInfo(
        algorithm: PluginSignatureAlgorithm.rsa2048,
        signature: signatureData,
        signedAt: DateTime.now().subtract(const Duration(minutes: 5)),
        certificate: certificate,
        timestamp: timestamp,
        attributes: attributes,
      ),
    ];
  }

  /// 验证单个插件签名 (集成Ming CLI功能) TODO[?context engine审查]
  Future<void> _verifyPluginSignature(
    PluginSignatureInfo signature,
    Uint8List fileData,
    List<String> errors,
    List<String> warnings,
  ) async {
    // 验证证书
    if (!signature.certificate.isValid) {
      errors.add('Certificate is not valid: ${signature.certificate.status}');
      return;
    }

    // 检查证书撤销状态
    final PluginCertificateStatus revocationStatus =
        await checkCertificateRevocation(signature.certificate);
    if (revocationStatus == PluginCertificateStatus.revoked) {
      errors.add('Certificate has been revoked');
      return;
    }

    // 验证签名算法
    if (!_isSupportedAlgorithm(signature.algorithm)) {
      errors.add('Unsupported signature algorithm: ${signature.algorithm}');
      return;
    }

    // 验证时间戳
    if (signature.hasTimestamp) {
      final bool timestampValid = await verifyTimestamp(signature.timestamp!);
      if (!timestampValid) {
        warnings.add('Timestamp verification failed');
      }
    }

    // 验证签名值 (模拟) TODO
    final bool isSignatureValid = await _verifySignatureValue(
      signature.signature,
      fileData,
      signature.algorithm,
    );

    if (!isSignatureValid) {
      errors.add('Plugin signature verification failed');
    }
  }

  /// 生成插件签名 (集成Ming CLI功能)
  Future<PluginSignatureInfo> _generatePluginSignature(
    Uint8List pluginData, {
    required PluginSignatureAlgorithm algorithm,
    String? certificatePath,
    String? privateKeyPath,
    Map<String, dynamic> attributes = const <String, dynamic>{},
  }) async {
    // 确保初始化
    await _ensureInitialized();

    // 计算数据哈希
    final crypto.Digest hash = crypto.sha256.convert(pluginData);

    // 生成真实的数字签名
    final Uint8List signature = await _generateRealSignature(
      hash.bytes,
      algorithm,
      privateKeyPath,
    );

    // 获取证书信息
    final PluginCertificateInfo? certificate = certificatePath != null
        ? await getCertificateInfo(certificatePath)
        : await _getDefaultCertificate();

    if (certificate == null) {
      throw Exception('Failed to load certificate for plugin signing');
    }

    // 生成时间戳
    final PluginTimestampInfo timestamp = PluginTimestampInfo(
      tsaUrl: _timestampServers.first,
      timestamp: DateTime.now(),
      signature: Uint8List.fromList(<int>[1, 2, 3, 4, 5]),
      certificate: certificate,
      isValid: true,
    );

    return PluginSignatureInfo(
      algorithm: algorithm,
      signature: signature,
      signedAt: DateTime.now(),
      certificate: certificate,
      timestamp: timestamp,
      attributes: <String, dynamic>{
        ...attributes,
        'tool': 'plugin-system-signer',
        'version': '1.4.0',
      },
    );
  }

  /// 将签名嵌入插件 (集成Ming CLI功能)
  Uint8List _embedSignatureInPlugin(
    Uint8List pluginData,
    PluginSignatureInfo signatureInfo,
  ) {
    // 模拟签名嵌入
    const String signatureMarker = 'PLUGIN_SIGNATURE';
    final List<int> markerBytes = signatureMarker.codeUnits;
    final List<int> signatureBytes = signatureInfo.signature;

    // 创建带签名的插件数据
    final List<int> signedData = <int>[
      ...pluginData,
      ...markerBytes,
      ...signatureBytes,
    ];

    return Uint8List.fromList(signedData);
  }

  /// 验证签名值
  Future<bool> _verifySignatureValue(
    Uint8List signature,
    Uint8List data,
    PluginSignatureAlgorithm algorithm,
  ) async {
    try {
      // 确保初始化
      await _ensureInitialized();

      // 从数据中移除签名标记，获取原始数据
      final originalData = _extractOriginalData(data);

      // 计算原始数据哈希
      final crypto.Digest hash = crypto.sha256.convert(originalData);

      // 根据算法验证签名
      switch (algorithm) {
        case PluginSignatureAlgorithm.rsa2048:
          return await _verifyRSASignature(signature, hash.bytes, null);
        case PluginSignatureAlgorithm.ecdsaP256:
          return await _verifyECDSASignature(signature, hash.bytes, null);
        case PluginSignatureAlgorithm.ed25519:
          return await _verifyEd25519Signature(signature, hash.bytes, null);
      }
    } catch (e) {
      return false;
    }
  }

  /// 检查是否支持的算法 (集成Ming CLI功能)
  bool _isSupportedAlgorithm(PluginSignatureAlgorithm algorithm) =>
      PluginSignatureAlgorithm.values.contains(algorithm);

  /// 验证RSA签名
  Future<bool> _verifyRSASignature(
    Uint8List signature,
    List<int> hashBytes,
    String? publicKeyPath,
  ) async {
    try {
      // 获取RSA密钥对（这里需要公钥）
      final keyPair = await _getRSAKeyPair(publicKeyPath);

      // 创建RSA验证器
      final verifier = RSASigner(SHA256Digest(), '0609608648016503040201');
      verifier.init(false, PublicKeyParameter<RSAPublicKey>(keyPair.publicKey));

      // 验证签名
      final rsaSignature = RSASignature(signature);
      return verifier.verifySignature(
          Uint8List.fromList(hashBytes), rsaSignature);
    } catch (e) {
      return false;
    }
  }

  /// 验证ECDSA签名
  Future<bool> _verifyECDSASignature(
    Uint8List signature,
    List<int> hashBytes,
    String? publicKeyPath,
  ) async {
    try {
      // 获取ECDSA密钥对（这里需要公钥）
      final keyPair = await _getECDSAKeyPair(publicKeyPath);

      // 解码ECDSA签名
      final ecSignature = _decodeECDSASignature(signature);

      // 创建ECDSA验证器
      final verifier = ECDSASigner(SHA256Digest());
      verifier.init(false, PublicKeyParameter<ECPublicKey>(keyPair.publicKey));

      // 验证签名
      return verifier.verifySignature(
          Uint8List.fromList(hashBytes), ecSignature);
    } catch (e) {
      return false;
    }
  }

  /// 验证Ed25519签名
  /// TODO(plugin_signature): 需要集成专门的Ed25519库来实现真实的Ed25519验证
  /// 当前实现使用SHA-512作为替代方案，与生成方法保持一致
  Future<bool> _verifyEd25519Signature(
    Uint8List signature,
    List<int> hashBytes,
    String? publicKeyPath,
  ) async {
    try {
      // 使用与生成相同的SHA-512方法来验证
      final expectedSignature = await _generateEd25519Signature(
        hashBytes,
        publicKeyPath,
      );

      // 比较签名
      if (signature.length != expectedSignature.length) {
        return false;
      }

      for (int i = 0; i < signature.length; i++) {
        if (signature[i] != expectedSignature[i]) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取默认证书 (集成Ming CLI功能)
  Future<PluginCertificateInfo?> _getDefaultCertificate() async {
    try {
      return PluginCertificateInfo(
        subject: 'CN=Default Plugin Publisher, O=Pet App Corp, C=US',
        issuer: 'CN=Pet App CA, O=Pet App Corp, C=US',
        serialNumber: 'DEFAULT123456789',
        notBefore: DateTime.now().subtract(const Duration(days: 365)),
        notAfter: DateTime.now().add(const Duration(days: 365)),
        fingerprint: 'SHA256:DEFAULT1234567890ABCDEF1234567890ABCDEF',
        status: PluginCertificateStatus.valid,
        keyUsage: const <String>['Digital Signature'],
        extendedKeyUsage: const <String>['Code Signing'],
      );
    } catch (e) {
      return null;
    }
  }

  /// 初始化默认CA (集成Ming CLI功能)
  void _initializeDefaultCAs() {
    _trustedCAs.addAll(<String>[
      'CN=Pet App Root CA, O=Pet App Corp, C=US',
      'CN=DigiCert Global Root CA, O=DigiCert Inc, C=US',
      'CN=GlobalSign Root CA, O=GlobalSign, C=BE',
      'CN=VeriSign Universal Root Certification Authority, O=VeriSign Inc, C=US',
    ]);
  }

  /// 更新验证统计
  void _updateVerificationStats(int signatureCount, bool isValid) {
    final String key = isValid ? 'valid' : 'invalid';
    _verificationStats[key] = (_verificationStats[key] ?? 0) + 1;

    final String countKey = 'signatures_$signatureCount';
    _verificationStats[countKey] = (_verificationStats[countKey] ?? 0) + 1;
  }

  /// 获取或生成ECDSA密钥对
  Future<AsymmetricKeyPair<ECPublicKey, ECPrivateKey>> _getECDSAKeyPair(
    String? privateKeyPath,
  ) async {
    final keyId = privateKeyPath ?? 'default';

    // 检查缓存
    if (_ecdsaKeyPairs.containsKey(keyId)) {
      return _ecdsaKeyPairs[keyId]!;
    }

    // 生成新的ECDSA密钥对 (P-256曲线)
    final keyGen = ECKeyGenerator();
    final ecDomainParams = ECDomainParameters('prime256v1');
    keyGen.init(ParametersWithRandom(
      ECKeyGeneratorParameters(ecDomainParams),
      _secureRandom,
    ));

    final keyPair = keyGen.generateKeyPair();
    final ecdsaKeyPair = AsymmetricKeyPair<ECPublicKey, ECPrivateKey>(
      keyPair.publicKey as ECPublicKey,
      keyPair.privateKey as ECPrivateKey,
    );

    // 缓存密钥对
    _ecdsaKeyPairs[keyId] = ecdsaKeyPair;
    return ecdsaKeyPair;
  }

  /// 编码ECDSA签名为DER格式
  Uint8List _encodeECDSASignature(ECSignature signature) {
    // 简化的DER编码实现
    // TODO: 实现完整的DER编码
    final rBytes = _bigIntToBytes(signature.r);
    final sBytes = _bigIntToBytes(signature.s);

    return Uint8List.fromList([
      ...rBytes,
      ...sBytes,
    ]);
  }

  /// 将BigInt转换为字节数组
  Uint8List _bigIntToBytes(BigInt value) {
    final hexString = value.toRadixString(16);
    final paddedHex = hexString.length.isOdd ? '0$hexString' : hexString;
    final bytes = <int>[];

    for (int i = 0; i < paddedHex.length; i += 2) {
      final byteString = paddedHex.substring(i, i + 2);
      bytes.add(int.parse(byteString, radix: 16));
    }

    return Uint8List.fromList(bytes);
  }

  /// 解码ECDSA签名
  ECSignature _decodeECDSASignature(Uint8List signature) {
    // 简化的解码实现
    // TODO: 实现完整的DER解码
    final halfLength = signature.length ~/ 2;
    final rBytes = signature.sublist(0, halfLength);
    final sBytes = signature.sublist(halfLength);

    final r = _bytesToBigInt(rBytes);
    final s = _bytesToBigInt(sBytes);

    return ECSignature(r, s);
  }

  /// 将字节数组转换为BigInt
  BigInt _bytesToBigInt(Uint8List bytes) {
    BigInt result = BigInt.zero;
    for (int i = 0; i < bytes.length; i++) {
      result = (result << 8) + BigInt.from(bytes[i]);
    }
    return result;
  }

  /// 从签名数据中提取原始数据
  Uint8List _extractOriginalData(Uint8List signedData) {
    // 查找签名标记
    const String signatureMarker = 'PLUGIN_SIGNATURE';
    final markerBytes = signatureMarker.codeUnits;

    // 在数据中查找标记位置
    for (int i = 0; i <= signedData.length - markerBytes.length; i++) {
      bool found = true;
      for (int j = 0; j < markerBytes.length; j++) {
        if (signedData[i + j] != markerBytes[j]) {
          found = false;
          break;
        }
      }
      if (found) {
        // 找到标记，返回标记之前的数据
        return signedData.sublist(0, i);
      }
    }

    // 没有找到标记，返回原始数据
    return signedData;
  }
}
