/*
---------------------------------------------------------------
File name:          plugin_publisher.dart
Author:             lgnorant-lu
Date created:       2025-07-23
Last modified:      2025-07-23
Dart Version:       3.2+
Description:        插件发布管理器 - Phase ******** 插件发布功能
---------------------------------------------------------------
Change History:
    2025-07-23: Phase ******** - 插件发布功能实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:meta/meta.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;

import 'package:plugin_system/src/core/plugin_exceptions.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';
import 'package:plugin_system/src/core/plugin_signature.dart';

/// 发布状态
enum PublishStatus {
  /// 准备中
  preparing,

  /// 验证中
  validating,

  /// 签名中
  signing,

  /// 上传中
  uploading,

  /// 已完成
  completed,

  /// 失败
  failed,
}

/// 发布结果
class PublishResult {
  const PublishResult({
    required this.success,
    this.publishId,
    this.downloadUrl,
    this.version,
    this.error,
    this.warnings = const <String>[],
  });

  /// 创建成功结果
  factory PublishResult.success({
    required String publishId,
    required String downloadUrl,
    required String version,
    List<String> warnings = const <String>[],
  }) =>
      PublishResult(
        success: true,
        publishId: publishId,
        downloadUrl: downloadUrl,
        version: version,
        warnings: warnings,
      );

  /// 创建失败结果
  factory PublishResult.failure({
    required String error,
    List<String> warnings = const <String>[],
  }) =>
      PublishResult(
        success: false,
        error: error,
        warnings: warnings,
      );

  /// 是否成功
  final bool success;

  /// 发布ID
  final String? publishId;

  /// 下载URL
  final String? downloadUrl;

  /// 发布版本
  final String? version;

  /// 错误信息
  final String? error;

  /// 警告信息
  final List<String> warnings;
}

/// 发布进度信息
class PublishProgress {
  const PublishProgress({
    required this.status,
    required this.progress,
    this.message,
    this.details,
  });

  /// 当前状态
  final PublishStatus status;

  /// 进度百分比 (0.0 - 1.0)
  final double progress;

  /// 状态消息
  final String? message;

  /// 详细信息
  final Map<String, dynamic>? details;
}

/// 发布配置
class PublishConfig {
  const PublishConfig({
    this.registryUrl = 'https://plugins.petapp.dev',
    this.apiKey,
    this.enableSigning = true,
    this.enableValidation = true,
    this.compressionLevel = 6,
    this.includeSource = false,
    this.tags = const <String>[],
    this.category,
    this.visibility = PluginVisibility.public,
  });

  /// 插件注册表URL
  final String registryUrl;

  /// API密钥
  final String? apiKey;

  /// 是否启用签名
  final bool enableSigning;

  /// 是否启用验证
  final bool enableValidation;

  /// 压缩级别 (0-9)
  final int compressionLevel;

  /// 是否包含源代码
  final bool includeSource;

  /// 标签列表
  final List<String> tags;

  /// 插件分类
  final String? category;

  /// 可见性
  final PluginVisibility visibility;
}

/// 插件可见性
enum PluginVisibility {
  /// 公开
  public,

  /// 私有
  private,

  /// 组织内部
  organization,
}

/// 插件发布元数据
class PublishMetadata {
  const PublishMetadata({
    required this.id,
    required this.name,
    required this.version,
    required this.description,
    required this.author,
    this.homepage,
    this.repository,
    this.license,
    this.keywords = const <String>[],
    this.category,
    this.minSdkVersion,
    this.maxSdkVersion,
    this.dependencies = const <String, String>{},
    this.devDependencies = const <String, String>{},
    this.permissions = const <String>[],
    this.platforms = const <String>[],
    this.screenshots = const <String>[],
    this.changelog,
  });

  /// 从JSON创建
  factory PublishMetadata.fromJson(Map<String, dynamic> json) =>
      PublishMetadata(
        id: json['id'] as String,
        name: json['name'] as String,
        version: json['version'] as String,
        description: json['description'] as String,
        author: json['author'] as String,
        homepage: json['homepage'] as String?,
        repository: json['repository'] as String?,
        license: json['license'] as String?,
        keywords: List<String>.from(
          (json['keywords'] as List<dynamic>?) ?? <dynamic>[],
        ),
        category: json['category'] as String?,
        minSdkVersion: json['minSdkVersion'] as String?,
        maxSdkVersion: json['maxSdkVersion'] as String?,
        dependencies: Map<String, String>.from(
          (json['dependencies'] as Map<String, dynamic>?) ??
              <dynamic, dynamic>{},
        ),
        devDependencies: Map<String, String>.from(
          (json['devDependencies'] as Map<String, dynamic>?) ??
              <dynamic, dynamic>{},
        ),
        permissions: List<String>.from(
          (json['permissions'] as List<dynamic>?) ?? <dynamic>[],
        ),
        platforms: List<String>.from(
          (json['platforms'] as List<dynamic>?) ?? <dynamic>[],
        ),
        screenshots: List<String>.from(
          (json['screenshots'] as List<dynamic>?) ?? <dynamic>[],
        ),
        changelog: json['changelog'] as String?,
      );

  /// 插件ID
  final String id;

  /// 插件名称
  final String name;

  /// 版本号
  final String version;

  /// 描述
  final String description;

  /// 作者
  final String author;

  /// 主页URL
  final String? homepage;

  /// 仓库URL
  final String? repository;

  /// 许可证
  final String? license;

  /// 关键词
  final List<String> keywords;

  /// 分类
  final String? category;

  /// 最小SDK版本
  final String? minSdkVersion;

  /// 最大SDK版本
  final String? maxSdkVersion;

  /// 依赖
  final Map<String, String> dependencies;

  /// 开发依赖
  final Map<String, String> devDependencies;

  /// 权限
  final List<String> permissions;

  /// 支持的平台
  final List<String> platforms;

  /// 截图URL列表
  final List<String> screenshots;

  /// 更新日志
  final String? changelog;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'name': name,
        'version': version,
        'description': description,
        'author': author,
        'homepage': homepage,
        'repository': repository,
        'license': license,
        'keywords': keywords,
        'category': category,
        'minSdkVersion': minSdkVersion,
        'maxSdkVersion': maxSdkVersion,
        'dependencies': dependencies,
        'devDependencies': devDependencies,
        'permissions': permissions,
        'platforms': platforms,
        'screenshots': screenshots,
        'changelog': changelog,
      };
}

/// 插件发布管理器
///
/// 负责插件的发布流程，包括验证、签名、打包和上传
class PluginPublisher {
  PluginPublisher._();
  static final PluginPublisher _instance = PluginPublisher._();
  static PluginPublisher get instance => _instance;

  /// 发布配置
  PublishConfig _config = const PublishConfig();

  /// 进度控制器
  final Map<String, StreamController<PublishProgress>> _progressControllers =
      <String, StreamController<PublishProgress>>{};

  /// 设置发布配置
  void setConfig(PublishConfig config) {
    _config = config;
  }

  /// 获取当前配置
  PublishConfig get config => _config;

  /// 发布插件
  ///
  /// [pluginId] 插件ID
  /// [metadata] 插件元数据
  /// [pluginData] 插件数据
  /// [config] 发布配置（可选，使用默认配置）
  Future<PublishResult> publishPlugin({
    required String pluginId,
    required PublishMetadata metadata,
    required Uint8List pluginData,
    PublishConfig? config,
  }) async {
    final publishConfig = config ?? _config;
    final progressController = StreamController<PublishProgress>.broadcast();
    _progressControllers[pluginId] = progressController;

    try {
      // 阶段1: 准备发布 (0-20%)
      progressController.add(
        const PublishProgress(
          status: PublishStatus.preparing,
          progress: 0,
          message: '准备发布插件...',
        ),
      );

      await _preparePublish(metadata, publishConfig);
      progressController.add(
        const PublishProgress(
          status: PublishStatus.preparing,
          progress: 0.2,
          message: '发布准备完成',
        ),
      );

      // 阶段2: 验证插件 (20-40%)
      if (publishConfig.enableValidation) {
        progressController.add(
          const PublishProgress(
            status: PublishStatus.validating,
            progress: 0.2,
            message: '验证插件...',
          ),
        );

        final validationResult = await _validatePlugin(metadata, pluginData);
        if (!validationResult.isValid) {
          return PublishResult.failure(
            error: '插件验证失败: ${validationResult.errors.join(', ')}',
            warnings: validationResult.warnings,
          );
        }

        progressController.add(
          const PublishProgress(
            status: PublishStatus.validating,
            progress: 0.4,
            message: '插件验证完成',
          ),
        );
      }

      // 阶段3: 签名插件 (40-60%)
      Uint8List finalPluginData = pluginData;
      if (publishConfig.enableSigning) {
        progressController.add(
          const PublishProgress(
            status: PublishStatus.signing,
            progress: 0.4,
            message: '签名插件...',
          ),
        );

        finalPluginData = await _signPlugin(metadata, pluginData);
        progressController.add(
          const PublishProgress(
            status: PublishStatus.signing,
            progress: 0.6,
            message: '插件签名完成',
          ),
        );
      }

      // 阶段4: 上传插件 (60-100%)
      progressController.add(
        const PublishProgress(
          status: PublishStatus.uploading,
          progress: 0.6,
          message: '上传插件...',
        ),
      );

      final uploadResult = await _uploadPlugin(
        metadata,
        finalPluginData,
        publishConfig,
        progressController,
      );

      progressController.add(
        const PublishProgress(
          status: PublishStatus.completed,
          progress: 1,
          message: '插件发布完成',
        ),
      );

      return uploadResult;
    } catch (e) {
      progressController.add(
        PublishProgress(
          status: PublishStatus.failed,
          progress: 0,
          message: '发布失败: $e',
        ),
      );

      return PublishResult.failure(error: '发布失败: $e');
    } finally {
      // 清理进度控制器
      await progressController.close();
      _progressControllers.remove(pluginId);
    }
  }

  /// 获取发布进度流
  Stream<PublishProgress>? getPublishProgress(String pluginId) =>
      _progressControllers[pluginId]?.stream;

  /// 准备发布
  Future<void> _preparePublish(
    PublishMetadata metadata,
    PublishConfig config,
  ) async {
    // 验证API密钥
    if (config.apiKey == null || config.apiKey!.isEmpty) {
      throw const GeneralPluginException('API密钥未配置');
    }

    // 验证插件元数据
    _validateMetadata(metadata);

    // 检查版本冲突
    await _checkVersionConflict(metadata);

    // 模拟准备时间
    await Future<void>.delayed(const Duration(milliseconds: 500));
  }

  /// 验证插件元数据
  void _validateMetadata(PublishMetadata metadata) {
    if (metadata.id.isEmpty) {
      throw const GeneralPluginException('插件ID不能为空');
    }

    if (metadata.name.isEmpty) {
      throw const GeneralPluginException('插件名称不能为空');
    }

    if (metadata.version.isEmpty) {
      throw const GeneralPluginException('插件版本不能为空');
    }

    if (metadata.description.isEmpty) {
      throw const GeneralPluginException('插件描述不能为空');
    }

    if (metadata.author.isEmpty) {
      throw const GeneralPluginException('插件作者不能为空');
    }

    // 验证版本格式 (简单的语义化版本检查)
    final versionRegex = RegExp(r'^\d+\.\d+\.\d+');
    if (!versionRegex.hasMatch(metadata.version)) {
      throw const GeneralPluginException('插件版本格式无效，应使用语义化版本 (如: 1.0.0)');
    }
  }

  /// 检查版本冲突
  Future<void> _checkVersionConflict(PublishMetadata metadata) async {
    try {
      print('开始检查版本冲突: ${metadata.id}@${metadata.version}');

      // 1. 检查本地注册表
      final localRegistry = PluginRegistry.instance;
      if (localRegistry.contains(metadata.id)) {
        final existingPlugin = localRegistry.get(metadata.id);
        if (existingPlugin != null &&
            existingPlugin.version == metadata.version) {
          throw GeneralPluginException(
            '版本冲突: 插件 ${metadata.id} 版本 ${metadata.version} 已存在于本地注册表中',
          );
        }
      }

      // 2. 检查远程注册表
      final bool remoteConflict = await checkRemoteVersionConflict(metadata);
      if (remoteConflict) {
        throw GeneralPluginException(
          '版本冲突: 插件 ${metadata.id} 版本 ${metadata.version} 已存在于远程注册表中',
        );
      }

      // 3. 检查pub.dev注册表
      final bool pubConflict = await checkPubDevVersionConflict(metadata);
      if (pubConflict) {
        throw GeneralPluginException(
          '版本冲突: 插件 ${metadata.id} 版本 ${metadata.version} 已存在于pub.dev中',
        );
      }

      print('版本冲突检查通过: ${metadata.id}@${metadata.version}');
    } catch (e) {
      print('版本冲突检查失败: ${metadata.id}@${metadata.version} - $e');
      rethrow;
    }
  }

  /// 验证插件
  Future<ValidationResult> _validatePlugin(
    PublishMetadata metadata,
    Uint8List pluginData,
  ) async {
    final errors = <String>[];
    final warnings = <String>[];

    // 验证插件大小
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (pluginData.length > maxSize) {
      errors.add('插件大小超过限制 (${pluginData.length} > $maxSize bytes)');
    }

    // 验证插件结构
    final structureValid = await _validatePluginStructure(pluginData);
    if (!structureValid) {
      errors.add('插件结构无效');
    }

    // 验证依赖
    final dependencyIssues = await _validateDependencies(metadata);
    errors.addAll(dependencyIssues);

    // 验证权限
    final permissionIssues = _validatePermissions(metadata);
    warnings.addAll(permissionIssues);

    // 模拟验证时间
    await Future<void>.delayed(const Duration(milliseconds: 800));

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证插件结构
  Future<bool> _validatePluginStructure(Uint8List pluginData) async {
    try {
      print('开始验证插件结构，数据大小: ${pluginData.length} bytes');

      // 1. 检查数据是否为空
      if (pluginData.isEmpty) {
        print('插件数据为空');
        return false;
      }

      // 2. 检查是否为有效的压缩包格式
      if (!await _isValidArchive(pluginData)) {
        print('插件数据不是有效的压缩包格式');
        return false;
      }

      // 3. 验证压缩包内容结构
      final structureValid = await _validateArchiveStructure(pluginData);
      if (!structureValid) {
        print('插件压缩包结构验证失败');
        return false;
      }

      // 4. 验证必需文件
      final requiredFilesValid = await _validateRequiredFiles(pluginData);
      if (!requiredFilesValid) {
        print('插件必需文件验证失败');
        return false;
      }

      // 5. 验证插件清单文件
      final manifestValid = await _validatePluginManifest(pluginData);
      if (!manifestValid) {
        print('插件清单文件验证失败');
        return false;
      }

      print('插件结构验证通过');
      return true;
    } catch (e) {
      print('插件结构验证过程中发生错误: $e');
      return false;
    }
  }

  /// 验证依赖
  Future<List<String>> _validateDependencies(PublishMetadata metadata) async {
    final errors = <String>[];

    // 检查依赖是否存在
    for (final dependency in metadata.dependencies.keys) {
      final exists = await _checkDependencyExists(dependency);
      if (!exists) {
        errors.add('依赖不存在: $dependency');
      }
    }

    return errors;
  }

  /// 检查依赖是否存在（真实实现）
  Future<bool> _checkDependencyExists(String dependencyId) async {
    try {
      // 1. 检查本地注册表
      final localRegistry = PluginRegistry.instance;
      if (localRegistry.contains(dependencyId)) {
        return true;
      }

      // 2. 检查远程注册表
      final remoteExists = await _checkRemoteDependency(dependencyId);
      if (remoteExists) {
        return true;
      }

      // 3. 检查pub.dev
      final pubExists = await _checkPubDevDependency(dependencyId);
      if (pubExists) {
        return true;
      }

      // 4. 检查GitHub注册表
      final githubExists = await _checkGitHubDependency(dependencyId);
      return githubExists;
    } catch (e) {
      print('检查依赖存在性失败: $dependencyId - $e');
      return false;
    }
  }

  /// 验证权限
  List<String> _validatePermissions(PublishMetadata metadata) {
    final warnings = <String>[];

    // 检查敏感权限
    const sensitivePermissions = <String>[
      'file_system',
      'network',
      'camera',
      'microphone',
      'location',
    ];

    for (final permission in metadata.permissions) {
      if (sensitivePermissions.contains(permission)) {
        warnings.add('使用了敏感权限: $permission');
      }
    }

    return warnings;
  }

  /// 签名插件 (集成Ming CLI数字签名功能)
  Future<Uint8List> _signPlugin(
    PublishMetadata metadata,
    Uint8List pluginData,
  ) async {
    try {
      print('开始签名插件: ${metadata.id}');

      // 使用集成的数字签名系统
      final PluginSignature signature = PluginSignature.instance;

      // 签名插件数据
      final Uint8List signedData = await signature.signPlugin(
        pluginData,
        attributes: <String, dynamic>{
          'plugin_id': metadata.id,
          'plugin_name': metadata.name,
          'plugin_version': metadata.version,
          'plugin_author': metadata.author,
          'signed_by': 'plugin-publisher',
          'signed_at': DateTime.now().toIso8601String(),
        },
      );

      print('插件签名完成: ${metadata.id}');
      return signedData;
    } catch (e) {
      print('插件签名失败: ${metadata.id}, 错误: $e');
      throw GeneralPluginException('插件签名失败: $e');
    }
  }

  /// 上传插件
  Future<PublishResult> _uploadPlugin(
    PublishMetadata metadata,
    Uint8List pluginData,
    PublishConfig config,
    StreamController<PublishProgress> progressController,
  ) async {
    // 模拟上传过程
    const totalSteps = 10;
    for (int i = 1; i <= totalSteps; i++) {
      await Future<void>.delayed(const Duration(milliseconds: 200));
      final progress = 0.6 + (0.4 * i / totalSteps);
      progressController.add(
        PublishProgress(
          status: PublishStatus.uploading,
          progress: progress,
          message: '上传进度: ${(progress * 100).toInt()}%',
        ),
      );
    }

    // 生成发布结果
    final publishId = _generatePublishId(metadata);
    final downloadUrl =
        '${config.registryUrl}/plugins/${metadata.id}/${metadata.version}';

    return PublishResult.success(
      publishId: publishId,
      downloadUrl: downloadUrl,
      version: metadata.version,
    );
  }

  /// 生成发布ID
  String _generatePublishId(PublishMetadata metadata) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${metadata.id}_${metadata.version}_$timestamp';
  }

  /// 清理资源
  void dispose() {
    for (final controller in _progressControllers.values) {
      controller.close();
    }
    _progressControllers.clear();
  }

  /// 检查远程注册表中的依赖
  Future<bool> _checkRemoteDependency(String dependencyId) async {
    try {
      // 获取私有注册表配置
      final config = await _getPrivateRegistryConfig();
      if (config == null) {
        return false;
      }

      final checkUrl = '${config['url']}/api/plugins/$dependencyId';
      final response = await http.get(
        Uri.parse(checkUrl),
        headers: <String, String>{
          'Content-Type': 'application/json',
          if (config['auth_type'] == 'token')
            'Authorization': 'Bearer ${config['auth_token']}',
          if (config['auth_type'] == 'apikey')
            'X-API-Key': '${config['api_key']}',
        },
      ).timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      print('检查远程依赖失败: $dependencyId - $e');
      return false;
    }
  }

  /// 检查pub.dev中的依赖
  Future<bool> _checkPubDevDependency(String dependencyId) async {
    try {
      final pubUrl = 'https://pub.dev/api/packages/$dependencyId';
      final response = await http.get(
        Uri.parse(pubUrl),
        headers: <String, String>{'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return data['name'] == dependencyId;
      }
      return false;
    } catch (e) {
      print('检查pub.dev依赖失败: $dependencyId - $e');
      return false;
    }
  }

  /// 检查GitHub注册表中的依赖
  Future<bool> _checkGitHubDependency(String dependencyId) async {
    try {
      final githubConfig = await _getGitHubConfiguration();
      if (githubConfig == null) {
        return false;
      }

      // 检查GitHub Releases
      final releasesUrl =
          '${githubConfig['baseUrl']}/repos/${githubConfig['owner']}/${githubConfig['repo']}/releases';
      final response = await http.get(
        Uri.parse(releasesUrl),
        headers: <String, String>{
          'Authorization': 'token ${githubConfig['token']}',
          'Accept': 'application/vnd.github.v3+json',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final releases = jsonDecode(response.body) as List;
        return releases.any(
          (release) =>
              (release['tag_name'] as String).contains(dependencyId) ||
              (release['name'] as String).contains(dependencyId),
        );
      }
      return false;
    } catch (e) {
      print('检查GitHub依赖失败: $dependencyId - $e');
      return false;
    }
  }

  /// 获取私有注册表配置（真实配置读取）
  Future<Map<String, dynamic>?> _getPrivateRegistryConfig() async {
    try {
      // 1. 尝试从环境变量读取配置
      final registryUrl = Platform.environment['PRIVATE_REGISTRY_URL'];
      final authType = Platform.environment['PRIVATE_REGISTRY_AUTH_TYPE'];
      final authToken = Platform.environment['PRIVATE_REGISTRY_AUTH_TOKEN'];
      final apiKey = Platform.environment['PRIVATE_REGISTRY_API_KEY'];
      final uploadEndpoint =
          Platform.environment['PRIVATE_REGISTRY_UPLOAD_ENDPOINT'];

      if (registryUrl != null && authType != null) {
        final config = <String, Object>{
          'url': registryUrl,
          'auth_type': authType,
          'upload_endpoint': uploadEndpoint ?? '/api/v1/packages/upload',
          'timeout': 30000,
          'retry_count': 3,
        };

        // 根据认证类型添加相应的认证信息
        switch (authType) {
          case 'token':
            if (authToken != null) {
              config['auth_token'] = authToken;
            }
          case 'apikey':
            if (apiKey != null) {
              config['api_key'] = apiKey;
            }
        }

        return config;
      }

      // 2. 尝试从配置文件读取
      final configFile = File('private_registry.json');
      if (await configFile.exists()) {
        final configContent = await configFile.readAsString();
        final config = jsonDecode(configContent) as Map<String, dynamic>;

        if (config['url'] != null && config['auth_type'] != null) {
          return config;
        }
      }

      // 3. 尝试从用户配置目录读取
      final homeDir =
          Platform.environment['HOME'] ?? Platform.environment['USERPROFILE'];
      if (homeDir != null) {
        final userConfigFile =
            File(path.join(homeDir, '.ming', 'private_registry.json'));
        if (await userConfigFile.exists()) {
          final configContent = await userConfigFile.readAsString();
          final config = jsonDecode(configContent) as Map<String, dynamic>;

          if (config['url'] != null && config['auth_type'] != null) {
            return config;
          }
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// 获取GitHub配置
  Future<Map<String, dynamic>?> _getGitHubConfiguration() async {
    try {
      // 尝试从环境变量读取
      final token = Platform.environment['GITHUB_TOKEN'];
      final owner = Platform.environment['GITHUB_OWNER'];
      final repo = Platform.environment['GITHUB_REPO'];

      if (token != null && owner != null && repo != null) {
        return <String, dynamic>{
          'token': token,
          'owner': owner,
          'repo': repo,
          'baseUrl': 'https://api.github.com',
        };
      }

      // 尝试从配置文件读取
      final configFile = File('github_config.json');
      if (await configFile.exists()) {
        final configContent = await configFile.readAsString();
        final config = jsonDecode(configContent) as Map<String, dynamic>;

        if (config['token'] != null &&
            config['owner'] != null &&
            config['repo'] != null) {
          return config;
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }
}

/// 验证结果
class ValidationResult {
  const ValidationResult({
    required this.isValid,
    this.errors = const <String>[],
    this.warnings = const <String>[],
  });

  /// 是否有效
  final bool isValid;

  /// 错误列表
  final List<String> errors;

  /// 警告列表
  final List<String> warnings;
}

/// PluginPublisher扩展方法
extension PluginPublisherExtension on PluginPublisher {
  /// 检查远程注册表版本冲突
  Future<bool> checkRemoteVersionConflict(PublishMetadata metadata) async {
    try {
      // 获取私有注册表配置
      final config = await _getPrivateRegistryConfig();
      if (config == null) {
        return false;
      }

      final checkUrl =
          '${config['url']}/api/plugins/${metadata.id}/versions/${metadata.version}';
      final response = await http.get(
        Uri.parse(checkUrl),
        headers: <String, String>{
          'Content-Type': 'application/json',
          if (config['auth_type'] == 'token')
            'Authorization': 'Bearer ${config['auth_token']}',
          if (config['auth_type'] == 'apikey')
            'X-API-Key': '${config['api_key']}',
        },
      ).timeout(const Duration(seconds: 10));

      // 如果返回200，说明版本已存在
      return response.statusCode == 200;
    } catch (e) {
      print('检查远程版本冲突失败: ${metadata.id}@${metadata.version} - $e');
      // 网络错误时假设不冲突
      return false;
    }
  }

  /// 检查pub.dev版本冲突
  Future<bool> checkPubDevVersionConflict(PublishMetadata metadata) async {
    try {
      // 查询pub.dev API检查包是否存在
      final checkUrl = 'https://pub.dev/api/packages/${metadata.id}';
      final response = await http.get(
        Uri.parse(checkUrl),
        headers: <String, String>{
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        // 包存在，检查版本
        final packageInfo = jsonDecode(response.body) as Map<String, dynamic>;
        final versions = packageInfo['versions'] as List<dynamic>?;

        if (versions != null) {
          for (final version in versions) {
            final versionInfo = version as Map<String, dynamic>;
            if (versionInfo['version'] == metadata.version) {
              return true; // 版本冲突
            }
          }
        }
      }

      return false; // 无冲突
    } catch (e) {
      print('检查pub.dev版本冲突失败: ${metadata.id}@${metadata.version} - $e');
      // 网络错误时假设不冲突
      return false;
    }
  }

  /// 检查是否为有效的压缩包格式
  Future<bool> _isValidArchive(Uint8List data) async {
    try {
      // 检查ZIP文件头
      if (data.length >= 4) {
        // ZIP文件魔数: 0x504B0304 (PK..)
        if (data[0] == 0x50 &&
            data[1] == 0x4B &&
            (data[2] == 0x03 || data[2] == 0x05 || data[2] == 0x07)) {
          return true;
        }
      }

      // 检查TAR.GZ文件头
      if (data.length >= 3) {
        // GZIP文件魔数: 0x1F8B
        if (data[0] == 0x1F && data[1] == 0x8B) {
          return true;
        }
      }

      // 检查7Z文件头
      if (data.length >= 6) {
        // 7Z文件魔数: 0x377ABCAF271C
        if (data[0] == 0x37 &&
            data[1] == 0x7A &&
            data[2] == 0xBC &&
            data[3] == 0xAF &&
            data[4] == 0x27 &&
            data[5] == 0x1C) {
          return true;
        }
      }

      return false;
    } catch (e) {
      print('检查压缩包格式失败: $e');
      return false;
    }
  }

  /// 验证压缩包结构
  Future<bool> _validateArchiveStructure(Uint8List data) async {
    try {
      // 模拟解压缩和结构验证
      // 在真实实现中，这里会使用archive库解压缩并检查结构
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // 检查数据完整性
      if (data.length < 1024) {
        // 至少1KB
        return false;
      }

      // 模拟结构检查通过
      return true;
    } catch (e) {
      print('验证压缩包结构失败: $e');
      return false;
    }
  }

  /// 验证必需文件
  Future<bool> _validateRequiredFiles(Uint8List data) async {
    try {
      // 模拟检查必需文件
      // 在真实实现中，这里会解压缩并检查以下文件是否存在：
      // - plugin.yaml (插件清单)
      // - lib/main.dart (主入口文件)
      // - pubspec.yaml (Dart包配置)
      // - README.md (说明文档)
      await Future<void>.delayed(const Duration(milliseconds: 150));

      // 模拟必需文件检查通过
      return true;
    } catch (e) {
      print('验证必需文件失败: $e');
      return false;
    }
  }

  /// 验证插件清单文件
  Future<bool> _validatePluginManifest(Uint8List data) async {
    try {
      // 模拟解析plugin.yaml文件
      // 在真实实现中，这里会：
      // 1. 从压缩包中提取plugin.yaml
      // 2. 解析YAML内容
      // 3. 验证必需字段（id, name, version, description等）
      // 4. 验证字段格式和约束
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // 模拟清单文件验证通过
      return true;
    } catch (e) {
      print('验证插件清单文件失败: $e');
      return false;
    }
  }
}
