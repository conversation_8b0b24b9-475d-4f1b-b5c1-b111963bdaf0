/*
---------------------------------------------------------------
File name:          advanced_memory_pool.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        高级内存池调优器 - Phase 5.1 高级性能优化
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 5.1 - 高级内存池调优实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:collection';
import 'dart:math' as math;

/// 内存池策略
enum MemoryPoolStrategy {
  /// 固定大小池
  fixedSize,

  /// 动态扩展池
  dynamicExpansion,

  /// 分层池
  tiered,

  /// 分代池
  generational,

  /// 自适应池
  adaptive,
}

/// 内存分配策略
enum AllocationStrategy {
  /// 首次适应
  firstFit,

  /// 最佳适应
  bestFit,

  /// 最差适应
  worstFit,

  /// 快速适应
  quickFit,

  /// 伙伴系统
  buddySystem,
}

/// 垃圾回收策略
enum GarbageCollectionStrategy {
  /// 标记清除
  markAndSweep,

  /// 复制回收
  copying,

  /// 分代回收
  generational,

  /// 增量回收
  incremental,

  /// 并发回收
  concurrent,
}

/// 内存块
class MemoryBlock {
  MemoryBlock({
    required this.id,
    required this.size,
    required this.data,
    this.generation = 0,
  })  : createdAt = DateTime.now(),
        lastAccessedAt = DateTime.now();

  /// 块ID
  final String id;

  /// 块大小
  final int size;

  /// 数据
  final dynamic data;

  /// 代数
  int generation;

  /// 创建时间
  final DateTime createdAt;

  /// 最后访问时间
  DateTime lastAccessedAt;

  /// 访问次数
  int accessCount = 0;

  /// 是否被标记
  bool isMarked = false;

  /// 更新访问信息
  void updateAccess() {
    lastAccessedAt = DateTime.now();
    accessCount++;
  }

  /// 获取年龄（毫秒）
  int get ageInMilliseconds =>
      DateTime.now().difference(createdAt).inMilliseconds;

  /// 获取空闲时间（毫秒）
  int get idleTimeInMilliseconds =>
      DateTime.now().difference(lastAccessedAt).inMilliseconds;
}

/// 内存池统计
class MemoryPoolStats {
  const MemoryPoolStats({
    required this.totalSize,
    required this.usedSize,
    required this.freeSize,
    required this.blockCount,
    required this.allocationCount,
    required this.deallocationCount,
    required this.hitRate,
    required this.fragmentationRate,
    required this.gcCount,
    required this.avgAllocationTime,
  });

  /// 总大小
  final int totalSize;

  /// 已使用大小
  final int usedSize;

  /// 空闲大小
  final int freeSize;

  /// 块数量
  final int blockCount;

  /// 分配次数
  final int allocationCount;

  /// 释放次数
  final int deallocationCount;

  /// 命中率
  final double hitRate;

  /// 碎片率
  final double fragmentationRate;

  /// GC次数
  final int gcCount;

  /// 平均分配时间（微秒）
  final double avgAllocationTime;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'totalSize': totalSize,
        'usedSize': usedSize,
        'freeSize': freeSize,
        'blockCount': blockCount,
        'allocationCount': allocationCount,
        'deallocationCount': deallocationCount,
        'hitRate': hitRate,
        'fragmentationRate': fragmentationRate,
        'gcCount': gcCount,
        'avgAllocationTime': avgAllocationTime,
      };
}

/// 高级内存池
class AdvancedMemoryPool<T> {
  AdvancedMemoryPool({
    required this.name,
    required this.factory,
    required this.reset,
    this.strategy = MemoryPoolStrategy.adaptive,
    this.allocationStrategy = AllocationStrategy.bestFit,
    this.gcStrategy = GarbageCollectionStrategy.generational,
    this.initialSize = 10,
    this.maxSize = 1000,
    this.growthFactor = 1.5,
    this.shrinkThreshold = 0.25,
    this.maxIdleTime = const Duration(minutes: 5),
    this.gcThreshold = 0.8,
    this.enableStatistics = true,
  });

  /// 池名称
  final String name;

  /// 对象工厂
  final T Function() factory;

  /// 对象重置函数
  final void Function(T) reset;

  /// 内存池策略
  final MemoryPoolStrategy strategy;

  /// 分配策略
  final AllocationStrategy allocationStrategy;

  /// 垃圾回收策略
  final GarbageCollectionStrategy gcStrategy;

  /// 初始大小
  final int initialSize;

  /// 最大大小
  final int maxSize;

  /// 增长因子
  final double growthFactor;

  /// 收缩阈值
  final double shrinkThreshold;

  /// 最大空闲时间
  final Duration maxIdleTime;

  /// GC阈值
  final double gcThreshold;

  /// 启用统计
  final bool enableStatistics;

  /// 内存块映射
  final Map<String, MemoryBlock> _blocks = <String, MemoryBlock>{};

  /// 空闲块队列
  final Queue<MemoryBlock> _freeBlocks = Queue<MemoryBlock>();

  /// 使用中的块
  final Set<MemoryBlock> _usedBlocks = <MemoryBlock>{};

  /// 分层池（按大小分层）
  final Map<int, Queue<MemoryBlock>> _tieredPools = <int, Queue<MemoryBlock>>{};

  /// 分代池（按年龄分代）
  final Map<int, Set<MemoryBlock>> _generationalPools =
      <int, Set<MemoryBlock>>{};

  /// 统计信息
  int _allocationCount = 0;
  int _deallocationCount = 0;
  int _hitCount = 0;
  int _missCount = 0;
  int _gcCount = 0;
  final List<int> _allocationTimes = <int>[];

  /// 分配对象
  T allocate({int? size}) {
    final startTime = DateTime.now().microsecondsSinceEpoch;

    try {
      final block = _allocateBlock(size ?? 1);
      final obj = block.data as T;

      if (enableStatistics) {
        _allocationCount++;
        _hitCount++;
        final allocationTime =
            DateTime.now().microsecondsSinceEpoch - startTime;
        _allocationTimes.add(allocationTime);
      }

      return obj;
    } catch (e) {
      // 分配失败，创建新对象
      final obj = factory();

      if (enableStatistics) {
        _allocationCount++;
        _missCount++;
        final allocationTime =
            DateTime.now().microsecondsSinceEpoch - startTime;
        _allocationTimes.add(allocationTime);
      }

      return obj;
    }
  }

  /// 释放对象
  void deallocate(T obj) {
    final block = _findBlockByData(obj);
    if (block != null) {
      _deallocateBlock(block);

      if (enableStatistics) {
        _deallocationCount++;
      }
    }
  }

  /// 分配内存块
  MemoryBlock _allocateBlock(int size) {
    MemoryBlock? block;

    switch (strategy) {
      case MemoryPoolStrategy.fixedSize:
        block = _allocateFromFixedPool();
      case MemoryPoolStrategy.dynamicExpansion:
        block = _allocateFromDynamicPool(size);
      case MemoryPoolStrategy.tiered:
        block = _allocateFromTieredPool(size);
      case MemoryPoolStrategy.generational:
        block = _allocateFromGenerationalPool();
      case MemoryPoolStrategy.adaptive:
        block = _allocateFromAdaptivePool(size);
    }

    if (block == null) {
      throw StateError('Unable to allocate memory block');
    }

    block.updateAccess();
    _usedBlocks.add(block);
    _freeBlocks.remove(block);

    return block;
  }

  /// 释放内存块
  void _deallocateBlock(MemoryBlock block) {
    _usedBlocks.remove(block);

    // 重置对象
    reset(block.data as T);

    // 根据策略决定是否回收
    if (_shouldRecycleBlock(block)) {
      _freeBlocks.add(block);

      // 更新分代信息
      if (strategy == MemoryPoolStrategy.generational) {
        _updateGenerationalInfo(block);
      }
    } else {
      _blocks.remove(block.id);
    }
  }

  /// 从固定池分配
  MemoryBlock? _allocateFromFixedPool() {
    if (_freeBlocks.isNotEmpty) {
      return _freeBlocks.removeFirst();
    }

    if (_blocks.length < maxSize) {
      return _createNewBlock();
    }

    return null;
  }

  /// 从动态池分配
  MemoryBlock? _allocateFromDynamicPool(int size) {
    // 查找合适大小的块
    MemoryBlock? bestBlock;

    switch (allocationStrategy) {
      case AllocationStrategy.firstFit:
        bestBlock = _findFirstFitBlock(size);
      case AllocationStrategy.bestFit:
        bestBlock = _findBestFitBlock(size);
      case AllocationStrategy.worstFit:
        bestBlock = _findWorstFitBlock(size);
      case AllocationStrategy.quickFit:
        bestBlock = _findQuickFitBlock(size);
      case AllocationStrategy.buddySystem:
        bestBlock = _findBuddyBlock(size);
    }

    return bestBlock ?? _createNewBlock(size: size);
  }

  /// 从分层池分配
  MemoryBlock? _allocateFromTieredPool(int size) {
    final tier = _getTierForSize(size);
    final tierPool = _tieredPools[tier];

    if (tierPool != null && tierPool.isNotEmpty) {
      return tierPool.removeFirst();
    }

    return _createNewBlock(size: size);
  }

  /// 从分代池分配
  MemoryBlock? _allocateFromGenerationalPool() {
    // 优先从年轻代分配
    for (int generation = 0; generation < 3; generation++) {
      final generationPool = _generationalPools[generation];
      if (generationPool != null && generationPool.isNotEmpty) {
        final block = generationPool.first;
        generationPool.remove(block);
        return block;
      }
    }

    return _createNewBlock();
  }

  /// 从自适应池分配
  MemoryBlock? _allocateFromAdaptivePool(int size) {
    // 根据当前使用情况选择最佳策略
    final usageRate = _usedBlocks.length / maxSize;

    if (usageRate < 0.5) {
      return _allocateFromFixedPool();
    } else if (usageRate < 0.8) {
      return _allocateFromDynamicPool(size);
    } else {
      return _allocateFromGenerationalPool();
    }
  }

  /// 创建新块
  MemoryBlock _createNewBlock({int size = 1}) {
    final id = 'block_${DateTime.now().microsecondsSinceEpoch}';
    final data = factory();
    final block = MemoryBlock(id: id, size: size, data: data);

    _blocks[id] = block;
    return block;
  }

  /// 查找最佳适应块
  MemoryBlock? _findBestFitBlock(int size) {
    MemoryBlock? bestBlock;
    int minWaste = double.maxFinite.toInt();

    for (final block in _freeBlocks) {
      if (block.size >= size) {
        final waste = block.size - size;
        if (waste < minWaste) {
          minWaste = waste;
          bestBlock = block;
        }
      }
    }

    return bestBlock;
  }

  /// 查找首次适应块
  MemoryBlock? _findFirstFitBlock(int size) {
    for (final block in _freeBlocks) {
      if (block.size >= size) {
        return block;
      }
    }
    return null;
  }

  /// 查找最差适应块
  MemoryBlock? _findWorstFitBlock(int size) {
    MemoryBlock? worstBlock;
    int maxWaste = -1;

    for (final block in _freeBlocks) {
      if (block.size >= size) {
        final waste = block.size - size;
        if (waste > maxWaste) {
          maxWaste = waste;
          worstBlock = block;
        }
      }
    }

    return worstBlock;
  }

  /// 查找快速适应块
  MemoryBlock? _findQuickFitBlock(int size) {
    // 简化实现：查找精确匹配
    for (final block in _freeBlocks) {
      if (block.size == size) {
        return block;
      }
    }
    return _findFirstFitBlock(size);
  }

  /// 查找伙伴块
  MemoryBlock? _findBuddyBlock(int size) {
    // 简化的伙伴系统实现
    final powerOfTwo = _nextPowerOfTwo(size);
    return _findFirstFitBlock(powerOfTwo);
  }

  /// 获取大小对应的层级
  int _getTierForSize(int size) {
    if (size <= 8) return 0;
    if (size <= 16) return 1;
    if (size <= 32) return 2;
    if (size <= 64) return 3;
    return 4;
  }

  /// 更新分代信息
  void _updateGenerationalInfo(MemoryBlock block) {
    final currentGeneration = block.generation;
    final int newGeneration = math.min(currentGeneration + 1, 2);

    _generationalPools[currentGeneration]?.remove(block);
    block.generation = newGeneration;
    _generationalPools[newGeneration] ??= <MemoryBlock>{};
    _generationalPools[newGeneration]!.add(block);
  }

  /// 是否应该回收块
  bool _shouldRecycleBlock(MemoryBlock block) {
    // 检查空闲时间
    if (block.idleTimeInMilliseconds > maxIdleTime.inMilliseconds) {
      return false;
    }

    // 检查池大小
    if (_freeBlocks.length >= maxSize * shrinkThreshold) {
      return false;
    }

    return true;
  }

  /// 根据数据查找块
  MemoryBlock? _findBlockByData(T data) {
    for (final block in _usedBlocks) {
      if (identical(block.data, data)) {
        return block;
      }
    }
    return null;
  }

  /// 计算下一个2的幂
  int _nextPowerOfTwo(int n) {
    int power = 1;
    while (power < n) {
      power *= 2;
    }
    return power;
  }

  /// 执行垃圾回收
  Future<void> performGarbageCollection() async {
    switch (gcStrategy) {
      case GarbageCollectionStrategy.markAndSweep:
        await _markAndSweepGC();
      case GarbageCollectionStrategy.copying:
        await _copyingGC();
      case GarbageCollectionStrategy.generational:
        await _generationalGC();
      case GarbageCollectionStrategy.incremental:
        await _incrementalGC();
      case GarbageCollectionStrategy.concurrent:
        await _concurrentGC();
    }

    _gcCount++;
  }

  /// 标记清除GC
  Future<void> _markAndSweepGC() async {
    // 标记阶段
    for (final block in _usedBlocks) {
      block.isMarked = true;
    }

    // 清除阶段
    final toRemove = <MemoryBlock>[];
    for (final block in _freeBlocks) {
      if (!block.isMarked &&
          block.idleTimeInMilliseconds > maxIdleTime.inMilliseconds) {
        toRemove.add(block);
      }
    }

    for (final block in toRemove) {
      _freeBlocks.remove(block);
      _blocks.remove(block.id);
    }

    // 重置标记
    for (final block in _blocks.values) {
      block.isMarked = false;
    }
  }

  /// 复制GC
  Future<void> _copyingGC() async {
    final newFreeBlocks = Queue<MemoryBlock>();

    for (final block in _freeBlocks) {
      if (block.idleTimeInMilliseconds <= maxIdleTime.inMilliseconds) {
        newFreeBlocks.add(block);
      } else {
        _blocks.remove(block.id);
      }
    }

    _freeBlocks.clear();
    _freeBlocks.addAll(newFreeBlocks);
  }

  /// 分代GC
  Future<void> _generationalGC() async {
    // 清理老年代
    final oldGeneration = _generationalPools[2];
    if (oldGeneration != null) {
      final toRemove = <MemoryBlock>[];
      for (final block in oldGeneration) {
        if (block.idleTimeInMilliseconds > maxIdleTime.inMilliseconds) {
          toRemove.add(block);
        }
      }

      for (final block in toRemove) {
        oldGeneration.remove(block);
        _freeBlocks.remove(block);
        _blocks.remove(block.id);
      }
    }
  }

  /// 增量GC
  Future<void> _incrementalGC() async {
    const batchSize = 10;
    final blocksToCheck = _freeBlocks.take(batchSize).toList();

    for (final block in blocksToCheck) {
      if (block.idleTimeInMilliseconds > maxIdleTime.inMilliseconds) {
        _freeBlocks.remove(block);
        _blocks.remove(block.id);
      }
    }
  }

  /// 并发GC
  Future<void> _concurrentGC() async {
    // 简化的并发GC实现
    await Future<void>.delayed(const Duration(milliseconds: 1));
    await _markAndSweepGC();
  }

  /// 获取统计信息
  MemoryPoolStats getStats() {
    final totalSize = _blocks.length;
    final usedSize = _usedBlocks.length;
    final freeSize = _freeBlocks.length;
    final hitRate = (_hitCount + _missCount) > 0
        ? _hitCount / (_hitCount + _missCount)
        : 0.0;
    final fragmentationRate =
        totalSize > 0 ? (totalSize - usedSize - freeSize) / totalSize : 0.0;
    final avgAllocationTime = _allocationTimes.isNotEmpty
        ? _allocationTimes.reduce((int a, int b) => a + b) /
            _allocationTimes.length
        : 0.0;

    return MemoryPoolStats(
      totalSize: totalSize,
      usedSize: usedSize,
      freeSize: freeSize,
      blockCount: _blocks.length,
      allocationCount: _allocationCount,
      deallocationCount: _deallocationCount,
      hitRate: hitRate,
      fragmentationRate: fragmentationRate,
      gcCount: _gcCount,
      avgAllocationTime: avgAllocationTime,
    );
  }

  /// 清理资源
  Future<void> dispose() async {
    await performGarbageCollection();
    _blocks.clear();
    _freeBlocks.clear();
    _usedBlocks.clear();
    _tieredPools.clear();
    _generationalPools.clear();
    _allocationTimes.clear();
  }
}
