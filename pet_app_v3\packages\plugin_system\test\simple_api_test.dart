/*
---------------------------------------------------------------
File name:          simple_api_test.dart
Author:             lgnorant-lu
Date created:       2025/07/28
Last modified:      2025/07/28
Dart Version:       3.2+
Description:        简化的API接口测试
---------------------------------------------------------------
Change History:
    2025/07/28: Initial creation - 简化的API接口测试;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:plugin_system/src/api/plugin_api_interface.dart';

void main() {
  group('PluginApiInterface', () {
    group('基础功能测试', () {
      test('应该创建API请求对象', () {
        const request = ApiRequest(
          method: HttpMethod.get,
          path: '/api/plugins',
          version: ApiVersion(major: 1, minor: 0, patch: 0),
          headers: {},
          queryParameters: {},
          body: null,
        );

        expect(request, isNotNull);
        expect(request.method, equals(HttpMethod.get));
        expect(request.path, equals('/api/plugins'));
      });

      test('应该创建API响应对象', () {
        final response = ApiResponse.success(data: {'test': 'data'});

        expect(response, isNotNull);
        expect(response.isSuccess, isTrue);
        expect(response.data, equals({'test': 'data'}));
      });

      test('应该创建错误响应对象', () {
        final response = ApiResponse.error<Map<String, dynamic>>(
          message: 'Test error',
          statusCode: 404,
        );

        expect(response, isNotNull);
        expect(response.isSuccess, isFalse);
        expect(response.message, equals('Test error'));
        expect(response.statusCode, equals(404));
      });

      test('应该创建API版本对象', () {
        const version = ApiVersion(major: 1, minor: 0, patch: 0);

        expect(version, isNotNull);
        expect(version.major, equals(1));
        expect(version.minor, equals(0));
        expect(version.patch, equals(0));
      });

      test('应该测试HTTP方法枚举', () {
        expect(HttpMethod.get, isNotNull);
        expect(HttpMethod.post, isNotNull);
        expect(HttpMethod.put, isNotNull);
        expect(HttpMethod.delete, isNotNull);
      });
    });

    group('HTTP方法测试', () {
      test('应该包含所有HTTP方法', () {
        expect(HttpMethod.values, contains(HttpMethod.get));
        expect(HttpMethod.values, contains(HttpMethod.post));
        expect(HttpMethod.values, contains(HttpMethod.put));
        expect(HttpMethod.values, contains(HttpMethod.delete));
        expect(HttpMethod.values, contains(HttpMethod.patch));
        expect(HttpMethod.values, contains(HttpMethod.head));
        expect(HttpMethod.values, contains(HttpMethod.options));
      });
    });

    group('API响应测试', () {
      test('应该正确序列化为JSON', () {
        final response =
            ApiResponse.success<Map<String, String>>(data: {'key': 'value'});
        final Map<String, dynamic> json = response.toJson();

        expect(json, isA<Map<String, dynamic>>());
        expect(json['status'], equals('success'));
        expect(json['data'], equals({'key': 'value'}));
      });

      test('应该正确处理错误响应', () {
        final response = ApiResponse.error<Map<String, dynamic>>(
          message: 'Error message',
          statusCode: 500,
        );

        expect(response.isSuccess, isFalse);
        expect(response.message, equals('Error message'));
        expect(response.statusCode, equals(500));
        expect(response.data, isNull);
      });
    });

    group('API版本测试', () {
      test('应该正确比较版本', () {
        const v1 = ApiVersion(major: 1, minor: 0, patch: 0);
        const v2 = ApiVersion(major: 1, minor: 0, patch: 1);
        const v3 = ApiVersion(major: 1, minor: 1, patch: 0);

        expect(v1.toString(), equals('1.0.0'));
        expect(v2.toString(), equals('1.0.1'));
        expect(v3.toString(), equals('1.1.0'));
      });
    });
  });
}
