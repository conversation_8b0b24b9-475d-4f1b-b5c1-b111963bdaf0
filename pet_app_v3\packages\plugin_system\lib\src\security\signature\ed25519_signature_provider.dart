/*
---------------------------------------------------------------
File name:          ed25519_signature_provider.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        Ed25519数字签名提供者实现
---------------------------------------------------------------
Change History:
    2025-07-28:     初始实现Ed25519数字签名提供者;
---------------------------------------------------------------
*/

import 'dart:math';
import 'dart:typed_data';

import 'package:crypto/crypto.dart' as crypto;
import 'package:pointycastle/export.dart';

import 'package:plugin_system/src/core/plugin_exceptions.dart';
import 'package:plugin_system/src/security/signature/plugin_signature_core.dart';

/// Ed25519签名提供者
/// TODO(ed25519_signature): 需要集成专门的Ed25519库(如cryptography包)来实现真实的Ed25519签名
/// 当前实现使用SHA-512作为替代方案，不是真正的Ed25519算法
class Ed25519SignatureProvider implements SignatureProvider {
  /// 构造函数
  Ed25519SignatureProvider() {
    _initializeSecureRandom();
  }

  @override
  PluginSignatureAlgorithm get algorithm => PluginSignatureAlgorithm.ed25519;

  /// 密钥对缓存
  final Map<String, Map<String, Uint8List>> _keyPairs =
      <String, Map<String, Uint8List>>{};

  /// 安全随机数生成器
  late final SecureRandom _secureRandom;

  /// 初始化安全随机数生成器
  void _initializeSecureRandom() {
    _secureRandom = SecureRandom('Fortuna');
    final seed = Uint8List(32);
    final random = Random.secure();
    for (int i = 0; i < seed.length; i++) {
      seed[i] = random.nextInt(256);
    }
    _secureRandom.seed(KeyParameter(seed));
  }

  @override
  Future<Uint8List> generateSignature(
    Uint8List data,
    String? privateKeyPath,
  ) async {
    try {
      // 计算数据哈希
      final hash = crypto.sha256.convert(data);

      // 获取或生成密钥对
      final keyPair = await _getKeyPair(privateKeyPath);
      final privateKey = keyPair['private']!;

      // 使用SHA-512作为Ed25519的替代实现
      // TODO(ed25519_signature): 实现真正的Ed25519算法
      final sha512 = SHA512Digest();
      final keyData = Uint8List.fromList([
        ...hash.bytes,
        ...privateKey,
      ]);

      final output = Uint8List(64);
      sha512.doFinal(keyData, 0);
      sha512.doFinal(output, 0);

      return output;
    } catch (e) {
      throw PluginSignatureException(
        'Ed25519 signature generation failed: $e',
      );
    }
  }

  @override
  Future<bool> verifySignature(
    Uint8List signature,
    Uint8List data,
    String? publicKeyPath,
  ) async {
    try {
      // 使用与生成相同的方法来验证
      final expectedSignature = await generateSignature(data, publicKeyPath);

      // 比较签名
      if (signature.length != expectedSignature.length) {
        return false;
      }

      for (int i = 0; i < signature.length; i++) {
        if (signature[i] != expectedSignature[i]) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> generateKeyPair(String keyPath) async {
    try {
      // 生成32字节的私钥
      final privateKey = Uint8List(32);
      for (int i = 0; i < 32; i++) {
        privateKey[i] = _secureRandom.nextUint8();
      }

      // 生成32字节的公钥（简化实现）
      // TODO(ed25519_signature): 实现真正的Ed25519公钥生成
      final publicKey = Uint8List(32);
      final sha256 = SHA256Digest();
      sha256.doFinal(privateKey, 0);
      sha256.doFinal(publicKey, 0);

      // 缓存密钥对
      _keyPairs[keyPath] = <String, Uint8List>{
        'private': privateKey,
        'public': publicKey,
      };
    } catch (e) {
      throw PluginSignatureException(
        'Ed25519 key pair generation failed: $e',
      );
    }
  }

  /// 获取或生成密钥对
  Future<Map<String, Uint8List>> _getKeyPair(String? keyPath) async {
    final keyId = keyPath ?? 'default';

    // 检查缓存
    if (_keyPairs.containsKey(keyId)) {
      return _keyPairs[keyId]!;
    }

    // 生成新的密钥对
    await generateKeyPair(keyId);
    return _keyPairs[keyId]!;
  }

  /// 清理缓存
  void clearCache() {
    _keyPairs.clear();
  }

  /// 获取公钥
  Future<Uint8List?> getPublicKey(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      return keyPair['public'];
    } catch (e) {
      return null;
    }
  }

  /// 获取私钥
  Future<Uint8List?> getPrivateKey(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      return keyPair['private'];
    } catch (e) {
      return null;
    }
  }

  /// 验证密钥对是否匹配
  Future<bool> verifyKeyPairMatch(String? keyPath) async {
    try {
      // 使用测试数据验证密钥对是否匹配
      final testData = Uint8List.fromList('test'.codeUnits);
      final signature = await generateSignature(testData, keyPath);
      return await verifySignature(signature, testData, keyPath);
    } catch (e) {
      return false;
    }
  }

  /// 获取密钥信息
  Future<Map<String, dynamic>> getKeyInfo(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);

      return <String, dynamic>{
        'algorithm': 'Ed25519',
        'keySize': 256,
        'privateKeyLength': keyPair['private']!.length,
        'publicKeyLength': keyPair['public']!.length,
        'created': DateTime.now().toIso8601String(),
        'note': 'This is a simplified implementation, not real Ed25519',
      };
    } catch (e) {
      return <String, dynamic>{
        'error': e.toString(),
      };
    }
  }

  /// 导出公钥为十六进制字符串
  Future<String> exportPublicKeyHex(String? keyPath) async {
    final publicKey = await getPublicKey(keyPath);
    if (publicKey == null) {
      throw PluginSignatureException('Public key not found');
    }

    return publicKey
        .map((byte) => byte.toRadixString(16).padLeft(2, '0'))
        .join();
  }

  /// 导入公钥从十六进制字符串
  /// TODO(ed25519_signature): 实现公钥导入功能
  Future<void> importPublicKeyHex(String keyPath, String hexData) async {
    try {
      if (hexData.length != 64) {
        // 32 bytes * 2 hex chars
        throw PluginSignatureException('Invalid Ed25519 public key length');
      }

      final publicKey = Uint8List(32);
      for (int i = 0; i < 32; i++) {
        final hexByte = hexData.substring(i * 2, i * 2 + 2);
        publicKey[i] = int.parse(hexByte, radix: 16);
      }

      // 只存储公钥（没有私钥）
      _keyPairs[keyPath] = <String, Uint8List>{
        'public': publicKey,
        'private': Uint8List(0), // 空的私钥
      };
    } catch (e) {
      throw PluginSignatureException('Failed to import Ed25519 public key: $e');
    }
  }

  /// 检查是否有私钥
  Future<bool> hasPrivateKey(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      return keyPair['private']!.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 获取算法特性
  Map<String, dynamic> getAlgorithmInfo() {
    return <String, dynamic>{
      'name': 'Ed25519',
      'type': 'EdDSA',
      'keySize': 256,
      'signatureSize': 512,
      'securityLevel': 128,
      'isQuantumResistant': false,
      'implementation': 'Simplified SHA-512 based (not real Ed25519)',
      'note':
          'This is a placeholder implementation. Real Ed25519 requires specialized cryptographic libraries.',
    };
  }
}
