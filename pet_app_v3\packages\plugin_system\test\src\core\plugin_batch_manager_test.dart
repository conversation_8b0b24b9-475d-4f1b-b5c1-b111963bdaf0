/*
---------------------------------------------------------------
File name:          plugin_batch_manager_test.dart
Author:             lgnorant-lu
Date created:       2025/07/28
Last modified:      2025/07/28
Dart Version:       3.2+
Description:        插件批量管理器测试
---------------------------------------------------------------
Change History:
    2025/07/28: Initial creation - 插件批量管理器测试;
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:test/test.dart';
import 'package:plugin_system/src/core/plugin_batch_manager.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

void main() {
  group('PluginBatchManager', () {
    late PluginBatchManager batchManager;
    late PluginRegistry registry;

    setUp(() {
      registry = PluginRegistry.instance;
      batchManager = PluginBatchManager.instance;

      // 清理注册表
      registry.clear();
    });

    tearDown(() {
      registry.clear();
    });

    group('批量操作配置测试', () {
      test('应该使用默认配置', () {
        const config = BatchOperationConfig();

        expect(config.maxConcurrency, equals(3));
        expect(config.continueOnError, isFalse);
        expect(config.timeout, equals(const Duration(minutes: 5)));
        expect(config.retryCount, equals(2));
      });

      test('应该支持自定义配置', () {
        const config = BatchOperationConfig(
          maxConcurrency: 5,
          continueOnError: true,
          timeout: Duration(minutes: 10),
          retryCount: 3,
        );

        expect(config.maxConcurrency, equals(5));
        expect(config.continueOnError, isTrue);
        expect(config.timeout, equals(const Duration(minutes: 10)));
        expect(config.retryCount, equals(3));
      });
    });

    group('批量验证测试', () {
      test('应该能批量验证插件', () async {
        final pluginIds = ['plugin1', 'plugin2', 'plugin3'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.validate,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(maxConcurrency: 2),
        );

        expect(result.operation, equals(BatchOperationType.validate));
        expect(result.totalCount, equals(3));
        expect(result.successCount, greaterThanOrEqualTo(0));
        expect(result.failureCount, greaterThanOrEqualTo(0));
        expect(result.results.length, equals(3));
      });

      test('应该处理验证错误', () async {
        final pluginIds = ['invalid_plugin'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.validate,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(continueOnError: true),
        );

        expect(result.operation, equals(BatchOperationType.validate));
        expect(result.totalCount, equals(1));
        expect(result.results.length, equals(1));
        expect(result.results.first.success, isA<bool>());
      });
    });

    group('批量构建测试', () {
      test('应该能批量构建插件', () async {
        final pluginIds = ['plugin1', 'plugin2'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.build,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(maxConcurrency: 1),
        );

        expect(result.operation, equals(BatchOperationType.build));
        expect(result.totalCount, equals(2));
        expect(result.results.length, equals(2));
      });

      test('应该支持构建参数', () async {
        final pluginIds = ['plugin1'];
        final buildParams = {
          'plugin1': {'target': 'release', 'optimize': true},
        };

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.build,
          pluginIds: pluginIds,
          operationParams: buildParams,
        );

        expect(result.operation, equals(BatchOperationType.build));
        expect(result.totalCount, equals(1));
      });
    });

    group('批量发布测试', () {
      test('应该能批量发布插件', () async {
        final pluginIds = ['plugin1', 'plugin2'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.publish,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(maxConcurrency: 1),
        );

        expect(result.operation, equals(BatchOperationType.publish));
        expect(result.totalCount, equals(2));
        expect(result.results.length, equals(2));
      });

      test('应该支持发布参数', () async {
        final pluginIds = ['plugin1'];
        final publishParams = {
          'plugin1': {'registry': 'production', 'version': '1.0.0'},
        };

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.publish,
          pluginIds: pluginIds,
          operationParams: publishParams,
        );

        expect(result.operation, equals(BatchOperationType.publish));
        expect(result.totalCount, equals(1));
      });
    });

    group('批量安装测试', () {
      test('应该能批量安装插件', () async {
        final pluginIds = ['new_plugin1', 'new_plugin2'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.install,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(maxConcurrency: 2),
        );

        expect(result.operation, equals(BatchOperationType.install));
        expect(result.totalCount, equals(2));
        expect(result.results.length, equals(2));
      });

      test('应该支持安装参数', () async {
        final pluginIds = ['new_plugin1'];
        final installParams = {
          'new_plugin1': {'version': '1.0.0', 'source': 'registry'},
        };

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.install,
          pluginIds: pluginIds,
          operationParams: installParams,
        );

        expect(result.operation, equals(BatchOperationType.install));
        expect(result.totalCount, equals(1));
      });
    });

    group('批量卸载测试', () {
      test('应该能批量卸载插件', () async {
        final pluginIds = ['test_plugin1', 'test_plugin2'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.uninstall,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(maxConcurrency: 2),
        );

        expect(result.operation, equals(BatchOperationType.uninstall));
        expect(result.totalCount, equals(2));
        expect(result.results.length, equals(2));
      });
    });

    group('批量更新测试', () {
      test('应该能批量更新插件', () async {
        final pluginIds = ['update_plugin1', 'update_plugin2'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.update,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(maxConcurrency: 1),
        );

        expect(result.operation, equals(BatchOperationType.update));
        expect(result.totalCount, equals(2));
        expect(result.results.length, equals(2));
      });

      test('应该支持更新参数', () async {
        final pluginIds = ['update_plugin1'];
        final updateParams = {
          'update_plugin1': {'version': '2.0.0', 'force': true},
        };

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.update,
          pluginIds: pluginIds,
          operationParams: updateParams,
        );

        expect(result.operation, equals(BatchOperationType.update));
        expect(result.totalCount, equals(1));
      });
    });

    group('批量同步测试', () {
      test('应该能批量同步插件', () async {
        final pluginIds = ['sync_plugin1', 'sync_plugin2'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.sync,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(maxConcurrency: 2),
        );

        expect(result.operation, equals(BatchOperationType.sync));
        expect(result.totalCount, equals(2));
        expect(result.results.length, equals(2));
      });
    });

    group('错误处理测试', () {
      test('应该处理超时错误', () async {
        final pluginIds = ['timeout_plugin'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.validate,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(
            timeout: Duration(milliseconds: 100), // 很短的超时
          ),
        );

        expect(result.operation, equals(BatchOperationType.validate));
        expect(result.totalCount, equals(1));
        expect(result.results.length, equals(1));
      });

      test('应该支持continueOnError配置', () async {
        final pluginIds = ['plugin1', 'invalid_plugin', 'plugin2'];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.validate,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(continueOnError: true),
        );

        expect(result.operation, equals(BatchOperationType.validate));
        expect(result.totalCount, equals(3));
        expect(result.results.length, equals(3));
      });
    });

    group('并发控制测试', () {
      test('应该限制并发数量', () async {
        final pluginIds = [
          'plugin1',
          'plugin2',
          'plugin3',
          'plugin4',
          'plugin5'
        ];

        final result = await batchManager.executeBatchOperation(
          operation: BatchOperationType.validate,
          pluginIds: pluginIds,
          config: const BatchOperationConfig(maxConcurrency: 2),
        );

        // 验证并发控制生效
        expect(result.operation, equals(BatchOperationType.validate));
        expect(result.totalCount, equals(5));
        expect(result.results.length, equals(5));
      });
    });
  });
}

/// 模拟插件类用于测试
class MockPlugin {
  MockPlugin(this.id);

  final String id;

  String get name => 'Mock Plugin $id';
  String get version => '1.0.0';
  String get description => 'Mock plugin for testing';
}
