/*
---------------------------------------------------------------
File name:          plugin_signature_core.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        插件数字签名核心接口和数据结构
---------------------------------------------------------------
Change History:
    2025-07-28:     初始实现插件数字签名功能;
*/

import 'dart:typed_data';

import 'package:flutter/foundation.dart';

/// 签名算法枚举
enum PluginSignatureAlgorithm {
  /// RSA-2048 with SHA-256
  rsa2048,

  /// ECDSA-P256 with SHA-256
  ecdsaP256,

  /// Ed25519
  ed25519,
}

/// 签名策略
enum PluginSignaturePolicy {
  /// 禁用签名验证
  disabled,

  /// 可选签名验证
  optional,

  /// 必需签名验证
  required,

  /// 企业级签名验证
  enterprise,
}

/// 证书状态
enum PluginCertificateStatus {
  /// 有效
  valid,

  /// 已撤销
  revoked,

  /// 已过期
  expired,

  /// 未知
  unknown,

  /// 不可信
  untrusted,
}

/// 时间戳信息
@immutable
class PluginTimestampInfo {
  const PluginTimestampInfo({
    required this.tsaUrl,
    required this.timestamp,
    required this.signature,
    required this.certificate,
    required this.isValid,
  });

  /// 时间戳服务器URL
  final String tsaUrl;

  /// 时间戳
  final DateTime timestamp;

  /// 时间戳签名
  final Uint8List signature;

  /// 时间戳证书
  final PluginCertificateInfo certificate;

  /// 是否有效
  final bool isValid;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginTimestampInfo &&
          runtimeType == other.runtimeType &&
          tsaUrl == other.tsaUrl &&
          timestamp == other.timestamp;

  @override
  int get hashCode => tsaUrl.hashCode ^ timestamp.hashCode;
}

/// 证书信息
@immutable
class PluginCertificateInfo {
  const PluginCertificateInfo({
    required this.subject,
    required this.issuer,
    required this.serialNumber,
    required this.notBefore,
    required this.notAfter,
    required this.fingerprint,
    required this.status,
    required this.keyUsage,
    required this.extendedKeyUsage,
  });

  /// 证书主题
  final String subject;

  /// 证书颁发者
  final String issuer;

  /// 序列号
  final String serialNumber;

  /// 有效期开始
  final DateTime notBefore;

  /// 有效期结束
  final DateTime notAfter;

  /// 指纹
  final String fingerprint;

  /// 证书状态
  final PluginCertificateStatus status;

  /// 密钥用法
  final List<String> keyUsage;

  /// 扩展密钥用法
  final List<String> extendedKeyUsage;

  /// 是否有效
  bool get isValid {
    final now = DateTime.now();
    return status == PluginCertificateStatus.valid &&
        now.isAfter(notBefore) &&
        now.isBefore(notAfter);
  }

  /// 是否已过期
  bool get isExpired => DateTime.now().isAfter(notAfter);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginCertificateInfo &&
          runtimeType == other.runtimeType &&
          serialNumber == other.serialNumber &&
          fingerprint == other.fingerprint;

  @override
  int get hashCode => serialNumber.hashCode ^ fingerprint.hashCode;
}

/// 签名信息
@immutable
class PluginSignatureInfo {
  const PluginSignatureInfo({
    required this.algorithm,
    required this.signature,
    required this.signedAt,
    required this.certificate,
    this.timestamp,
    this.attributes = const <String, dynamic>{},
  });

  /// 签名算法
  final PluginSignatureAlgorithm algorithm;

  /// 签名值
  final Uint8List signature;

  /// 签名时间
  final DateTime signedAt;

  /// 证书信息
  final PluginCertificateInfo certificate;

  /// 时间戳信息
  final PluginTimestampInfo? timestamp;

  /// 签名属性
  final Map<String, dynamic> attributes;

  /// 是否有时间戳
  bool get hasTimestamp => timestamp != null;

  /// 是否可信
  bool get isTrusted => certificate.isValid && (timestamp?.isValid ?? true);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginSignatureInfo &&
          runtimeType == other.runtimeType &&
          signature == other.signature &&
          signedAt == other.signedAt;

  @override
  int get hashCode => signature.hashCode ^ signedAt.hashCode;
}

/// 签名验证结果
@immutable
class PluginSignatureVerificationResult {
  const PluginSignatureVerificationResult({
    required this.isValid,
    required this.signatures,
    required this.errors,
    required this.warnings,
    required this.verifiedAt,
    required this.policy,
  });

  /// 是否验证通过
  final bool isValid;

  /// 签名信息列表
  final List<PluginSignatureInfo> signatures;

  /// 错误信息
  final List<String> errors;

  /// 警告信息
  final List<String> warnings;

  /// 验证时间
  final DateTime verifiedAt;

  /// 使用的策略
  final PluginSignaturePolicy policy;

  /// 是否有签名
  bool get hasSigned => signatures.isNotEmpty;

  /// 是否有错误
  bool get hasErrors => errors.isNotEmpty;

  /// 是否有警告
  bool get hasWarnings => warnings.isNotEmpty;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PluginSignatureVerificationResult &&
          runtimeType == other.runtimeType &&
          isValid == other.isValid &&
          signatures == other.signatures;

  @override
  int get hashCode => isValid.hashCode ^ signatures.hashCode;
}

/// 数字签名提供者接口
abstract class SignatureProvider {
  /// 支持的算法
  PluginSignatureAlgorithm get algorithm;

  /// 生成签名
  Future<Uint8List> generateSignature(
    Uint8List data,
    String? privateKeyPath,
  );

  /// 验证签名
  Future<bool> verifySignature(
    Uint8List signature,
    Uint8List data,
    String? publicKeyPath,
  );

  /// 生成密钥对
  Future<void> generateKeyPair(String keyPath);
}

/// 插件签名服务接口
abstract class PluginSignatureService {
  /// 签名插件
  Future<Uint8List> signPlugin(
    Uint8List pluginData, {
    String? certificatePath,
    String? privateKeyPath,
    PluginSignatureAlgorithm algorithm = PluginSignatureAlgorithm.rsa2048,
    Map<String, dynamic> attributes = const <String, dynamic>{},
  });

  /// 验证插件签名
  Future<PluginSignatureVerificationResult> verifyPluginSignature(
    String filePath,
    Uint8List fileData,
  );

  /// 获取证书信息
  Future<PluginCertificateInfo?> getCertificateInfo(String certificatePath);

  /// 验证时间戳
  Future<bool> verifyTimestamp(PluginTimestampInfo timestamp);
}
