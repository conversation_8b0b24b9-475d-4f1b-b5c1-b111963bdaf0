# Pet App V3 插件目录

这是Pet App V3项目的插件存储目录，用于存放所有的插件项目。

## 目录结构

```
plugins/
├── README.md                    # 本文件
├── .gitkeep                     # 保持目录存在
├── plugin_registry.json         # 插件注册表
├── templates/                   # 插件模板
│   ├── basic_plugin/            # 基础插件模板
│   ├── ui_plugin/               # UI插件模板
│   └── service_plugin/          # 服务插件模板
├── examples/                    # 示例插件
│   ├── hello_world_plugin/      # Hello World示例
│   ├── calculator_plugin/       # 计算器插件示例
│   └── weather_plugin/          # 天气插件示例
├── development/                 # 开发中的插件
│   └── .gitkeep
├── testing/                     # 测试中的插件
│   └── .gitkeep
├── production/                  # 生产环境插件
│   └── .gitkeep
└── archived/                    # 已归档的插件
    └── .gitkeep
```

## 插件开发流程

### 1. 创建新插件
```bash
# 使用Ming CLI创建插件
ming create my_plugin --template=plugin --plugin_type=tool

# 或者从模板复制
cp -r templates/basic_plugin development/my_plugin
```

### 2. 开发阶段
- 在 `development/` 目录下进行开发
- 使用Creative Workshop进行调试和测试
- 遵循插件开发规范

### 3. 测试阶段
- 将插件移动到 `testing/` 目录
- 进行完整的功能测试
- 性能测试和兼容性测试

### 4. 生产部署
- 测试通过后移动到 `production/` 目录
- 通过Creative Workshop发布到注册表
- 发布到pub.dev（如果是公开插件）

### 5. 归档管理
- 不再维护的插件移动到 `archived/` 目录
- 保留历史记录和文档

## 插件规范

### 必需文件
- `plugin.yaml` - 插件清单文件
- `pubspec.yaml` - Dart包配置
- `README.md` - 插件文档
- `CHANGELOG.md` - 变更日志
- `lib/` - 源代码目录
- `test/` - 测试目录

### 可选文件
- `example/` - 示例应用
- `assets/` - 资源文件
- `docs/` - 详细文档
- `.github/` - GitHub工作流

## 插件类型

| 类型 | 目录前缀 | 说明 |
|------|----------|------|
| tool | tool_ | 工具插件 |
| game | game_ | 游戏插件 |
| theme | theme_ | 主题插件 |
| service | service_ | 服务插件 |
| widget | widget_ | 小部件插件 |
| ui | ui_ | UI组件插件 |
| system | system_ | 系统插件 |

## 命名规范

### 插件命名
- 使用snake_case格式
- 包含类型前缀
- 描述性名称
- 例如：`tool_image_editor`, `game_puzzle_solver`

### 文件命名
- 主插件文件：`{plugin_name}_plugin.dart`
- 测试文件：`{plugin_name}_test.dart`
- 配置文件：`{plugin_name}_config.dart`

## 开发工具

### Ming CLI命令
```bash
# 创建插件
ming create <plugin_name> --template=plugin

# 验证插件
ming plugin validate

# 构建插件
ming plugin build

# 发布插件
ming plugin publish
```

### Creative Workshop
- 插件管理界面
- 开发者平台
- 发布管理
- 测试工具

## 质量标准

### 代码质量
- 通过dart analyze检查
- 代码覆盖率 > 80%
- 遵循Dart代码规范
- 完整的文档注释

### 功能要求
- 完整的生命周期管理
- 错误处理和恢复
- 权限管理
- 平台兼容性

### 性能要求
- 启动时间 < 500ms
- 内存使用合理
- 无内存泄漏
- 响应时间 < 100ms

## 发布流程

### 本地测试
1. 单元测试通过
2. 集成测试通过
3. 性能测试通过
4. 兼容性测试通过

### 注册表发布
1. 更新版本号
2. 更新CHANGELOG
3. 通过Creative Workshop发布
4. 验证发布结果

### pub.dev发布
1. 验证pubspec.yaml
2. 运行pub publish --dry-run
3. 执行pub publish
4. 验证发布状态

## 维护指南

### 版本管理
- 遵循语义化版本规范
- 主版本：不兼容的API更改
- 次版本：向后兼容的功能添加
- 修订版本：向后兼容的错误修复

### 文档维护
- 保持README.md更新
- 更新API文档
- 维护示例代码
- 记录已知问题

### 社区支持
- 及时回应问题报告
- 处理功能请求
- 维护社区关系
- 提供技术支持

---

*此目录由Pet App V3插件系统管理*
