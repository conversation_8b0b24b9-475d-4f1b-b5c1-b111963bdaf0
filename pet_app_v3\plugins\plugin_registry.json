{"registry": {"name": "Pet App V3 Local Plugin Registry", "version": "1.0.0", "description": "本地插件注册表，管理Pet App V3的所有插件", "created_at": "2025-07-28T00:00:00Z", "updated_at": "2025-07-28T00:00:00Z", "total_plugins": 0, "active_plugins": 0}, "plugins": {"examples": {"hello_world_plugin": {"id": "hello_world_plugin", "name": "Hello World Plugin", "version": "1.0.0", "description": "一个简单的Hello World示例插件", "author": "Pet App Team", "category": "tool", "type": "example", "status": "active", "path": "examples/hello_world_plugin", "entry_point": "lib/hello_world_plugin.dart", "supported_platforms": ["android", "ios", "web", "desktop"], "required_permissions": [], "dependencies": [], "created_at": "2025-07-28T00:00:00Z", "updated_at": "2025-07-28T00:00:00Z", "install_count": 0, "rating": 0.0, "tags": ["example", "hello-world", "tutorial"]}, "calculator_plugin": {"id": "calculator_plugin", "name": "Calculator Plugin", "version": "1.0.0", "description": "一个功能完整的计算器插件示例", "author": "Pet App Team", "category": "tool", "type": "example", "status": "active", "path": "examples/calculator_plugin", "entry_point": "lib/calculator_plugin.dart", "supported_platforms": ["android", "ios", "web", "desktop"], "required_permissions": [], "dependencies": [], "created_at": "2025-07-28T00:00:00Z", "updated_at": "2025-07-28T00:00:00Z", "install_count": 0, "rating": 0.0, "tags": ["example", "calculator", "math", "tool"]}, "weather_plugin": {"id": "weather_plugin", "name": "Weather Plugin", "version": "1.0.0", "description": "天气信息显示插件示例", "author": "Pet App Team", "category": "widget", "type": "example", "status": "active", "path": "examples/weather_plugin", "entry_point": "lib/weather_plugin.dart", "supported_platforms": ["android", "ios", "web", "desktop"], "required_permissions": ["network"], "dependencies": ["http"], "created_at": "2025-07-28T00:00:00Z", "updated_at": "2025-07-28T00:00:00Z", "install_count": 0, "rating": 0.0, "tags": ["example", "weather", "widget", "network"]}}, "development": {}, "testing": {}, "production": {}, "archived": {}}, "categories": {"tool": {"name": "工具插件", "description": "提供各种工具功能的插件", "icon": "build", "count": 0}, "game": {"name": "游戏插件", "description": "提供游戏和娱乐功能的插件", "icon": "games", "count": 0}, "theme": {"name": "主题插件", "description": "提供界面主题和样式的插件", "icon": "palette", "count": 0}, "service": {"name": "服务插件", "description": "提供后台服务功能的插件", "icon": "cloud", "count": 0}, "widget": {"name": "小部件插件", "description": "提供桌面小部件功能的插件", "icon": "widgets", "count": 0}, "ui": {"name": "UI组件插件", "description": "提供用户界面组件的插件", "icon": "view_module", "count": 0}, "system": {"name": "系统插件", "description": "提供系统级功能的插件", "icon": "settings", "count": 0}}, "templates": {"basic_plugin": {"name": "基础插件模板", "description": "最基本的插件模板，包含必需的文件和结构", "path": "templates/basic_plugin", "version": "1.0.0", "supported_types": ["tool", "service", "system"], "features": ["basic_lifecycle", "error_handling", "logging"]}, "ui_plugin": {"name": "UI插件模板", "description": "包含UI组件的插件模板", "path": "templates/ui_plugin", "version": "1.0.0", "supported_types": ["widget", "ui", "theme"], "features": ["ui_components", "theme_support", "responsive_design"]}, "service_plugin": {"name": "服务插件模板", "description": "后台服务插件模板", "path": "templates/service_plugin", "version": "1.0.0", "supported_types": ["service", "system"], "features": ["background_service", "data_sync", "api_integration"]}}, "statistics": {"total_downloads": 0, "active_developers": 0, "popular_categories": [], "recent_updates": [], "trending_plugins": []}, "configuration": {"auto_update_enabled": true, "security_scan_enabled": true, "performance_monitoring": true, "backup_enabled": true, "backup_interval": "daily", "max_plugin_size": "50MB", "supported_dart_versions": [">=3.0.0 <4.0.0"], "supported_flutter_versions": [">=3.0.0"]}}