/*
---------------------------------------------------------------
File name:          plugin_installation_manager_test.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        插件安装管理器测试 - 简化版本
---------------------------------------------------------------
Change History:
    2025-07-28:     创建插件安装管理器简化测试;
---------------------------------------------------------------
*/

import 'dart:typed_data';

import 'package:plugin_system/src/api/plugin_installation_manager.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';
import 'package:test/test.dart';

void main() {
  group('PluginInstallationManager Tests', () {
    test('应该能创建PluginInstallationManager实例', () {
      final registry = PluginRegistry.instance;
      final manager = PluginInstallationManager(registry: registry);

      expect(manager, isNotNull);
      expect(manager.registry, equals(registry));
    });

    test('应该拒绝空插件ID进行安装', () async {
      final registry = PluginRegistry.instance;
      final manager = PluginInstallationManager(registry: registry);

      final result = await manager.installPlugin('');

      expect(result.isSuccess, isFalse);
      expect(result.statusCode, equals(400));
      expect(result.message, contains('插件ID不能为空'));
    });

    test('应该拒绝空插件ID进行卸载', () async {
      final registry = PluginRegistry.instance;
      final manager = PluginInstallationManager(registry: registry);

      final result = await manager.uninstallPlugin('');

      expect(result.isSuccess, isFalse);
      expect(result.statusCode, equals(400));
      expect(result.message, contains('插件ID不能为空'));
    });

    test('应该能验证包格式', () async {
      final registry = PluginRegistry.instance;
      final manager = PluginInstallationManager(registry: registry);

      final tooSmallPackage = Uint8List.fromList([0x01, 0x02]);
      final result = await manager.validatePluginPackage(tooSmallPackage);

      expect(result.isSuccess, isFalse);
      expect(result.statusCode, equals(400));
      expect(result.message, contains('文件太小'));
    });
  });
}
