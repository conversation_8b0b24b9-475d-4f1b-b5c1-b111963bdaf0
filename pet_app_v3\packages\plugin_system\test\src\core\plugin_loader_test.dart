/*
---------------------------------------------------------------
File name:          plugin_loader_test.dart
Author:             lgnorant-lu
Date created:       2025/07/27
Last modified:      2025/07/28
Dart Version:       3.2+
Description:        插件加载器测试 (Plugin loader tests)
---------------------------------------------------------------
Change History:
    2025/07/27: Initial creation - 插件加载器测试;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:isolate';

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_exceptions.dart';
import 'package:plugin_system/src/core/plugin_loader.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

import '../../helpers/test_plugin.dart';
import 'plugin_loader_test.mocks.dart';

@GenerateMocks([PluginRegistry])
void main() {
  group('PluginLoader', () {
    late PluginLoader loader;
    late MockPluginRegistry mockRegistry;
    late TestPlugin testPlugin;

    setUp(() {
      loader = PluginLoader.instance;
      mockRegistry = MockPluginRegistry();
      testPlugin = TestPlugin();
    });

    tearDown(() async {
      // 清理所有插件
      try {
        await loader.unloadAllPlugins(force: true);
      } catch (e) {
        // 忽略清理错误
      }
    });

    group('单例模式', () {
      test('应该返回相同的实例', () {
        final instance1 = PluginLoader.instance;
        final instance2 = PluginLoader.instance;
        expect(instance1, same(instance2));
      });
    });

    group('插件加载', () {
      test('应该成功加载有效插件', () async {
        // 创建一个简单的测试插件
        final plugin = TestPlugin(
          id: 'test_plugin',
          name: 'Test Plugin',
          version: '1.0.0',
        );

        // 加载插件
        await expectLater(
          loader.loadPlugin(plugin),
          completes,
        );

        // 验证插件状态
        expect(loader.isLoading(plugin.id), isFalse);
      });

      test('应该拒绝重复加载相同插件', () async {
        final plugin = TestPlugin(
          id: 'duplicate_plugin',
          name: 'Duplicate Plugin',
          version: '1.0.0',
        );

        // 第一次加载应该成功
        await loader.loadPlugin(plugin);

        // 第二次加载应该抛出异常
        await expectLater(
          loader.loadPlugin(plugin),
          throwsA(isA<PluginStateException>()),
        );
      });

      test('应该处理插件加载超时', () async {
        final plugin = SlowTestPlugin(
          id: 'slow_plugin',
          name: 'Slow Plugin',
          version: '1.0.0',
          initDelay: const Duration(seconds: 5),
        );

        // 设置较短的超时时间
        await expectLater(
          loader.loadPlugin(plugin, timeoutSeconds: 1),
          throwsA(isA<PluginTimeoutException>()),
        );
      });

      test('应该处理插件初始化失败', () async {
        final plugin = FailingTestPlugin(
          id: 'failing_plugin',
          name: 'Failing Plugin',
          version: '1.0.0',
          shouldFailOnInit: true,
        );

        await expectLater(
          loader.loadPlugin(plugin),
          throwsA(isA<PluginLoadException>()),
        );
      });
    });

    group('插件卸载', () {
      test('应该成功卸载已加载的插件', () async {
        final plugin = TestPlugin(
          id: 'unload_test_plugin',
          name: 'Unload Test Plugin',
          version: '1.0.0',
        );

        // 先加载插件
        await loader.loadPlugin(plugin);

        // 然后卸载插件
        await expectLater(
          loader.unloadPlugin(plugin.id),
          completes,
        );
      });

      test('应该处理卸载不存在的插件', () async {
        await expectLater(
          loader.unloadPlugin('nonexistent_plugin'),
          throwsA(isA<PluginNotFoundException>()),
        );
      });

      test('应该支持强制卸载', () async {
        final plugin = FailingTestPlugin(
          id: 'force_unload_plugin',
          name: 'Force Unload Plugin',
          version: '1.0.0',
          shouldFailOnDispose: true,
        );

        // 先加载插件
        await loader.loadPlugin(plugin);

        // 强制卸载应该成功，即使dispose失败
        await expectLater(
          loader.unloadPlugin(plugin.id, force: true),
          completes,
        );
      });
    });

    group('插件重新加载', () {
      test('应该成功重新加载插件', () async {
        final plugin = TestPlugin(
          id: 'reload_test_plugin',
          name: 'Reload Test Plugin',
          version: '1.0.0',
        );

        // 先加载插件
        await loader.loadPlugin(plugin);

        // 重新加载插件
        await expectLater(
          loader.reloadPlugin(plugin.id),
          completes,
        );
      });

      test('应该处理重新加载不存在的插件', () async {
        await expectLater(
          loader.reloadPlugin('nonexistent_plugin'),
          throwsA(isA<PluginNotFoundException>()),
        );
      });
    });

    group('资源监控', () {
      test('应该返回插件资源使用情况', () async {
        final plugin = TestPlugin(
          id: 'resource_test_plugin',
          name: 'Resource Test Plugin',
          version: '1.0.0',
        );

        // 加载插件
        await loader.loadPlugin(plugin);

        // 获取资源使用情况
        final usage = loader.getPluginResourceUsage(plugin.id);
        expect(usage, isNotNull);
        expect(usage!['pluginId'], equals(plugin.id));
        expect(usage['memoryUsage'], isA<double>());
        expect(usage['cpuUsage'], isA<double>());
        expect(usage['timestamp'], isA<String>());
        expect(usage['uptime'], isA<int>());
      });

      test('应该处理不存在插件的资源查询', () {
        final usage = loader.getPluginResourceUsage('nonexistent_plugin');
        expect(usage, isNull);
      });
    });

    group('批量操作', () {
      test('应该成功卸载所有插件', () async {
        // 加载多个插件
        final plugins = [
          TestPlugin(
              id: 'batch_plugin_1', name: 'Batch Plugin 1', version: '1.0.0'),
          TestPlugin(
              id: 'batch_plugin_2', name: 'Batch Plugin 2', version: '1.0.0'),
          TestPlugin(
              id: 'batch_plugin_3', name: 'Batch Plugin 3', version: '1.0.0'),
        ];

        for (final plugin in plugins) {
          await loader.loadPlugin(plugin);
        }

        // 卸载所有插件
        await expectLater(
          loader.unloadAllPlugins(),
          completes,
        );
      });
    });

    group('状态查询', () {
      test('应该返回正确的加载器状态', () {
        final status = loader.getStatus();
        expect(status, isA<Map<String, dynamic>>());
        expect(status.containsKey('totalPlugins'), isTrue);
        expect(status.containsKey('loadingPlugins'), isTrue);
        expect(status.containsKey('activeIsolates'), isTrue);
        expect(status.containsKey('loadingPluginIds'), isTrue);
      });

      test('应该正确检测插件加载状态', () async {
        final plugin = SlowTestPlugin(
          id: 'loading_status_plugin',
          name: 'Loading Status Plugin',
          version: '1.0.0',
          initDelay: const Duration(milliseconds: 100),
        );

        // 开始加载插件（不等待完成）
        final loadFuture = loader.loadPlugin(plugin);

        // 检查加载状态
        expect(loader.isLoading(plugin.id), isTrue);

        // 等待加载完成
        await loadFuture;

        // 检查加载状态
        expect(loader.isLoading(plugin.id), isFalse);
      });

      test('应该支持等待插件加载完成', () async {
        final plugin = SlowTestPlugin(
          id: 'wait_plugin',
          name: 'Wait Plugin',
          version: '1.0.0',
          initDelay: const Duration(milliseconds: 50),
        );

        // 开始加载插件（不等待完成）
        final loadFuture = loader.loadPlugin(plugin);

        // 等待插件加载完成
        await expectLater(
          loader.waitForPlugin(plugin.id),
          completes,
        );

        // 确保加载确实完成了
        await loadFuture;
      });
    });

    group('权限验证', () {
      test('应该验证插件权限', () async {
        final plugin = TestPlugin(
          pluginId: 'permission_test_plugin',
          pluginName: 'Permission Test Plugin',
          pluginVersion: '1.0.0',
          pluginRequiredPermissions: [
            PluginPermission.network,
            PluginPermission.fileSystem,
          ],
        );

        // 加载插件应该成功（基础权限）
        await expectLater(
          loader.loadPlugin(plugin),
          completes,
        );
      });

      test('应该拒绝高风险权限组合', () async {
        final plugin = TestPlugin(
          id: 'high_risk_plugin',
          name: 'High Risk Plugin',
          version: '1.0.0',
          requiredPermissions: [
            PluginPermission.systemSettings,
            PluginPermission.camera,
          ],
        );

        // 高风险权限可能被拒绝
        // 注意：具体行为取决于权限策略实现
        await expectLater(
          loader.loadPlugin(plugin),
          anyOf([
            completes,
            throwsA(isA<PermissionNotDeclaredException>()),
          ]),
        );
      });
    });
  });
}
