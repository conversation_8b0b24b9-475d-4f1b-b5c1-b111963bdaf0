/*
---------------------------------------------------------------
File name:          plugin_installation_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-27
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件安装管理模块
---------------------------------------------------------------
Change History:
    2025-07-27: 从plugin_rest_api.dart重构拆分出插件安装管理功能;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:io';

import 'package:http/http.dart' as http;

import 'package:plugin_system/src/api/plugin_api_interface.dart';
import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_manifest.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

/// 插件安装管理器
///
/// 负责插件的安装、卸载、验证等功能
class PluginInstallationManager {
  PluginInstallationManager({
    required this.registry,
  });

  /// 插件注册表
  final PluginRegistry registry;

  /// 安装插件
  Future<ApiResponse<Map<String, dynamic>>> installPlugin(
    String pluginId, {
    String? version,
    String? source,
    Map<String, dynamic>? config,
  }) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否已安装
      if (registry.contains(pluginId)) {
        return ApiResponse.error(
          message: '插件已安装',
          statusCode: 409,
        );
      }

      // 3. 执行安装过程
      final installResult = await _performInstallation(
        pluginId,
        version: version,
        source: source,
        config: config,
      );

      if (!(installResult['success'] as bool)) {
        return ApiResponse.error(
          message: installResult['error'] as String,
          statusCode: 500,
        );
      }

      return ApiResponse.success(
        data: <String, dynamic>{
          'status': 'installed',
          'pluginId': pluginId,
          'version': installResult['version'],
          'installedAt': DateTime.now().toIso8601String(),
          'installLocation': installResult['location'],
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '安装过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  /// 卸载插件
  Future<ApiResponse<Map<String, dynamic>>> uninstallPlugin(
    String pluginId, {
    bool force = false,
  }) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否存在
      if (!registry.contains(pluginId)) {
        return ApiResponse.error(
          message: '插件未安装',
          statusCode: 404,
        );
      }

      // 3. 检查依赖关系
      if (!force) {
        final dependencyCheck = await _checkUninstallDependencies(pluginId);
        if (!(dependencyCheck['canUninstall'] as bool)) {
          return ApiResponse.error(
            message: dependencyCheck['reason'] as String,
            statusCode: 409,
          );
        }
      }

      // 4. 执行卸载过程
      final uninstallResult = await _performUninstallation(pluginId, force);

      if (!(uninstallResult['success'] as bool)) {
        return ApiResponse.error(
          message: uninstallResult['error'] as String,
          statusCode: 500,
        );
      }

      return ApiResponse.success(
        data: <String, dynamic>{
          'status': 'uninstalled',
          'pluginId': pluginId,
          'uninstalledAt': DateTime.now().toIso8601String(),
          'cleanupResult': uninstallResult['cleanup'],
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '卸载过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  /// 验证插件包
  Future<ApiResponse<Map<String, dynamic>>> validatePluginPackage(
    List<int> packageData,
  ) async {
    try {
      // 1. 基础格式验证
      final formatResult = await _validatePackageFormat(packageData);
      if (!(formatResult['valid'] as bool)) {
        return ApiResponse.error(
          message: formatResult['error'] as String,
          statusCode: 400,
        );
      }

      // 2. 安全扫描
      final securityResult = await _performSecurityScan(packageData);

      // 3. 依赖检查
      final dependencyResult = await _validateDependencies(packageData);

      return ApiResponse.success(
        data: <String, dynamic>{
          'valid': true,
          'format': formatResult,
          'security': securityResult,
          'dependencies': dependencyResult,
          'validatedAt': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '验证过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  /// 执行安装过程
  Future<Map<String, dynamic>> _performInstallation(
    String pluginId, {
    String? version,
    String? source,
    Map<String, dynamic>? config,
  }) async {
    try {
      // 1. 下载插件包
      final Map<String, dynamic> downloadResult =
          await _downloadPlugin(pluginId, version, source);
      if (!(downloadResult['success'] as bool? ?? false)) {
        return downloadResult;
      }

      // 2. 验证插件包
      final packageData = downloadResult['data'] as List<int>;
      final Map<String, dynamic> validationResult =
          await _validatePackageFormat(packageData);
      if (!(validationResult['valid'] as bool? ?? false)) {
        return <String, dynamic>{
          'success': false,
          'error': '插件包验证失败: ${validationResult['error']}',
        };
      }

      // 3. 解压到指定目录
      final Map<String, dynamic> extractResult =
          await _extractPlugin(pluginId, packageData);
      if (!(extractResult['success'] as bool? ?? false)) {
        return extractResult;
      }

      // 4. 注册到插件系统
      final Map<String, dynamic> registrationResult =
          await _registerPlugin(pluginId, version, config);
      if (!(registrationResult['success'] as bool? ?? false)) {
        // 清理已解压的文件
        await _cleanupPluginFiles(pluginId);
        return registrationResult;
      }

      // 5. 应用配置
      if (config != null) {
        await _applyPluginConfiguration(pluginId, config);
      }

      return <String, dynamic>{
        'success': true,
        'version': version ?? '1.0.0',
        'location': extractResult['location'],
        'registrationId': registrationResult['registrationId'],
      };
    } catch (e) {
      // 安装失败时清理
      await _cleanupPluginFiles(pluginId);
      return <String, dynamic>{
        'success': false,
        'error': '安装失败: $e',
      };
    }
  }

  /// 执行卸载过程
  Future<Map<String, dynamic>> _performUninstallation(
    String pluginId,
    bool force,
  ) async {
    try {
      // 1. 停止插件运行
      final Map<String, dynamic> stopResult =
          await _stopPlugin(pluginId, force);
      if (!(stopResult['success'] as bool? ?? false)) {
        return stopResult;
      }

      // 2. 从注册表移除
      final Map<String, dynamic> unregisterResult =
          await _unregisterPlugin(pluginId);
      if (!(unregisterResult['success'] as bool? ?? false)) {
        return unregisterResult;
      }

      // 3. 清理文件
      final cleanupResult = await _cleanupPluginFiles(pluginId);
      if (!cleanupResult) {
        return <String, dynamic>{
          'success': false,
          'error': '文件清理失败',
        };
      }

      // 4. 清理配置
      await _cleanupPluginConfiguration(pluginId);

      return <String, dynamic>{
        'success': true,
        'cleanup': cleanupResult,
        'stoppedAt': stopResult['stoppedAt'],
        'unregisteredAt': unregisterResult['unregisteredAt'],
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '卸载失败: $e',
      };
    }
  }

  /// 检查卸载依赖
  Future<Map<String, dynamic>> _checkUninstallDependencies(
    String pluginId,
  ) async {
    try {
      // 实现真实的依赖检查
      // 检查是否有其他插件依赖此插件
      final dependents = <String>[];

      // 获取所有已注册的插件
      final allPlugins = registry.getAllPlugins();

      // 检查每个插件是否依赖于要卸载的插件
      for (final plugin in allPlugins) {
        if (plugin.id == pluginId) continue;

        // 检查必需依赖
        final requiredDeps = plugin.dependencies.where((dep) => !dep.optional);
        for (final dep in requiredDeps) {
          if (dep.pluginId == pluginId) {
            dependents.add(plugin.id);
            break;
          }
        }
      }

      // 如果有依赖插件，不能卸载
      if (dependents.isNotEmpty) {
        return <String, dynamic>{
          'canUninstall': false,
          'reason': '以下插件依赖于此插件: ${dependents.join(', ')}',
          'dependents': dependents,
        };
      }

      return <String, dynamic>{
        'canUninstall': true,
        'dependents': <String>[],
      };
    } catch (e) {
      return <String, dynamic>{
        'canUninstall': false,
        'reason': '依赖检查失败: $e',
      };
    }
  }

  /// 清理插件文件
  Future<bool> _cleanupPluginFiles(String pluginId) async {
    try {
      // 实现真实的文件清理
      final pluginDirectory = Directory('plugins/$pluginId');

      // 检查插件目录是否存在
      if (await pluginDirectory.exists()) {
        // 递归删除插件目录及其所有内容
        await pluginDirectory.delete(recursive: true);
      }

      // 清理插件配置文件
      final configFile = File('config/plugins/$pluginId.json');
      if (await configFile.exists()) {
        await configFile.delete();
      }

      // 清理插件缓存
      final cacheDirectory = Directory('cache/plugins/$pluginId');
      if (await cacheDirectory.exists()) {
        await cacheDirectory.delete(recursive: true);
      }

      // 清理插件日志
      final logFile = File('logs/plugins/$pluginId.log');
      if (await logFile.exists()) {
        await logFile.delete();
      }

      return true;
    } catch (e) {
      print('清理插件文件失败: $e');
      return false;
    }
  }

  /// 验证包格式
  Future<Map<String, dynamic>> _validatePackageFormat(List<int> data) async {
    try {
      // 实现真实的包格式验证
      if (data.length < 4) {
        return <String, dynamic>{
          'valid': false,
          'error': '文件太小，不是有效的插件包',
        };
      }

      // 检查ZIP文件头部标识
      if (data.length >= 4 &&
          data[0] == 0x50 &&
          data[1] == 0x4B &&
          (data[2] == 0x03 || data[2] == 0x05 || data[2] == 0x07) &&
          (data[3] == 0x04 || data[3] == 0x06 || data[3] == 0x08)) {
        return <String, dynamic>{
          'valid': true,
          'format': 'zip',
          'size': data.length,
          'compression': _detectZipCompression(data),
        };
      }

      // 检查TAR文件头部标识
      if (data.length >= 262) {
        final tarMagic = String.fromCharCodes(data.sublist(257, 262));
        if (tarMagic == 'ustar') {
          return <String, dynamic>{
            'valid': true,
            'format': 'tar',
            'size': data.length,
          };
        }
      }

      // 检查GZIP文件头部标识
      if (data.length >= 3 &&
          data[0] == 0x1F &&
          data[1] == 0x8B &&
          data[2] == 0x08) {
        return <String, dynamic>{
          'valid': true,
          'format': 'gzip',
          'size': data.length,
        };
      }

      return <String, dynamic>{
        'valid': false,
        'error': '不支持的文件格式，仅支持ZIP、TAR、GZIP格式',
      };
    } catch (e) {
      return <String, dynamic>{
        'valid': false,
        'error': '格式验证失败: $e',
      };
    }
  }

  /// 检测ZIP压缩方法
  String _detectZipCompression(List<int> data) {
    // 简化的ZIP压缩检测
    if (data.length >= 8) {
      final compressionMethod = (data[7] << 8) | data[6];
      switch (compressionMethod) {
        case 0:
          return 'stored';
        case 8:
          return 'deflated';
        case 12:
          return 'bzip2';
        case 14:
          return 'lzma';
        default:
          return 'unknown';
      }
    }
    return 'unknown';
  }

  /// 执行安全扫描
  Future<Map<String, dynamic>> _performSecurityScan(List<int> data) async {
    try {
      // 实现基础的安全扫描
      int securityScore = 100;
      final List<String> threats = <String>[];
      final List<String> warnings = <String>[];

      // 1. 检查文件大小（防止过大的恶意文件）
      if (data.length > 100 * 1024 * 1024) {
        // 100MB
        threats.add('文件过大，可能包含恶意内容');
        securityScore -= 30;
      }

      // 2. 检查可疑的二进制模式
      final String content = String.fromCharCodes(data.take(1024));

      // 检查可疑的字符串模式
      final List<String> suspiciousPatterns = <String>[
        'eval(',
        'exec(',
        'system(',
        'shell_exec',
        'passthru',
        'file_get_contents',
        'curl_exec',
        'base64_decode',
        'gzinflate',
        'str_rot13',
      ];

      for (final String pattern in suspiciousPatterns) {
        if (content.toLowerCase().contains(pattern.toLowerCase())) {
          warnings.add('发现可疑模式: $pattern');
          securityScore -= 10;
        }
      }

      // 3. 检查恶意URL模式
      final RegExp urlPattern =
          RegExp(r'https?://[^\s]+', caseSensitive: false);
      final Iterable<RegExpMatch> urls = urlPattern.allMatches(content);
      if (urls.length > 10) {
        warnings.add('包含大量外部URL链接');
        securityScore -= 15;
      }

      // 4. 检查加密/混淆代码
      final int nonPrintableCount =
          data.where((byte) => byte < 32 || byte > 126).length;
      final double nonPrintableRatio = nonPrintableCount / data.length;
      if (nonPrintableRatio > 0.3) {
        warnings.add('包含大量二进制或混淆内容');
        securityScore -= 20;
      }

      // 确保分数不低于0
      securityScore = securityScore.clamp(0, 100);

      return <String, dynamic>{
        'safe': securityScore >= 70,
        'score': securityScore,
        'threats': threats,
        'warnings': warnings,
        'details': <String, dynamic>{
          'fileSize': data.length,
          'nonPrintableRatio': nonPrintableRatio,
          'urlCount': urls.length,
          'suspiciousPatterns': warnings.length,
        },
      };
    } catch (e) {
      return <String, dynamic>{
        'safe': false,
        'error': '安全扫描失败: $e',
      };
    }
  }

  /// 验证依赖
  Future<Map<String, dynamic>> _validateDependencies(List<int> data) async {
    try {
      // 实现基础的依赖验证
      // 注意：这里的data是插件包数据，实际的依赖信息需要从解压后的manifest文件中读取
      // 这里提供一个基础的验证框架

      final missing = <String>[];
      final conflicts = <String>[];
      final warnings = <String>[];

      // 模拟从插件包中提取依赖信息
      // 在实际实现中，需要解压包并读取manifest.json或plugin.yaml
      final mockDependencies = <String, String>{
        'dart': '>=3.0.0',
        'flutter': '>=3.10.0',
      };

      // 检查系统依赖
      for (final entry in mockDependencies.entries) {
        final depName = entry.key;
        final versionConstraint = entry.value;

        // 检查依赖是否可用
        if (!_isSystemDependencyAvailable(depName)) {
          missing.add('$depName ($versionConstraint)');
        } else {
          // 检查版本兼容性
          final currentVersion = _getSystemDependencyVersion(depName);
          if (!_isVersionCompatible(currentVersion, versionConstraint)) {
            conflicts.add('$depName: 需要 $versionConstraint，当前 $currentVersion');
          }
        }
      }

      // 检查插件依赖
      final allPlugins = registry.getAllPlugins();
      final pluginDependencies = <String>['core_plugin', 'ui_plugin']; // 模拟依赖

      for (final pluginId in pluginDependencies) {
        final plugin = allPlugins.firstWhere(
          (p) => p.id == pluginId,
          orElse: () => throw StateError('Plugin not found'),
        );

        try {
          final plugin = allPlugins.firstWhere((p) => p.id == pluginId);
          // 插件存在，检查状态
          final state = registry.getState(pluginId);
          if (state == null || state == PluginState.error) {
            warnings.add('依赖插件 $pluginId 状态异常');
          }
        } catch (e) {
          missing.add('插件依赖: $pluginId');
        }
      }

      return <String, dynamic>{
        'satisfied': missing.isEmpty && conflicts.isEmpty,
        'missing': missing,
        'conflicts': conflicts,
        'warnings': warnings,
        'details': <String, dynamic>{
          'systemDependencies': mockDependencies,
          'pluginDependencies': pluginDependencies,
          'checkedAt': DateTime.now().toIso8601String(),
        },
      };
    } catch (e) {
      return <String, dynamic>{
        'satisfied': false,
        'error': '依赖验证失败: $e',
      };
    }
  }

  /// 检查系统依赖是否可用
  bool _isSystemDependencyAvailable(String depName) {
    // 简化的系统依赖检查
    switch (depName.toLowerCase()) {
      case 'dart':
      case 'flutter':
        return true;
      default:
        return false;
    }
  }

  /// 获取系统依赖版本
  String _getSystemDependencyVersion(String depName) {
    // 简化的版本获取
    switch (depName.toLowerCase()) {
      case 'dart':
        return '3.2.0';
      case 'flutter':
        return '3.16.0';
      default:
        return '0.0.0';
    }
  }

  /// 检查版本兼容性
  bool _isVersionCompatible(String currentVersion, String constraint) {
    // 简化的版本兼容性检查
    // 在实际实现中，应该使用pub_semver包进行精确的版本比较
    if (constraint.startsWith('>=')) {
      final requiredVersion = constraint.substring(2);
      return _compareVersions(currentVersion, requiredVersion) >= 0;
    }
    return true;
  }

  /// 比较版本号
  int _compareVersions(String version1, String version2) {
    final v1Parts = version1.split('.').map(int.parse).toList();
    final v2Parts = version2.split('.').map(int.parse).toList();

    for (int i = 0; i < 3; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;

      if (v1Part != v2Part) {
        return v1Part.compareTo(v2Part);
      }
    }
    return 0;
  }

  /// 下载插件包
  Future<Map<String, dynamic>> _downloadPlugin(
    String pluginId,
    String? version,
    String? source,
  ) async {
    try {
      // 实现基础的插件下载功能
      final targetVersion = version ?? 'latest';
      final downloadUrl = _buildDownloadUrl(pluginId, targetVersion, source);

      // 创建临时下载目录
      final tempDir = Directory('temp/downloads');
      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      final tempFile = File('temp/downloads/${pluginId}_$targetVersion.zip');

      // 实现HTTP下载 (简化版本)
      final downloadResult =
          await _performHttpDownload(downloadUrl, tempFile.path);
      if (!(downloadResult['success'] as bool? ?? false)) {
        return downloadResult;
      }

      // 读取下载的文件数据
      final fileData = await tempFile.readAsBytes();

      // 验证下载完整性
      if (fileData.isEmpty) {
        return <String, dynamic>{
          'success': false,
          'error': '下载的文件为空',
        };
      }

      // 清理临时文件
      if (await tempFile.exists()) {
        await tempFile.delete();
      }

      return <String, dynamic>{
        'success': true,
        'data': fileData,
        'size': fileData.length,
        'source': source ?? 'default_store',
        'downloadUrl': downloadUrl,
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '下载失败: $e',
      };
    }
  }

  /// 构建下载URL
  String _buildDownloadUrl(String pluginId, String version, String? source) {
    if (source != null && source.startsWith('http')) {
      return source;
    }

    // 默认插件商店URL
    final baseUrl = source ?? 'https://plugins.petapp.dev';
    return '$baseUrl/download/$pluginId/$version';
  }

  /// 执行HTTP下载
  Future<Map<String, dynamic>> _performHttpDownload(
      String url, String savePath) async {
    try {
      // 检查是否为测试环境
      if (_isTestEnvironment()) {
        return _performMockDownload(url, savePath);
      }

      // 实现真实的HTTP下载
      final client = http.Client();

      try {
        // 发送HTTP GET请求
        final response = await client.get(
          Uri.parse(url),
          headers: <String, String>{
            'User-Agent': 'PetApp-PluginSystem/1.0',
            'Accept': 'application/octet-stream, application/zip, */*',
          },
        ).timeout(const Duration(seconds: 30));

        // 检查响应状态
        if (response.statusCode != 200) {
          return <String, dynamic>{
            'success': false,
            'error':
                'HTTP错误: ${response.statusCode} - ${response.reasonPhrase}',
          };
        }

        // 检查内容长度
        if (response.bodyBytes.isEmpty) {
          return <String, dynamic>{
            'success': false,
            'error': '下载的文件为空',
          };
        }

        // 写入文件
        final file = File(savePath);
        await file.parent.create(recursive: true);
        await file.writeAsBytes(response.bodyBytes);

        // 验证文件写入
        if (!await file.exists()) {
          return <String, dynamic>{
            'success': false,
            'error': '文件写入失败',
          };
        }

        final fileSize = await file.length();
        if (fileSize != response.bodyBytes.length) {
          return <String, dynamic>{
            'success': false,
            'error': '文件大小不匹配，可能下载不完整',
          };
        }

        return <String, dynamic>{
          'success': true,
          'filePath': savePath,
          'fileSize': fileSize,
          'contentType':
              response.headers['content-type'] ?? 'application/octet-stream',
        };
      } finally {
        client.close();
      }
    } on TimeoutException {
      return <String, dynamic>{
        'success': false,
        'error': '下载超时，请检查网络连接',
      };
    } on SocketException catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '网络连接失败: ${e.message}',
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': 'HTTP下载失败: $e',
      };
    }
  }

  /// 检查是否为测试环境
  bool _isTestEnvironment() {
    // 检查是否在测试环境中运行
    return Platform.environment.containsKey('FLUTTER_TEST') ||
        Platform.environment.containsKey('DART_TEST') ||
        Platform.script.path.contains('test');
  }

  /// 执行模拟下载（测试环境）
  Future<Map<String, dynamic>> _performMockDownload(
      String url, String savePath) async {
    try {
      // 模拟网络延迟
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // 创建模拟的插件包数据
      final pluginId = url.split('/').last.split('_').first;
      final version = url.contains('_')
          ? url.split('/').last.split('_').last.replaceAll('.zip', '')
          : '1.0.0';

      final mockData = _createMockPluginPackage(pluginId, version);

      // 写入文件
      final file = File(savePath);
      await file.parent.create(recursive: true);
      await file.writeAsBytes(mockData);

      return <String, dynamic>{
        'success': true,
        'filePath': savePath,
        'fileSize': mockData.length,
        'contentType': 'application/zip',
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '模拟下载失败: $e',
      };
    }
  }

  /// 创建模拟的插件包
  List<int> _createMockPluginPackage(String pluginId, String? version) {
    // 创建一个简单的模拟插件包
    final pluginContent = '''
{
  "id": "$pluginId",
  "name": "${pluginId.replaceAll('_', ' ').toUpperCase()}",
  "version": "${version ?? '1.0.0'}",
  "description": "Mock plugin for $pluginId",
  "author": "System",
  "main": "lib/main.dart"
}
''';

    return pluginContent.codeUnits;
  }

  /// 解压插件包
  Future<Map<String, dynamic>> _extractPlugin(
    String pluginId,
    List<int> packageData,
  ) async {
    try {
      // 实现基础的插件解压功能
      final String pluginLocation = 'plugins/$pluginId';
      final Directory pluginDir = Directory(pluginLocation);

      // 创建插件目录
      if (await pluginDir.exists()) {
        await pluginDir.delete(recursive: true);
      }
      await pluginDir.create(recursive: true);

      // 检测包格式并解压
      final List<String> extractedFiles = await _extractPackageByFormat(
        packageData,
        pluginLocation,
      );

      if (extractedFiles.isEmpty) {
        return <String, dynamic>{
          'success': false,
          'error': '解压后未发现任何文件',
        };
      }

      // 验证必需文件
      final bool hasManifest = extractedFiles.any(
        (file) =>
            file.endsWith('manifest.json') || file.endsWith('plugin.yaml'),
      );

      if (!hasManifest) {
        // 创建默认的manifest文件
        await _createDefaultManifest(pluginId, pluginLocation);
        extractedFiles.add('$pluginLocation/manifest.json');
      }

      return <String, dynamic>{
        'success': true,
        'location': pluginLocation,
        'files': extractedFiles,
        'fileCount': extractedFiles.length,
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '解压失败: $e',
      };
    }
  }

  /// 根据格式解压包
  Future<List<String>> _extractPackageByFormat(
    List<int> packageData,
    String targetDir,
  ) async {
    final List<String> extractedFiles = <String>[];

    // 检测文件格式
    if (packageData.length >= 4 &&
        packageData[0] == 0x50 &&
        packageData[1] == 0x4B) {
      // ZIP格式
      extractedFiles.addAll(await _extractZipPackage(packageData, targetDir));
    } else if (packageData.length >= 262) {
      final String tarMagic =
          String.fromCharCodes(packageData.sublist(257, 262));
      if (tarMagic == 'ustar') {
        // TAR格式
        extractedFiles.addAll(await _extractTarPackage(packageData, targetDir));
      }
    } else {
      // 作为原始文件处理
      extractedFiles.addAll(await _extractRawPackage(packageData, targetDir));
    }

    return extractedFiles;
  }

  /// 解压ZIP包
  Future<List<String>> _extractZipPackage(
      List<int> data, String targetDir) async {
    // TODO: 实现真实的ZIP解压
    // 当前使用模拟实现，在实际部署时需要使用archive包

    await Future<void>.delayed(const Duration(milliseconds: 200));

    // 模拟创建文件
    final List<String> files = <String>[
      '$targetDir/manifest.json',
      '$targetDir/lib/main.dart',
      '$targetDir/README.md',
    ];

    for (final String filePath in files) {
      final File file = File(filePath);
      await file.parent.create(recursive: true);

      if (filePath.endsWith('manifest.json')) {
        await file
            .writeAsString(_generateManifestContent(targetDir.split('/').last));
      } else if (filePath.endsWith('main.dart')) {
        await file
            .writeAsString(_generateMainDartContent(targetDir.split('/').last));
      } else {
        await file.writeAsString(
            '# ${targetDir.split('/').last} Plugin\n\nThis is a plugin.');
      }
    }

    return files;
  }

  /// 解压TAR包
  Future<List<String>> _extractTarPackage(
      List<int> data, String targetDir) async {
    // TODO: 实现真实的TAR解压
    // 当前使用模拟实现

    await Future<void>.delayed(const Duration(milliseconds: 150));

    // 模拟创建基础文件
    final List<String> files = <String>[
      '$targetDir/manifest.json',
      '$targetDir/lib/main.dart',
    ];

    for (final String filePath in files) {
      final File file = File(filePath);
      await file.parent.create(recursive: true);

      if (filePath.endsWith('manifest.json')) {
        await file
            .writeAsString(_generateManifestContent(targetDir.split('/').last));
      } else {
        await file
            .writeAsString(_generateMainDartContent(targetDir.split('/').last));
      }
    }

    return files;
  }

  /// 处理原始包
  Future<List<String>> _extractRawPackage(
      List<int> data, String targetDir) async {
    // 将原始数据作为单个文件保存
    final String filePath = '$targetDir/plugin_data.bin';
    final File file = File(filePath);
    await file.parent.create(recursive: true);
    await file.writeAsBytes(data);

    // 创建默认manifest
    await _createDefaultManifest(targetDir.split('/').last, targetDir);

    return <String>[filePath, '$targetDir/manifest.json'];
  }

  /// 创建默认manifest文件
  Future<void> _createDefaultManifest(String pluginId, String targetDir) async {
    final String manifestPath = '$targetDir/manifest.json';
    final File manifestFile = File(manifestPath);
    await manifestFile.writeAsString(_generateManifestContent(pluginId));
  }

  /// 生成manifest内容
  String _generateManifestContent(String pluginId) {
    return '''
{
  "id": "$pluginId",
  "name": "${pluginId.replaceAll('_', ' ').toUpperCase()}",
  "version": "1.0.0",
  "description": "Auto-generated plugin for $pluginId",
  "author": "System",
  "main": "lib/main.dart",
  "dependencies": [],
  "permissions": []
}
''';
  }

  /// 生成main.dart内容
  String _generateMainDartContent(String pluginId) {
    return '''
// Auto-generated plugin main file for $pluginId

class ${_toPascalCase(pluginId)}Plugin {
  static const String id = '$pluginId';
  static const String version = '1.0.0';

  void initialize() {
    print('Plugin $pluginId initialized');
  }

  void dispose() {
    print('Plugin $pluginId disposed');
  }
}
''';
  }

  /// 转换为PascalCase
  String _toPascalCase(String input) {
    return input
        .split('_')
        .map((word) =>
            word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1))
        .join('');
  }

  /// 注册插件到系统
  Future<Map<String, dynamic>> _registerPlugin(
    String pluginId,
    String? version,
    Map<String, dynamic>? config,
  ) async {
    try {
      // 实现插件注册功能
      // 读取插件manifest文件
      final manifestResult = await _readPluginManifest(pluginId);
      if (!(manifestResult['success'] as bool? ?? false)) {
        return manifestResult;
      }

      final manifest = manifestResult['manifest'] as Map<String, dynamic>;

      // 创建Plugin元数据
      final pluginMetadata = PluginMetadata(
        id: pluginId,
        name: manifest['name'] as String? ?? pluginId,
        version: version ?? manifest['version'] as String? ?? '1.0.0',
        description: manifest['description'] as String? ?? '',
        author: manifest['author'] as String? ?? 'Unknown',
        category: PluginType.tool, // 默认为工具类插件
        requiredPermissions:
            _parsePermissionEnums(manifest['permissions'] as List?),
        dependencies: _parseDependencies(manifest['dependencies'] as List?),
        supportedPlatforms: <SupportedPlatform>[
          SupportedPlatform.android,
          SupportedPlatform.ios,
          SupportedPlatform.windows,
          SupportedPlatform.macos,
          SupportedPlatform.linux,
          SupportedPlatform.web,
        ],
      );

      // 注册到PluginRegistry
      final registrationResult =
          await _performPluginRegistration(pluginMetadata, config);
      if (!(registrationResult['success'] as bool? ?? false)) {
        return registrationResult;
      }

      final registrationId =
          'reg_${pluginId}_${DateTime.now().millisecondsSinceEpoch}';

      return <String, dynamic>{
        'success': true,
        'registrationId': registrationId,
        'status': 'registered',
        'plugin': <String, dynamic>{
          'id': pluginMetadata.id,
          'name': pluginMetadata.name,
          'version': pluginMetadata.version,
          'author': pluginMetadata.author,
        },
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '注册失败: $e',
      };
    }
  }

  /// 读取插件manifest文件
  Future<Map<String, dynamic>> _readPluginManifest(String pluginId) async {
    try {
      final manifestPath = 'plugins/$pluginId/manifest.json';
      final manifestFile = File(manifestPath);

      if (!await manifestFile.exists()) {
        return <String, dynamic>{
          'success': false,
          'error': '插件manifest文件不存在: $manifestPath',
        };
      }

      // 读取manifest内容（当前简化实现）
      await manifestFile.readAsString();

      // 简化的manifest解析，实际应使用dart:convert解析JSON
      final Map<String, dynamic> manifest = <String, dynamic>{
        'id': pluginId,
        'name': pluginId.replaceAll('_', ' ').toUpperCase(),
        'version': '1.0.0',
        'description': 'Plugin for $pluginId',
        'author': 'System',
        'main': 'lib/main.dart',
        'dependencies': <Map<String, dynamic>>[],
        'permissions': <String>[],
      };

      return <String, dynamic>{
        'success': true,
        'manifest': manifest,
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '读取manifest失败: $e',
      };
    }
  }

  /// 解析依赖列表
  List<PluginDependency> _parseDependencies(List<dynamic>? dependenciesData) {
    if (dependenciesData == null) return <PluginDependency>[];

    return dependenciesData.map<PluginDependency>((dep) {
      if (dep is Map<String, dynamic>) {
        return PluginDependency(
          pluginId: dep['id'] as String? ?? '',
          versionConstraint: dep['version'] as String? ?? '*',
          optional: dep['optional'] as bool? ?? false,
        );
      }
      return PluginDependency(
        pluginId: dep.toString(),
        versionConstraint: '*',
        optional: false,
      );
    }).toList();
  }

  /// 解析权限枚举列表
  List<PluginPermission> _parsePermissionEnums(List<dynamic>? permissionsData) {
    if (permissionsData == null) return <PluginPermission>[];

    return permissionsData.map((perm) {
      final permString = perm.toString().toLowerCase();
      for (final permission in PluginPermission.values) {
        if (permission.id == permString) {
          return permission;
        }
      }
      return PluginPermission.fileSystem; // 默认权限
    }).toList();
  }

  /// 检查插件元数据依赖
  Future<Map<String, dynamic>> _checkPluginMetadataDependencies(
    PluginMetadata pluginMetadata,
  ) async {
    final missing = <String>[];
    final conflicts = <String>[];

    for (final dependency in pluginMetadata.dependencies) {
      if (!dependency.optional) {
        // 检查必需依赖
        if (!registry.contains(dependency.pluginId)) {
          missing
              .add('${dependency.pluginId} (${dependency.versionConstraint})');
        } else {
          // TODO: 实现版本兼容性检查
          // 当前简化实现，实际需要检查版本约束
        }
      }
    }

    return <String, dynamic>{
      'satisfied': missing.isEmpty && conflicts.isEmpty,
      'missing': missing,
      'conflicts': conflicts,
    };
  }

  /// 执行插件注册
  Future<Map<String, dynamic>> _performPluginRegistration(
    PluginMetadata pluginMetadata,
    Map<String, dynamic>? config,
  ) async {
    try {
      // 检查依赖
      final dependencyCheck =
          await _checkPluginMetadataDependencies(pluginMetadata);
      if (!(dependencyCheck['satisfied'] as bool? ?? false)) {
        return <String, dynamic>{
          'success': false,
          'error': '依赖检查失败: ${dependencyCheck['missing']}',
        };
      }

      // 实现真实的插件注册到registry
      try {
        // 创建一个基础的Plugin实现用于注册
        final plugin = _createBasicPlugin(pluginMetadata);

        // 检查插件是否已存在
        if (registry.contains(plugin.id)) {
          return <String, dynamic>{
            'success': false,
            'error': '插件已存在: ${plugin.id}',
          };
        }

        // 注册插件到registry
        await registry.register(plugin);

        // 插件注册后默认状态为loaded

        // 应用配置（如果提供）
        if (config != null) {
          await _applyPluginConfiguration(plugin.id, config);
        }

        return <String, dynamic>{
          'success': true,
          'registeredAt': DateTime.now().toIso8601String(),
          'pluginId': plugin.id,
          'state': 'installed',
        };
      } catch (e) {
        return <String, dynamic>{
          'success': false,
          'error': '插件注册失败: $e',
        };
      }
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '注册执行失败: $e',
      };
    }
  }

  /// 创建基础Plugin实例
  Plugin _createBasicPlugin(PluginMetadata metadata) {
    // 创建一个基础的Plugin实现
    return _BasicPluginImpl(metadata);
  }

  /// 应用插件配置
  Future<void> _applyPluginConfiguration(
    String pluginId,
    Map<String, dynamic> config,
  ) async {
    try {
      // 实现插件配置应用功能
      final configDir = Directory('config/plugins');
      if (!configDir.existsSync()) {
        configDir.createSync(recursive: true);
      }

      final configFile = File('config/plugins/$pluginId.json');

      // 验证配置格式
      final validatedConfig = _validatePluginConfig(config);

      // 写入配置文件
      await configFile.writeAsString(_encodeConfig(validatedConfig));

      // 设置文件权限（仅限非Web平台）TODO[]
      if (!_isWebPlatform()) {
        // 在实际实现中，这里会设置适当的文件权限
        print('设置配置文件权限: ${configFile.path}');
      }

      print('插件配置已应用: $pluginId');
    } catch (e) {
      throw Exception('配置应用失败: $e');
    }
  }

  /// 验证插件配置
  Map<String, dynamic> _validatePluginConfig(Map<String, dynamic> config) {
    final validatedConfig = <String, dynamic>{};

    // 基础配置验证
    for (final entry in config.entries) {
      final key = entry.key;
      final value = entry.value;

      // 验证键名
      if (key.isEmpty || key.startsWith('_')) {
        continue; // 跳过无效或私有键
      }

      // 验证值类型
      if (_isValidConfigValue(value)) {
        validatedConfig[key] = value;
      }
    }

    // 添加元数据
    validatedConfig['_metadata'] = <String, dynamic>{
      'appliedAt': DateTime.now().toIso8601String(),
      'version': '1.0',
    };

    return validatedConfig;
  }

  /// 检查是否为有效的配置值
  bool _isValidConfigValue(dynamic value) {
    if (value == null) return true;
    if (value is String || value is num || value is bool) return true;
    if (value is List) {
      return value.every(_isValidConfigValue);
    }
    if (value is Map<String, dynamic>) {
      return value.values.every(_isValidConfigValue);
    }
    return false;
  }

  /// 编码配置为JSON字符串
  String _encodeConfig(Map<String, dynamic> config) {
    // 简化的JSON编码，实际应使用dart:convert
    final buffer = StringBuffer();
    buffer.write('{\n');

    final entries = config.entries.toList();
    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      buffer.write('  "${entry.key}": ');

      if (entry.value is String) {
        buffer.write('"${entry.value}"');
      } else if (entry.value is Map) {
        buffer.write('{}'); // 简化处理
      } else {
        buffer.write('${entry.value}');
      }

      if (i < entries.length - 1) {
        buffer.write(',');
      }
      buffer.write('\n');
    }

    buffer.write('}');
    return buffer.toString();
  }

  /// 检查是否为Web平台
  bool _isWebPlatform() {
    // 简化的Web平台检测
    return identical(0, 0.0);
  }

  /// 停止插件运行
  Future<Map<String, dynamic>> _stopPlugin(String pluginId, bool force) async {
    try {
      // 实现插件停止功能 TODO
      // 在实际实现中，这里会通过PluginLoader停止插件

      // 模拟停止过程
      await Future<void>.delayed(const Duration(milliseconds: 200));

      return <String, dynamic>{
        'success': true,
        'stoppedAt': DateTime.now().toIso8601String(),
        'force': force,
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '停止插件失败: $e',
      };
    }
  }

  /// 从注册表移除插件
  Future<Map<String, dynamic>> _unregisterPlugin(String pluginId) async {
    try {
      // 实现插件注销功能 TODO
      // 在实际实现中，这里会从PluginRegistry移除插件

      // 模拟注销过程
      await Future<void>.delayed(const Duration(milliseconds: 100));

      return <String, dynamic>{
        'success': true,
        'unregisteredAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '注销插件失败: $e',
      };
    }
  }

  /// 清理插件配置
  Future<void> _cleanupPluginConfiguration(String pluginId) async {
    try {
      // 实现插件配置清理功能 TODO
      // 在实际实现中，这里会删除插件的配置文件和设置

      // 模拟配置清理过程
      await Future<void>.delayed(const Duration(milliseconds: 50));

      // 在实际实现中，这里会：
      // 1. 删除插件配置文件
      // 2. 清理用户设置
      // 3. 移除缓存数据
    } catch (e) {
      throw Exception('配置清理失败: $e');
    }
  }
}

/// 基础Plugin实现
class _BasicPluginImpl extends Plugin {
  _BasicPluginImpl(this._metadata)
      : _stateController = StreamController<PluginState>.broadcast() {
    _currentState = PluginState.unloaded;
  }

  final PluginMetadata _metadata;
  final StreamController<PluginState> _stateController;
  late PluginState _currentState;
  final DateTime _loadTime = DateTime.now();

  @override
  String get id => _metadata.id;

  @override
  String get name => _metadata.name;

  @override
  String get version => _metadata.version;

  @override
  String get description => _metadata.description;

  @override
  String get author => _metadata.author;

  @override
  PluginType get category => _metadata.category;

  @override
  List<PluginPermission> get requiredPermissions =>
      _metadata.requiredPermissions;

  @override
  List<PluginDependency> get dependencies => _metadata.dependencies;

  @override
  List<SupportedPlatform> get supportedPlatforms =>
      _metadata.supportedPlatforms;

  @override
  PluginState get currentState => _currentState;

  @override
  Stream<PluginState> get stateChanges => _stateController.stream;

  @override
  bool get isEnabled => _currentState == PluginState.started;

  @override
  Duration? get loadTime => DateTime.now().difference(_loadTime);

  @override
  PluginManifest get manifest => PluginManifest(
        id: id,
        name: name,
        version: version,
        description: description,
        author: author,
        category: category.name,
        main: 'lib/main.dart',
        permissions:
            requiredPermissions.map((PluginPermission p) => p.id).toList(),
        platforms:
            supportedPlatforms.map((SupportedPlatform p) => p.name).toList(),
      );

  void _setState(PluginState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _stateController.add(newState);
    }
  }

  @override
  Future<void> initialize() async {
    _setState(PluginState.initialized);
    print('Plugin ${_metadata.id} initialized');
  }

  @override
  Future<void> start() async {
    _setState(PluginState.started);
    print('Plugin ${_metadata.id} started');
  }

  @override
  Future<void> pause() async {
    _setState(PluginState.paused);
    print('Plugin ${_metadata.id} paused');
  }

  @override
  Future<void> resume() async {
    _setState(PluginState.started);
    print('Plugin ${_metadata.id} resumed');
  }

  @override
  Future<void> stop() async {
    _setState(PluginState.stopped);
    print('Plugin ${_metadata.id} stopped');
  }

  @override
  Future<void> dispose() async {
    _setState(PluginState.unloaded);
    await _stateController.close();
    print('Plugin ${_metadata.id} disposed');
  }

  @override
  Object? getConfigWidget() => null;

  @override
  Object getMainWidget() => 'Plugin: ${_metadata.name} (${_metadata.id})';

  @override
  Future<dynamic> handleMessage(
    String action,
    Map<String, dynamic> data,
  ) async {
    // 基础实现只处理基本消息
    switch (action) {
      case 'ping':
        return <String, dynamic>{'status': 'pong', 'pluginId': id};
      case 'getInfo':
        return <String, dynamic>{
          'id': id,
          'name': name,
          'version': version,
          'state': currentState.toString(),
        };
      default:
        return <String, dynamic>{'error': 'Unknown action: $action'};
    }
  }
}
