/*
---------------------------------------------------------------
File name:          plugin_performance_optimizer.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        插件系统性能优化器 - Phase 4.1 性能优化和内存管理
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 4.1 - 性能优化和内存管理实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:collection';

/// 内存池配置
class MemoryPoolConfig {
  const MemoryPoolConfig({
    this.initialSize = 10,
    this.maxSize = 100,
    this.growthFactor = 1.5,
    this.shrinkThreshold = 0.25,
    this.maxIdleTime = const Duration(minutes: 5),
  });

  /// 初始大小
  final int initialSize;

  /// 最大大小
  final int maxSize;

  /// 增长因子
  final double growthFactor;

  /// 收缩阈值
  final double shrinkThreshold;

  /// 最大空闲时间
  final Duration maxIdleTime;
}

/// 缓存配置
class CacheConfig {
  const CacheConfig({
    this.maxSize = 1000,
    this.ttl = const Duration(hours: 1),
    this.enableLRU = true,
    this.enableCompression = false,
    this.compressionThreshold = 1024,
  });

  /// 最大缓存大小
  final int maxSize;

  /// 生存时间
  final Duration ttl;

  /// 启用LRU淘汰
  final bool enableLRU;

  /// 启用压缩
  final bool enableCompression;

  /// 压缩阈值
  final int compressionThreshold;
}

/// 异步处理配置
class AsyncConfig {
  const AsyncConfig({
    this.maxConcurrency = 10,
    this.queueSize = 1000,
    this.timeoutDuration = const Duration(seconds: 30),
    this.enableBatching = true,
    this.batchSize = 50,
    this.batchTimeout = const Duration(milliseconds: 100),
  });

  /// 最大并发数
  final int maxConcurrency;

  /// 队列大小
  final int queueSize;

  /// 超时时间
  final Duration timeoutDuration;

  /// 启用批处理
  final bool enableBatching;

  /// 批处理大小
  final int batchSize;

  /// 批处理超时
  final Duration batchTimeout;
}

/// 性能优化配置
class PerformanceOptimizerConfig {
  const PerformanceOptimizerConfig({
    this.memoryPoolConfig = const MemoryPoolConfig(),
    this.cacheConfig = const CacheConfig(),
    this.asyncConfig = const AsyncConfig(),
    this.enableMemoryOptimization = true,
    this.enableCacheOptimization = true,
    this.enableAsyncOptimization = true,
    this.enableIsolatePool = false,
    this.gcInterval = const Duration(minutes: 5),
  });

  /// 内存池配置
  final MemoryPoolConfig memoryPoolConfig;

  /// 缓存配置
  final CacheConfig cacheConfig;

  /// 异步配置
  final AsyncConfig asyncConfig;

  /// 启用内存优化
  final bool enableMemoryOptimization;

  /// 启用缓存优化
  final bool enableCacheOptimization;

  /// 启用异步优化
  final bool enableAsyncOptimization;

  /// 启用Isolate池
  final bool enableIsolatePool;

  /// GC间隔
  final Duration gcInterval;
}

/// 缓存条目
class CacheEntry<T> {
  CacheEntry({
    required this.value,
    required this.timestamp,
    required this.accessCount,
  });

  /// 缓存值
  final T value;

  /// 时间戳
  final DateTime timestamp;

  /// 访问次数
  int accessCount;

  /// 最后访问时间
  DateTime lastAccessed = DateTime.now();

  /// 是否过期
  bool isExpired(Duration ttl) => DateTime.now().difference(timestamp) > ttl;

  /// 更新访问信息
  void updateAccess() {
    accessCount++;
    lastAccessed = DateTime.now();
  }
}

/// LRU缓存实现
class LRUCache<K, V> {
  LRUCache({
    required this.maxSize,
    required this.ttl,
  });

  /// 最大大小
  final int maxSize;

  /// 生存时间
  final Duration ttl;

  /// 缓存数据
  final Map<K, CacheEntry<V>> _cache = <K, CacheEntry<V>>{};

  /// 访问顺序
  final Queue<K> _accessOrder = Queue<K>();

  /// 获取缓存值
  V? get(K key) {
    final entry = _cache[key];
    if (entry == null) return null;

    // 检查是否过期
    if (entry.isExpired(ttl)) {
      remove(key);
      return null;
    }

    // 更新访问信息
    entry.updateAccess();
    _updateAccessOrder(key);

    return entry.value;
  }

  /// 设置缓存值
  void put(K key, V value) {
    // 如果已存在，更新值
    if (_cache.containsKey(key)) {
      _cache[key] = CacheEntry(
        value: value,
        timestamp: DateTime.now(),
        accessCount: _cache[key]!.accessCount + 1,
      );
      _updateAccessOrder(key);
      return;
    }

    // 检查容量限制
    if (_cache.length >= maxSize) {
      _evictLRU();
    }

    // 添加新条目
    _cache[key] = CacheEntry(
      value: value,
      timestamp: DateTime.now(),
      accessCount: 1,
    );
    _accessOrder.addLast(key);
  }

  /// 移除缓存值
  void remove(K key) {
    _cache.remove(key);
    _accessOrder.remove(key);
  }

  /// 清空缓存
  void clear() {
    _cache.clear();
    _accessOrder.clear();
  }

  /// 获取缓存大小
  int get size => _cache.length;

  /// 获取命中率
  double get hitRate {
    final int totalAccess = _cache.values.fold<int>(
        0, (int sum, CacheEntry<V> entry) => sum + entry.accessCount);
    final int hits = _cache.values
        .where((CacheEntry<V> entry) => entry.accessCount > 1)
        .length;
    return totalAccess > 0 ? hits / totalAccess : 0.0;
  }

  /// 更新访问顺序
  void _updateAccessOrder(K key) {
    _accessOrder.remove(key);
    _accessOrder.addLast(key);
  }

  /// 淘汰最近最少使用的条目
  void _evictLRU() {
    if (_accessOrder.isNotEmpty) {
      final lruKey = _accessOrder.removeFirst();
      _cache.remove(lruKey);
    }
  }

  /// 清理过期条目
  void cleanupExpired() {
    final expiredKeys = <K>[];
    for (final entry in _cache.entries) {
      if (entry.value.isExpired(ttl)) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      remove(key);
    }
  }
}

/// 对象池实现
class ObjectPool<T> {
  ObjectPool({
    required this.factory,
    required this.reset,
    required this.config,
  }) {
    // 预创建初始对象
    for (int i = 0; i < config.initialSize; i++) {
      _available.add(factory());
    }
  }

  /// 对象工厂
  final T Function() factory;

  /// 对象重置函数
  final void Function(T) reset;

  /// 配置
  final MemoryPoolConfig config;

  /// 可用对象
  final Queue<T> _available = Queue<T>();

  /// 使用中的对象
  final Set<T> _inUse = <T>{};

  /// 最后使用时间
  final Map<T, DateTime> _lastUsed = <T, DateTime>{};

  /// 获取对象
  T acquire() {
    late T obj;

    if (_available.isNotEmpty) {
      obj = _available.removeFirst();
    } else if (_inUse.length < config.maxSize) {
      obj = factory();
    } else {
      // 池已满，等待或抛出异常
      throw StateError('Object pool is full');
    }

    _inUse.add(obj);
    _lastUsed[obj] = DateTime.now();
    return obj;
  }

  /// 释放对象
  void release(T obj) {
    if (!_inUse.contains(obj)) return;

    _inUse.remove(obj);
    reset(obj);
    _available.add(obj);
    _lastUsed[obj] = DateTime.now();
  }

  /// 清理空闲对象
  void cleanup() {
    final now = DateTime.now();
    final toRemove = <T>[];

    for (final obj in _available) {
      final lastUsed = _lastUsed[obj];
      if (lastUsed != null && now.difference(lastUsed) > config.maxIdleTime) {
        toRemove.add(obj);
      }
    }

    for (final obj in toRemove) {
      _available.remove(obj);
      _lastUsed.remove(obj);
    }

    // 如果池太小，添加新对象
    while (_available.length < config.initialSize &&
        (_available.length + _inUse.length) < config.maxSize) {
      _available.add(factory());
    }
  }

  /// 获取统计信息
  Map<String, dynamic> getStats() => <String, dynamic>{
        'available': _available.length,
        'inUse': _inUse.length,
        'total': _available.length + _inUse.length,
        'maxSize': config.maxSize,
        'utilization': _inUse.length / config.maxSize,
      };
}

/// 异步任务队列
class AsyncTaskQueue<T> {
  AsyncTaskQueue({
    required this.config,
  });

  /// 配置
  final AsyncConfig config;

  /// 任务队列
  final Queue<_AsyncTask<T>> _queue = Queue<_AsyncTask<T>>();

  /// 活跃任务数
  int _activeTasks = 0;

  /// 批处理缓冲区
  final List<_AsyncTask<T>> _batchBuffer = <_AsyncTask<T>>[];

  /// 批处理定时器
  Timer? _batchTimer;

  /// 添加任务
  Future<T> addTask(Future<T> Function() task) {
    final completer = Completer<T>();
    final asyncTask = _AsyncTask<T>(
      task: task,
      completer: completer,
      timestamp: DateTime.now(),
    );

    if (_queue.length >= config.queueSize) {
      completer.completeError(StateError('Task queue is full'));
      return completer.future;
    }

    if (config.enableBatching) {
      _addToBatch(asyncTask);
    } else {
      _queue.add(asyncTask);
      _processQueue();
    }

    return completer.future;
  }

  /// 添加到批处理
  void _addToBatch(_AsyncTask<T> task) {
    _batchBuffer.add(task);

    if (_batchBuffer.length >= config.batchSize) {
      _flushBatch();
    } else {
      _batchTimer?.cancel();
      _batchTimer = Timer(config.batchTimeout, _flushBatch);
    }
  }

  /// 刷新批处理
  void _flushBatch() {
    _batchTimer?.cancel();
    _queue.addAll(_batchBuffer);
    _batchBuffer.clear();
    _processQueue();
  }

  /// 处理队列
  void _processQueue() {
    while (_queue.isNotEmpty && _activeTasks < config.maxConcurrency) {
      final task = _queue.removeFirst();
      _activeTasks++;

      _executeTask(task).whenComplete(() {
        _activeTasks--;
        _processQueue();
      });
    }
  }

  /// 执行任务
  Future<void> _executeTask(_AsyncTask<T> task) async {
    try {
      final result = await task.task().timeout(config.timeoutDuration);
      task.completer.complete(result);
    } catch (e) {
      task.completer.completeError(e);
    }
  }

  /// 获取统计信息
  Map<String, dynamic> getStats() => <String, dynamic>{
        'queueSize': _queue.length,
        'activeTasks': _activeTasks,
        'batchBufferSize': _batchBuffer.length,
        'maxConcurrency': config.maxConcurrency,
        'utilization': _activeTasks / config.maxConcurrency,
      };

  /// 清理资源
  void dispose() {
    _batchTimer?.cancel();
    _queue.clear();
    _batchBuffer.clear();
  }
}

/// 异步任务
class _AsyncTask<T> {
  _AsyncTask({
    required this.task,
    required this.completer,
    required this.timestamp,
  });

  /// 任务函数
  final Future<T> Function() task;

  /// 完成器
  final Completer<T> completer;

  /// 时间戳
  final DateTime timestamp;
}

/// 插件性能优化器
///
/// 提供内存池管理、缓存优化、异步处理优化等功能
class PluginPerformanceOptimizer {
  PluginPerformanceOptimizer._();
  static final PluginPerformanceOptimizer _instance =
      PluginPerformanceOptimizer._();
  static PluginPerformanceOptimizer get instance => _instance;

  /// 配置
  PerformanceOptimizerConfig _config = const PerformanceOptimizerConfig();

  /// 缓存实例
  final Map<String, LRUCache<String, dynamic>> _caches =
      <String, LRUCache<String, dynamic>>{};

  /// 对象池实例
  final Map<String, ObjectPool<dynamic>> _objectPools =
      <String, ObjectPool<dynamic>>{};

  /// 异步任务队列
  AsyncTaskQueue<dynamic>? _taskQueue;

  /// GC定时器
  Timer? _gcTimer;

  /// 是否已初始化
  bool _initialized = false;

  /// 初始化
  Future<void> initialize({PerformanceOptimizerConfig? config}) async {
    if (_initialized) return;

    _config = config ?? _config;

    // 初始化异步任务队列
    if (_config.enableAsyncOptimization) {
      _taskQueue = AsyncTaskQueue<dynamic>(config: _config.asyncConfig);
    }

    // 启动GC定时器
    _startGCTimer();

    _initialized = true;
  }

  /// 获取或创建缓存
  LRUCache<String, T> getCache<T>(String name, {CacheConfig? config}) {
    if (!_config.enableCacheOptimization) {
      throw StateError('Cache optimization is disabled');
    }

    final cacheConfig = config ?? _config.cacheConfig;

    if (!_caches.containsKey(name)) {
      _caches[name] = LRUCache<String, dynamic>(
        maxSize: cacheConfig.maxSize,
        ttl: cacheConfig.ttl,
      );
    }

    return _caches[name]! as LRUCache<String, T>;
  }

  /// 获取或创建对象池
  ObjectPool<T> getObjectPool<T>(
    String name,
    T Function() factory,
    void Function(T) reset, {
    MemoryPoolConfig? config,
  }) {
    if (!_config.enableMemoryOptimization) {
      throw StateError('Memory optimization is disabled');
    }

    final poolConfig = config ?? _config.memoryPoolConfig;

    if (!_objectPools.containsKey(name)) {
      _objectPools[name] = ObjectPool<T>(
        factory: factory,
        reset: reset,
        config: poolConfig,
      );
    }

    return _objectPools[name]! as ObjectPool<T>;
  }

  /// 提交异步任务
  Future<T> submitTask<T>(Future<T> Function() task) {
    if (!_config.enableAsyncOptimization || _taskQueue == null) {
      return task();
    }

    return _taskQueue!.addTask(task) as Future<T>;
  }

  /// 启动GC定时器
  void _startGCTimer() {
    _gcTimer?.cancel();
    _gcTimer = Timer.periodic(_config.gcInterval, (_) {
      _performGC();
    });
  }

  /// 执行垃圾回收
  void _performGC() {
    // 清理过期缓存
    for (final cache in _caches.values) {
      cache.cleanupExpired();
    }

    // 清理空闲对象池
    for (final pool in _objectPools.values) {
      pool.cleanup();
    }
  }

  /// 获取性能统计
  Map<String, dynamic> getPerformanceStats() {
    final stats = <String, dynamic>{
      'initialized': _initialized,
      'config': <String, bool>{
        'memoryOptimization': _config.enableMemoryOptimization,
        'cacheOptimization': _config.enableCacheOptimization,
        'asyncOptimization': _config.enableAsyncOptimization,
      },
    };

    // 缓存统计
    if (_config.enableCacheOptimization) {
      stats['caches'] = <String, dynamic>{};
      for (final entry in _caches.entries) {
        stats['caches'][entry.key] = <String, num>{
          'size': entry.value.size,
          'hitRate': entry.value.hitRate,
        };
      }
    }

    // 对象池统计
    if (_config.enableMemoryOptimization) {
      stats['objectPools'] = <String, dynamic>{};
      for (final entry in _objectPools.entries) {
        stats['objectPools'][entry.key] = entry.value.getStats();
      }
    }

    // 异步任务统计
    if (_config.enableAsyncOptimization && _taskQueue != null) {
      stats['asyncQueue'] = _taskQueue!.getStats();
    }

    return stats;
  }

  /// 清理资源
  Future<void> dispose() async {
    _gcTimer?.cancel();
    _taskQueue?.dispose();
    _caches.clear();
    _objectPools.clear();
    _initialized = false;
  }
}
