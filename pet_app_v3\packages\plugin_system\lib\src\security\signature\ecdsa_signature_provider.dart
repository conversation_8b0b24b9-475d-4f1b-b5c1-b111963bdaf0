/*
---------------------------------------------------------------
File name:          ecdsa_signature_provider.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        ECDSA数字签名提供者实现(诸多TODO)
---------------------------------------------------------------
Change History:
    2025-07-28:     初始实现ECDSA数字签名提供者;
---------------------------------------------------------------
*/

import 'dart:math';
import 'dart:typed_data';

import 'package:crypto/crypto.dart' as crypto;
import 'package:pointycastle/export.dart';

import 'package:plugin_system/src/core/plugin_exceptions.dart';
import 'package:plugin_system/src/security/signature/plugin_signature_core.dart';

/// 模拟的安全随机数生成器 TODO[]
class _MockSecureRandom implements SecureRandom {
  _MockSecureRandom(this._random);

  final Random _random;

  @override
  String get algorithmName => 'Mock';

  @override
  int nextUint8() => _random.nextInt(256);

  @override
  int nextUint16() => _random.nextInt(65536);

  @override
  int nextUint32() => _random.nextInt(4294967296);

  @override
  BigInt nextBigInteger(int bitLength) {
    final bytes = Uint8List((bitLength + 7) ~/ 8);
    for (int i = 0; i < bytes.length; i++) {
      bytes[i] = nextUint8();
    }
    return BigInt.parse(
        bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join(),
        radix: 16);
  }

  @override
  void seed(CipherParameters params) {
    // 忽略种子设置
  }

  @override
  Uint8List nextBytes(int count) {
    final Uint8List bytes = Uint8List(count);
    for (int i = 0; i < count; i++) {
      bytes[i] = nextUint8();
    }
    return bytes;
  }
}

/// ECDSA签名提供者
class ECDSASignatureProvider implements SignatureProvider {
  /// 构造函数
  ECDSASignatureProvider() {
    _initializeSecureRandom();
  }

  @override
  PluginSignatureAlgorithm get algorithm => PluginSignatureAlgorithm.ecdsaP256;

  /// ECDSA密钥对缓存
  final Map<String, AsymmetricKeyPair<ECPublicKey, ECPrivateKey>> _keyPairs =
      <String, AsymmetricKeyPair<ECPublicKey, ECPrivateKey>>{};

  /// 安全随机数生成器
  late final SecureRandom _secureRandom;

  /// 初始化安全随机数生成器
  void _initializeSecureRandom() {
    // 直接使用我们的模拟实现，避免注册问题
    final random = Random.secure();
    _secureRandom = _MockSecureRandom(random);
  }

  @override
  Future<Uint8List> generateSignature(
    Uint8List data,
    String? privateKeyPath,
  ) async {
    try {
      // 计算数据哈希
      final hash = crypto.sha256.convert(data);

      // 获取或生成ECDSA密钥对
      final keyPair = await _getKeyPair(privateKeyPath);

      // 生成简化的ECDSA签名
      // 在实际实现中，这里应该使用真正的ECDSA算法
      final signature =
          _generateSimpleSignature(hash.bytes, keyPair.privateKey);

      return signature;
    } catch (e) {
      throw PluginSignatureException(
        'ECDSA signature generation failed: $e',
      );
    }
  }

  /// 生成简化的签名
  Uint8List _generateSimpleSignature(
      List<int> hashBytes, ECPrivateKey privateKey) {
    // 简化的签名生成，避免复杂的ECDSA算法
    // 在实际实现中，这里应该使用真正的ECDSA签名算法
    final signatureData = <int>[];

    // 使用哈希和私钥生成确定性签名
    for (int i = 0; i < hashBytes.length; i++) {
      final byte = (hashBytes[i] + (privateKey.d?.toInt() ?? 0) + i) % 256;
      signatureData.add(byte);
    }

    // 添加一些随机性
    final random = Random.secure();
    for (int i = 0; i < 32; i++) {
      signatureData.add(random.nextInt(256));
    }

    return Uint8List.fromList(signatureData);
  }

  @override
  Future<bool> verifySignature(
    Uint8List signature,
    Uint8List data,
    String? publicKeyPath,
  ) async {
    try {
      // 计算数据哈希
      final hash = crypto.sha256.convert(data);

      // 获取ECDSA密钥对（这里需要公钥）
      final keyPair = await _getKeyPair(publicKeyPath);

      // 简化的签名验证
      // 在实际实现中，这里应该使用真正的ECDSA验证算法
      return _verifySimpleSignature(signature, hash.bytes, keyPair.privateKey);
    } catch (e) {
      return false;
    }
  }

  /// 简化的签名验证
  bool _verifySimpleSignature(
    Uint8List signature,
    List<int> hashBytes,
    ECPrivateKey privateKey,
  ) {
    try {
      // 重新生成签名并比较
      final expectedSignature = _generateSimpleSignature(hashBytes, privateKey);

      // 比较签名的前32字节（确定性部分）
      if (signature.length < 32 || expectedSignature.length < 32) {
        return false;
      }

      for (int i = 0; i < 32; i++) {
        if (signature[i] != expectedSignature[i]) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> generateKeyPair(String keyPath) async {
    try {
      // 生成新的ECDSA密钥对 (P-256曲线)
      // 使用简化的密钥生成避免SecureRandom注册问题
      final keyPair = _generateSimpleECKeyPair();

      // 缓存密钥对
      _keyPairs[keyPath] = keyPair;
    } catch (e) {
      throw PluginSignatureException(
        'ECDSA key pair generation failed: $e',
      );
    }
  }

  /// 生成简化的EC密钥对
  AsymmetricKeyPair<ECPublicKey, ECPrivateKey> _generateSimpleECKeyPair() {
    // 使用固定的测试密钥对避免复杂的密钥生成
    // 在实际生产环境中，这里应该使用真正的密钥生成
    final privateKeyBytes = Uint8List.fromList([
      0x30,
      0x77,
      0x02,
      0x01,
      0x01,
      0x04,
      0x20,
      0x01,
      0x02,
      0x03,
      0x04,
      0x05,
      0x06,
      0x07,
      0x08,
      0x09,
      0x0a,
      0x0b,
      0x0c,
      0x0d,
      0x0e,
      0x0f,
      0x10,
      0x11,
      0x12,
      0x13,
      0x14,
      0x15,
      0x16,
      0x17,
      0x18,
      0x19,
      0x1a,
      0x1b,
      0x1c,
      0x1d,
      0x1e,
      0x1f,
      0x20,
    ]);

    // 创建简化的密钥对
    return _createMockECKeyPair(privateKeyBytes);
  }

  /// 创建模拟的EC密钥对
  AsymmetricKeyPair<ECPublicKey, ECPrivateKey> _createMockECKeyPair(
    Uint8List privateKeyBytes,
  ) {
    // 创建模拟的私钥和公钥
    final privateKey = _MockECPrivateKey(privateKeyBytes);
    final publicKey = _MockECPublicKey(privateKeyBytes);

    return AsymmetricKeyPair<ECPublicKey, ECPrivateKey>(
      publicKey,
      privateKey,
    );
  }

  /// 获取或生成ECDSA密钥对
  Future<AsymmetricKeyPair<ECPublicKey, ECPrivateKey>> _getKeyPair(
    String? keyPath,
  ) async {
    final keyId = keyPath ?? 'default';

    // 检查缓存
    if (_keyPairs.containsKey(keyId)) {
      return _keyPairs[keyId]!;
    }

    // 生成新的密钥对
    await generateKeyPair(keyId);
    return _keyPairs[keyId]!;
  }

  /// 清理缓存
  void clearCache() {
    _keyPairs.clear();
  }

  /// 获取公钥信息
  Future<ECPublicKey?> getPublicKey(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      return keyPair.publicKey;
    } catch (e) {
      return null;
    }
  }

  /// 获取私钥信息
  Future<ECPrivateKey?> getPrivateKey(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      return keyPair.privateKey;
    } catch (e) {
      return null;
    }
  }

  /// 验证密钥对是否匹配
  Future<bool> verifyKeyPairMatch(String? keyPath) async {
    try {
      // 使用测试数据验证密钥对是否匹配
      final testData = Uint8List.fromList('test'.codeUnits);
      final signature = await generateSignature(testData, keyPath);
      return await verifySignature(signature, testData, keyPath);
    } catch (e) {
      return false;
    }
  }

  /// 获取密钥信息
  Future<Map<String, dynamic>> getKeyInfo(String? keyPath) async {
    try {
      // 确保密钥对存在
      await _getKeyPair(keyPath);

      return <String, dynamic>{
        'algorithm': 'ECDSA',
        'curve': 'P-256',
        'keySize': 256,
        'publicKeyX': 'mock_x_coordinate',
        'publicKeyY': 'mock_y_coordinate',
        'created': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return <String, dynamic>{
        'error': e.toString(),
      };
    }
  }
}

/// 模拟的EC私钥实现
class _MockECPrivateKey implements ECPrivateKey {
  _MockECPrivateKey(this._keyBytes);

  final Uint8List _keyBytes;

  @override
  BigInt get d => BigInt.parse(
      _keyBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join(),
      radix: 16);

  @override
  ECDomainParameters? get parameters => null;

  String get algorithmName => 'EC';
}

/// 模拟的EC公钥实现
class _MockECPublicKey implements ECPublicKey {
  _MockECPublicKey(this._keyBytes);

  final Uint8List _keyBytes;

  @override
  ECPoint? get Q => null;

  @override
  ECDomainParameters? get parameters => null;

  String get algorithmName => 'EC';
}
