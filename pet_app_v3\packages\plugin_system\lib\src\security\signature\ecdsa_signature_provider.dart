/*
---------------------------------------------------------------
File name:          ecdsa_signature_provider.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        ECDSA数字签名提供者实现
---------------------------------------------------------------
Change History:
    2025-07-28:     初始实现ECDSA数字签名提供者;
---------------------------------------------------------------
*/

import 'dart:math';
import 'dart:typed_data';

import 'package:crypto/crypto.dart' as crypto;
import 'package:pointycastle/export.dart';

import 'package:plugin_system/src/core/plugin_exceptions.dart';
import 'package:plugin_system/src/security/signature/plugin_signature_core.dart';

/// 模拟的安全随机数生成器
class _MockSecureRandom implements SecureRandom {
  _MockSecureRandom(this._random);

  final Random _random;

  @override
  String get algorithmName => 'Mock';

  @override
  int nextUint8() => _random.nextInt(256);

  @override
  int nextUint16() => _random.nextInt(65536);

  @override
  int nextUint32() => _random.nextInt(4294967296);

  @override
  BigInt nextBigInteger(int bitLength) {
    final bytes = Uint8List((bitLength + 7) ~/ 8);
    for (int i = 0; i < bytes.length; i++) {
      bytes[i] = nextUint8();
    }
    return BigInt.parse(
        bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join(),
        radix: 16);
  }

  @override
  void seed(CipherParameters params) {
    // 忽略种子设置
  }

  @override
  Uint8List nextBytes(int count) {
    final Uint8List bytes = Uint8List(count);
    for (int i = 0; i < count; i++) {
      bytes[i] = nextUint8();
    }
    return bytes;
  }
}

/// ECDSA签名提供者
class ECDSASignatureProvider implements SignatureProvider {
  /// 构造函数
  ECDSASignatureProvider() {
    _initializeSecureRandom();
  }

  @override
  PluginSignatureAlgorithm get algorithm => PluginSignatureAlgorithm.ecdsaP256;

  /// ECDSA密钥对缓存
  final Map<String, AsymmetricKeyPair<ECPublicKey, ECPrivateKey>> _keyPairs =
      <String, AsymmetricKeyPair<ECPublicKey, ECPrivateKey>>{};

  /// 安全随机数生成器
  late final SecureRandom _secureRandom;

  /// 初始化安全随机数生成器
  void _initializeSecureRandom() {
    try {
      _secureRandom = FortunaRandom();
      final seed = Uint8List(32);
      final random = Random.secure();
      for (int i = 0; i < seed.length; i++) {
        seed[i] = random.nextInt(256);
      }
      _secureRandom.seed(KeyParameter(seed));
    } catch (e) {
      // 如果Fortuna不可用，使用简单的随机数生成器
      final random = Random.secure();
      _secureRandom = _MockSecureRandom(random);
    }
  }

  @override
  Future<Uint8List> generateSignature(
    Uint8List data,
    String? privateKeyPath,
  ) async {
    try {
      // 计算数据哈希
      final hash = crypto.sha256.convert(data);

      // 获取或生成ECDSA密钥对
      final keyPair = await _getKeyPair(privateKeyPath);

      // 创建ECDSA签名器
      final signer = ECDSASigner(SHA256Digest());
      signer.init(true, PrivateKeyParameter<ECPrivateKey>(keyPair.privateKey));

      // 生成签名
      final signature =
          signer.generateSignature(Uint8List.fromList(hash.bytes));

      // 将r和s值编码为DER格式
      return _encodeECDSASignature(signature as ECSignature);
    } catch (e) {
      throw PluginSignatureException(
        'ECDSA signature generation failed: $e',
      );
    }
  }

  @override
  Future<bool> verifySignature(
    Uint8List signature,
    Uint8List data,
    String? publicKeyPath,
  ) async {
    try {
      // 计算数据哈希
      final hash = crypto.sha256.convert(data);

      // 获取ECDSA密钥对（这里需要公钥）
      final keyPair = await _getKeyPair(publicKeyPath);

      // 解码ECDSA签名
      final ecSignature = _decodeECDSASignature(signature);

      // 创建ECDSA验证器
      final verifier = ECDSASigner(SHA256Digest());
      verifier.init(false, PublicKeyParameter<ECPublicKey>(keyPair.publicKey));

      // 验证签名
      return verifier.verifySignature(
          Uint8List.fromList(hash.bytes), ecSignature);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> generateKeyPair(String keyPath) async {
    try {
      // 生成新的ECDSA密钥对 (P-256曲线)
      final keyGen = ECKeyGenerator();
      final ecDomainParams = ECDomainParameters('prime256v1');
      keyGen.init(ParametersWithRandom(
        ECKeyGeneratorParameters(ecDomainParams),
        _secureRandom,
      ));

      final keyPair = keyGen.generateKeyPair();
      final ecdsaKeyPair = AsymmetricKeyPair<ECPublicKey, ECPrivateKey>(
        keyPair.publicKey as ECPublicKey,
        keyPair.privateKey as ECPrivateKey,
      );

      // 缓存密钥对
      _keyPairs[keyPath] = ecdsaKeyPair;
    } catch (e) {
      throw PluginSignatureException(
        'ECDSA key pair generation failed: $e',
      );
    }
  }

  /// 获取或生成ECDSA密钥对
  Future<AsymmetricKeyPair<ECPublicKey, ECPrivateKey>> _getKeyPair(
    String? keyPath,
  ) async {
    final keyId = keyPath ?? 'default';

    // 检查缓存
    if (_keyPairs.containsKey(keyId)) {
      return _keyPairs[keyId]!;
    }

    // 生成新的密钥对
    await generateKeyPair(keyId);
    return _keyPairs[keyId]!;
  }

  /// 编码ECDSA签名为DER格式
  /// TODO(ecdsa_signature): 实现完整的DER编码
  Uint8List _encodeECDSASignature(ECSignature signature) {
    // 简化的DER编码实现
    final rBytes = _bigIntToBytes(signature.r);
    final sBytes = _bigIntToBytes(signature.s);

    return Uint8List.fromList([
      ...rBytes,
      ...sBytes,
    ]);
  }

  /// 解码ECDSA签名
  /// TODO(ecdsa_signature): 实现完整的DER解码
  ECSignature _decodeECDSASignature(Uint8List signature) {
    // 简化的解码实现
    final halfLength = signature.length ~/ 2;
    final rBytes = signature.sublist(0, halfLength);
    final sBytes = signature.sublist(halfLength);

    final r = _bytesToBigInt(rBytes);
    final s = _bytesToBigInt(sBytes);

    return ECSignature(r, s);
  }

  /// 将BigInt转换为字节数组
  Uint8List _bigIntToBytes(BigInt value) {
    final hexString = value.toRadixString(16);
    final paddedHex = hexString.length.isOdd ? '0$hexString' : hexString;
    final bytes = <int>[];

    for (int i = 0; i < paddedHex.length; i += 2) {
      final byteString = paddedHex.substring(i, i + 2);
      bytes.add(int.parse(byteString, radix: 16));
    }

    return Uint8List.fromList(bytes);
  }

  /// 将字节数组转换为BigInt
  BigInt _bytesToBigInt(Uint8List bytes) {
    BigInt result = BigInt.zero;
    for (int i = 0; i < bytes.length; i++) {
      result = (result << 8) + BigInt.from(bytes[i]);
    }
    return result;
  }

  /// 清理缓存
  void clearCache() {
    _keyPairs.clear();
  }

  /// 获取公钥信息
  Future<ECPublicKey?> getPublicKey(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      return keyPair.publicKey;
    } catch (e) {
      return null;
    }
  }

  /// 获取私钥信息
  Future<ECPrivateKey?> getPrivateKey(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      return keyPair.privateKey;
    } catch (e) {
      return null;
    }
  }

  /// 验证密钥对是否匹配
  Future<bool> verifyKeyPairMatch(String? keyPath) async {
    try {
      // 使用测试数据验证密钥对是否匹配
      final testData = Uint8List.fromList('test'.codeUnits);
      final signature = await generateSignature(testData, keyPath);
      return await verifySignature(signature, testData, keyPath);
    } catch (e) {
      return false;
    }
  }

  /// 获取密钥信息
  Future<Map<String, dynamic>> getKeyInfo(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      final publicKey = keyPair.publicKey;

      return <String, dynamic>{
        'algorithm': 'ECDSA',
        'curve': 'P-256',
        'keySize': 256,
        'publicKeyX': publicKey.Q!.x!.toBigInteger().toString(),
        'publicKeyY': publicKey.Q!.y!.toBigInteger().toString(),
        'created': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return <String, dynamic>{
        'error': e.toString(),
      };
    }
  }
}
