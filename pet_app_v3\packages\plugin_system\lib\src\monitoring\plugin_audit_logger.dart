/*
---------------------------------------------------------------
File name:          plugin_audit_logger.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        插件系统审计日志记录器 - Phase 3.3 监控日志实现
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 3.3 - 监控日志系统实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';

/// 审计日志级别
enum AuditLogLevel {
  /// 调试信息
  debug,

  /// 一般信息
  info,

  /// 警告
  warning,

  /// 错误
  error,

  /// 严重错误
  critical,
}

/// 审计事件类型
enum AuditEventType {
  /// 插件安装
  pluginInstall,

  /// 插件卸载
  pluginUninstall,

  /// 插件更新
  pluginUpdate,

  /// 插件发布
  pluginPublish,

  /// 插件验证
  pluginValidation,

  /// 插件构建
  pluginBuild,

  /// 插件同步
  pluginSync,

  /// 批量操作
  batchOperation,

  /// 系统配置
  systemConfig,

  /// 安全事件
  security,

  /// 性能事件
  performance,
}

/// 审计日志条目
class AuditLogEntry {
  const AuditLogEntry({
    required this.id,
    required this.timestamp,
    required this.level,
    required this.eventType,
    required this.message,
    this.pluginId,
    this.userId,
    this.sessionId,
    this.details,
    this.metadata,
    this.duration,
    this.error,
    this.stackTrace,
  });

  /// 从JSON创建
  factory AuditLogEntry.fromJson(Map<String, dynamic> json) => AuditLogEntry(
        id: json['id'] as String,
        timestamp: DateTime.parse(json['timestamp'] as String),
        level: AuditLogLevel.values.firstWhere(
          (AuditLogLevel e) => e.name == json['level'],
          orElse: () => AuditLogLevel.info,
        ),
        eventType: AuditEventType.values.firstWhere(
          (AuditEventType e) => e.name == json['eventType'],
          orElse: () => AuditEventType.systemConfig,
        ),
        message: json['message'] as String,
        pluginId: json['pluginId'] as String?,
        userId: json['userId'] as String?,
        sessionId: json['sessionId'] as String?,
        details: json['details'] as Map<String, dynamic>?,
        metadata: json['metadata'] as Map<String, dynamic>?,
        duration: json['duration'] != null
            ? Duration(milliseconds: json['duration'] as int)
            : null,
        error: json['error'] as String?,
        stackTrace: json['stackTrace'] as String?,
      );

  /// 日志ID
  final String id;

  /// 时间戳
  final DateTime timestamp;

  /// 日志级别
  final AuditLogLevel level;

  /// 事件类型
  final AuditEventType eventType;

  /// 消息
  final String message;

  /// 插件ID
  final String? pluginId;

  /// 用户ID
  final String? userId;

  /// 会话ID
  final String? sessionId;

  /// 详细信息
  final Map<String, dynamic>? details;

  /// 元数据
  final Map<String, dynamic>? metadata;

  /// 执行时长
  final Duration? duration;

  /// 错误信息
  final String? error;

  /// 堆栈跟踪
  final String? stackTrace;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'timestamp': timestamp.toIso8601String(),
        'level': level.name,
        'eventType': eventType.name,
        'message': message,
        'pluginId': pluginId,
        'userId': userId,
        'sessionId': sessionId,
        'details': details,
        'metadata': metadata,
        'duration': duration?.inMilliseconds,
        'error': error,
        'stackTrace': stackTrace,
      };
}

/// 审计日志配置
class AuditLogConfig {
  const AuditLogConfig({
    this.enableFileLogging = true,
    this.enableConsoleLogging = true,
    this.enableRemoteLogging = false,
    this.logLevel = AuditLogLevel.info,
    this.maxFileSize = 10 * 1024 * 1024, // 10MB
    this.maxFiles = 5,
    this.logDirectory = 'logs',
    this.logFileName = 'plugin_audit.log',
    this.remoteEndpoint,
    this.remoteApiKey,
    this.bufferSize = 100,
    this.flushInterval = const Duration(seconds: 30),
  });

  /// 启用文件日志
  final bool enableFileLogging;

  /// 启用控制台日志
  final bool enableConsoleLogging;

  /// 启用远程日志
  final bool enableRemoteLogging;

  /// 日志级别
  final AuditLogLevel logLevel;

  /// 最大文件大小
  final int maxFileSize;

  /// 最大文件数量
  final int maxFiles;

  /// 日志目录
  final String logDirectory;

  /// 日志文件名
  final String logFileName;

  /// 远程端点
  final String? remoteEndpoint;

  /// 远程API密钥
  final String? remoteApiKey;

  /// 缓冲区大小
  final int bufferSize;

  /// 刷新间隔
  final Duration flushInterval;
}

/// 插件审计日志记录器
///
/// 提供结构化的插件系统操作审计和监控功能
class PluginAuditLogger {
  PluginAuditLogger._();
  static final PluginAuditLogger _instance = PluginAuditLogger._();
  static PluginAuditLogger get instance => _instance;

  /// 配置
  AuditLogConfig _config = const AuditLogConfig();

  /// 日志缓冲区
  final List<AuditLogEntry> _buffer = <AuditLogEntry>[];

  /// 刷新定时器
  Timer? _flushTimer;

  /// 会话ID
  String? _sessionId;

  /// 用户ID
  String? _userId;

  /// 是否已初始化
  bool _initialized = false;

  /// 初始化
  Future<void> initialize({
    AuditLogConfig? config,
    String? sessionId,
    String? userId,
  }) async {
    if (_initialized) return;

    _config = config ?? _config;
    _sessionId = sessionId ?? _generateSessionId();
    _userId = userId;

    // 创建日志目录
    if (_config.enableFileLogging) {
      await _ensureLogDirectory();
    }

    // 启动刷新定时器
    _startFlushTimer();

    _initialized = true;

    // 记录初始化日志
    await log(
      level: AuditLogLevel.info,
      eventType: AuditEventType.systemConfig,
      message: '插件审计日志系统已初始化',
      details: <String, dynamic>{
        'config': <String, Object>{
          'fileLogging': _config.enableFileLogging,
          'consoleLogging': _config.enableConsoleLogging,
          'remoteLogging': _config.enableRemoteLogging,
          'logLevel': _config.logLevel.name,
        },
        'sessionId': _sessionId,
        'userId': _userId,
      },
    );
  }

  /// 记录日志
  Future<void> log({
    required AuditLogLevel level,
    required AuditEventType eventType,
    required String message,
    String? pluginId,
    Map<String, dynamic>? details,
    Map<String, dynamic>? metadata,
    Duration? duration,
    String? error,
    String? stackTrace,
  }) async {
    if (!_initialized) {
      await initialize();
    }

    // 检查日志级别
    if (level.index < _config.logLevel.index) {
      return;
    }

    final entry = AuditLogEntry(
      id: _generateLogId(),
      timestamp: DateTime.now(),
      level: level,
      eventType: eventType,
      message: message,
      pluginId: pluginId,
      userId: _userId,
      sessionId: _sessionId,
      details: details,
      metadata: metadata,
      duration: duration,
      error: error,
      stackTrace: stackTrace,
    );

    // 添加到缓冲区
    _buffer.add(entry);

    // 控制台输出
    if (_config.enableConsoleLogging) {
      _logToConsole(entry);
    }

    // 检查是否需要立即刷新
    if (_buffer.length >= _config.bufferSize) {
      await flush();
    }
  }

  /// 生成会话ID
  String _generateSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'session_$timestamp';
  }

  /// 生成日志ID
  String _generateLogId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecond;
    return 'log_${timestamp}_$random';
  }

  /// 刷新缓冲区
  Future<void> flush() async {
    if (_buffer.isEmpty) return;

    final entries = List<AuditLogEntry>.from(_buffer);
    _buffer.clear();

    // 写入文件
    if (_config.enableFileLogging) {
      await _writeToFile(entries);
    }

    // 发送到远程
    if (_config.enableRemoteLogging) {
      await _sendToRemote(entries);
    }
  }

  /// 启动刷新定时器
  void _startFlushTimer() {
    _flushTimer?.cancel();
    _flushTimer = Timer.periodic(_config.flushInterval, (_) async {
      await flush();
    });
  }

  /// 确保日志目录存在
  Future<void> _ensureLogDirectory() async {
    final directory = Directory(_config.logDirectory);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
  }

  /// 控制台输出
  void _logToConsole(AuditLogEntry entry) {
    final levelIcon = _getLevelIcon(entry.level);
    final timestamp = entry.timestamp.toIso8601String();
    final message =
        '$levelIcon [$timestamp] [${entry.eventType.name}] ${entry.message}';

    if (entry.pluginId != null) {
      print('$message (Plugin: ${entry.pluginId})');
    } else {
      print(message);
    }

    if (entry.error != null) {
      print('  Error: ${entry.error}');
    }

    if (entry.duration != null) {
      print('  Duration: ${entry.duration!.inMilliseconds}ms');
    }
  }

  /// 获取级别图标
  String _getLevelIcon(AuditLogLevel level) {
    switch (level) {
      case AuditLogLevel.debug:
        return '🔍';
      case AuditLogLevel.info:
        return 'ℹ️';
      case AuditLogLevel.warning:
        return '⚠️';
      case AuditLogLevel.error:
        return '❌';
      case AuditLogLevel.critical:
        return '🚨';
    }
  }

  /// 写入文件
  Future<void> _writeToFile(List<AuditLogEntry> entries) async {
    try {
      final logFile = File('${_config.logDirectory}/${_config.logFileName}');

      // 检查文件大小并轮转
      if (await logFile.exists()) {
        final fileSize = await logFile.length();
        if (fileSize >= _config.maxFileSize) {
          await _rotateLogFiles();
        }
      }

      // 写入日志条目
      final sink = logFile.openWrite(mode: FileMode.append);
      for (final entry in entries) {
        sink.writeln(jsonEncode(entry.toJson()));
      }
      await sink.close();
    } catch (e) {
      print('写入日志文件失败: $e');
    }
  }

  /// 轮转日志文件
  Future<void> _rotateLogFiles() async {
    try {
      final baseName = _config.logFileName.replaceAll('.log', '');

      // 删除最旧的文件
      final oldestFile = File(
        '${_config.logDirectory}/${baseName}_${_config.maxFiles - 1}.log',
      );
      if (await oldestFile.exists()) {
        await oldestFile.delete();
      }

      // 重命名现有文件
      for (int i = _config.maxFiles - 2; i >= 0; i--) {
        final currentFile = i == 0
            ? File('${_config.logDirectory}/${_config.logFileName}')
            : File('${_config.logDirectory}/${baseName}_$i.log');

        if (await currentFile.exists()) {
          final newFile =
              File('${_config.logDirectory}/${baseName}_${i + 1}.log');
          await currentFile.rename(newFile.path);
        }
      }
    } catch (e) {
      print('轮转日志文件失败: $e');
    }
  }

  /// 发送到远程
  Future<void> _sendToRemote(List<AuditLogEntry> entries) async {
    if (_config.remoteEndpoint == null) return;

    try {
      // 这里应该实现HTTP请求发送日志到远程服务器
      // 为了简化，这里只是模拟
      print('发送 ${entries.length} 条日志到远程服务器: ${_config.remoteEndpoint}');
    } catch (e) {
      print('发送远程日志失败: $e');
    }
  }

  /// 设置用户ID
  void setUserId(String userId) {
    _userId = userId;
  }

  /// 设置会话ID
  void setSessionId(String sessionId) {
    _sessionId = sessionId;
  }

  /// 清理资源
  Future<void> dispose() async {
    _flushTimer?.cancel();
    await flush();
    _initialized = false;
  }
}
