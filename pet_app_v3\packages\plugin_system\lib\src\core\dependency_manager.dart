/*
---------------------------------------------------------------
File name:          dependency_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-19
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件依赖管理器
---------------------------------------------------------------
Change History:
    2025-07-19: Initial creation - 插件依赖管理器;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:collection';

import 'package:pub_semver/pub_semver.dart';

import 'package:plugin_system/src/core/dependency_node.dart';
import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

/// 插件依赖管理器
///
/// 负责插件依赖的解析、验证和管理
class DependencyManager {
  DependencyManager._();

  /// 单例实例
  static final DependencyManager _instance = DependencyManager._();
  static DependencyManager get instance => _instance;

  /// 插件注册中心
  final PluginRegistry _registry = PluginRegistry.instance;

  /// 依赖图缓存
  final Map<String, Set<String>> _dependencyGraph = <String, Set<String>>{};

  /// 反向依赖图缓存
  final Map<String, Set<String>> _reverseDependencyGraph =
      <String, Set<String>>{};

  /// 解析插件依赖 (集成Creative Workshop算法)
  ///
  /// [plugins] 要解析的插件列表
  /// [installedPlugins] 已安装的插件映射
  /// [availablePlugins] 可用的插件映射（用于查找缺失依赖）
  Future<DependencyResolutionResult> resolveDependencies(
    List<Plugin> plugins, {
    Map<String, PluginInstallInfo>? installedPlugins,
    Map<String, PluginInstallInfo>? availablePlugins,
  }) async {
    try {
      // 转换插件列表为安装信息映射
      final Map<String, PluginInstallInfo> pluginInfoMap =
          <String, PluginInstallInfo>{};
      for (final Plugin plugin in plugins) {
        pluginInfoMap[plugin.id] = PluginInstallInfo.fromPlugin(plugin);
      }

      // 使用提供的映射或默认映射
      final Map<String, PluginInstallInfo> installed =
          installedPlugins ?? pluginInfoMap;
      final Map<String, PluginInstallInfo> available =
          availablePlugins ?? <String, PluginInstallInfo>{};

      // 1. 构建依赖图
      final Map<String, DependencyNode> dependencyGraph =
          _buildDependencyGraphAdvanced(plugins, installed, available);

      // 2. 检查缺失依赖
      final List<PluginDependency> missingDeps =
          _findMissingDependencies(plugins, installed, available);
      if (missingDeps.isNotEmpty) {
        return DependencyResolutionResult.failure(
          error: '存在缺失的依赖',
          missingDependencies: missingDeps,
        );
      }

      // 3. 检查版本冲突
      final List<DependencyConflict> conflicts =
          _findVersionConflicts(dependencyGraph, installed);
      if (conflicts.isNotEmpty) {
        return DependencyResolutionResult.failure(
          error: '存在版本冲突',
          conflicts: conflicts,
        );
      }

      // 4. 检查循环依赖
      final List<List<String>> cycles =
          _findCircularDependencies(dependencyGraph);
      if (cycles.isNotEmpty) {
        return DependencyResolutionResult.failure(
          error: '存在循环依赖',
          circularDependencies: cycles,
        );
      }

      // 5. 生成安装顺序（拓扑排序）
      final List<String> installOrder = _topologicalSort(dependencyGraph);

      return DependencyResolutionResult.success(
        loadOrder: installOrder,
      );
    } catch (e) {
      return DependencyResolutionResult.failure(
        error: '依赖解析失败: $e',
      );
    }
  }

  /// 检查插件依赖是否满足
  ///
  /// [plugin] 要检查的插件
  Future<bool> checkDependencies(Plugin plugin) async {
    try {
      // 1. 必需依赖检查
      final List<PluginDependency> requiredDeps =
          plugin.dependencies.where((dep) => !dep.optional).toList();

      for (final PluginDependency dependency in requiredDeps) {
        final Plugin? depPlugin = _registry.get(dependency.pluginId);

        if (depPlugin == null) {
          print('必需依赖缺失: ${dependency.pluginId}');
          return false;
        }

        // 检查依赖插件状态
        final PluginState? depState = _registry.getState(dependency.pluginId);
        if (depState == null || depState == PluginState.error) {
          print('依赖插件状态异常: ${dependency.pluginId} - $depState');
          return false;
        }
      }

      // 2. 可选依赖检查
      final List<PluginDependency> optionalDeps =
          plugin.dependencies.where((dep) => dep.optional).toList();

      for (final PluginDependency dependency in optionalDeps) {
        final Plugin? depPlugin = _registry.get(dependency.pluginId);
        if (depPlugin != null) {
          // 可选依赖存在时也需要检查版本兼容性
          if (!await _checkVersionCompatibility(depPlugin, dependency)) {
            print('可选依赖版本不兼容: ${dependency.pluginId}');
            // 可选依赖版本不兼容时只警告，不阻止加载
          }
        }
      }

      // 3. 版本兼容性验证
      for (final PluginDependency dependency in plugin.dependencies) {
        final Plugin? depPlugin = _registry.get(dependency.pluginId);
        if (depPlugin != null) {
          if (!await _checkVersionCompatibility(depPlugin, dependency)) {
            print('版本兼容性检查失败: ${dependency.pluginId}');
            if (!dependency.optional) {
              return false;
            }
          }
        }
      }

      // 4. 平台兼容性检查
      if (!await _checkPlatformCompatibility(plugin)) {
        print('平台兼容性检查失败: ${plugin.id}');
        return false;
      }

      return true;
    } catch (e) {
      print('依赖检查过程中发生错误: $e');
      return false;
    }
  }

  /// 获取插件的所有依赖
  ///
  /// [pluginId] 插件ID
  /// [recursive] 是否递归获取
  List<String> getPluginDependencies(
    String pluginId, {
    bool recursive = false,
  }) {
    try {
      // 1. 直接依赖获取
      final dependencies = _dependencyGraph[pluginId] ?? <String>{};

      if (!recursive) {
        // 2. 依赖排序（按字母顺序）
        final sortedDeps = dependencies.toList()..sort();
        return sortedDeps;
      }

      // 3. 递归依赖遍历
      final allDependencies = <String>{};
      final visited = <String>{};
      final dependencyOrder = <String>[];

      void collectDependencies(String currentPluginId, int depth) {
        if (visited.contains(currentPluginId)) {
          return;
        }

        visited.add(currentPluginId);
        final deps = _dependencyGraph[currentPluginId] ?? <String>{};

        // 按深度优先顺序收集依赖
        for (final dep in deps) {
          if (!allDependencies.contains(dep)) {
            allDependencies.add(dep);
            dependencyOrder.add(dep);

            // 递归收集子依赖
            collectDependencies(dep, depth + 1);
          }
        }
      }

      collectDependencies(pluginId, 0);

      // 4. 依赖去重和排序
      // 使用拓扑排序确保依赖顺序正确
      final sortedDependencies = _sortDependenciesByLoadOrder(dependencyOrder);

      return sortedDependencies;
    } catch (e) {
      print('获取插件依赖失败: $e');
      return <String>[];
    }
  }

  /// 获取依赖于指定插件的插件列表
  ///
  /// [pluginId] 插件ID
  /// [recursive] 是否递归获取所有间接依赖者
  List<String> getPluginDependents(
    String pluginId, {
    bool recursive = false,
  }) {
    try {
      final directDependents = _reverseDependencyGraph[pluginId] ?? <String>{};

      if (!recursive) {
        // 返回直接依赖者，按字母顺序排序
        final sortedDependents = directDependents.toList()..sort();
        return sortedDependents;
      }

      // 递归获取所有依赖者
      final allDependents = <String>{};
      final visited = <String>{};

      void collectDependents(String currentPluginId) {
        if (visited.contains(currentPluginId)) {
          return;
        }

        visited.add(currentPluginId);
        final dependents =
            _reverseDependencyGraph[currentPluginId] ?? <String>{};

        for (final dependent in dependents) {
          if (!allDependents.contains(dependent)) {
            allDependents.add(dependent);
            // 递归收集间接依赖者
            collectDependents(dependent);
          }
        }
      }

      collectDependents(pluginId);

      // 按字母顺序排序
      final sortedAllDependents = allDependents.toList()..sort();
      return sortedAllDependents;
    } catch (e) {
      print('获取插件依赖者失败: $e');
      return <String>[];
    }
  }

  /// 检查是否可以安全卸载插件
  ///
  /// [pluginId] 插件ID
  bool canUnloadPlugin(String pluginId) {
    // TODO(High): [Phase 2.9.1] 实现安全卸载检查
    // 需要实现：
    // 1. 检查是否有其他插件依赖
    // 2. 检查运行时依赖
    // 3. 检查用户数据依赖

    final List<String> dependents = getPluginDependents(pluginId);

    // 检查是否有活跃的依赖者
    for (final String dependent in dependents) {
      final PluginState? state = _registry.getState(dependent);
      if (state == PluginState.started || state == PluginState.initialized) {
        return false;
      }
    }

    return true;
  }

  /// 自动安装缺失的依赖
  ///
  /// [plugin] 插件
  /// [installationManager] 可选的安装管理器实例
  Future<List<String>> autoInstallDependencies(
    Plugin plugin, {
    dynamic installationManager,
  }) async {
    final installed = <String>[];
    final failed = <String>[];

    try {
      // 1. 检查必需依赖
      final requiredDeps =
          plugin.dependencies.where((dep) => !dep.optional).toList();

      // 2. 检查可选依赖
      final optionalDeps =
          plugin.dependencies.where((dep) => dep.optional).toList();

      // 3. 安装必需依赖
      for (final dependency in requiredDeps) {
        if (!_registry.contains(dependency.pluginId)) {
          final success = await _installSingleDependency(
            dependency,
            installationManager,
            required: true,
          );

          if (success) {
            installed.add(dependency.pluginId);
          } else {
            failed.add(dependency.pluginId);
          }
        }
      }

      // 4. 安装可选依赖（失败不影响整体）
      for (final dependency in optionalDeps) {
        if (!_registry.contains(dependency.pluginId)) {
          final success = await _installSingleDependency(
            dependency,
            installationManager,
            required: false,
          );

          if (success) {
            installed.add(dependency.pluginId);
          }
          // 可选依赖失败不记录到failed中
        }
      }

      // 5. 检查必需依赖是否全部安装成功
      if (failed.isNotEmpty) {
        print('必需依赖安装失败: ${failed.join(', ')}');
        // TODO: 实现安装失败回滚
        // await _rollbackInstallation(installed);
      }
    } catch (e) {
      print('自动安装依赖失败: $e');
    }

    return installed;
  }

  /// 安装单个依赖
  Future<bool> _installSingleDependency(
      PluginDependency dependency, dynamic installationManager,
      {required bool required}) async {
    try {
      print('正在安装依赖: ${dependency.pluginId}');

      // TODO: 集成真实的安装管理器
      // if (installationManager != null) {
      //   final result = await installationManager.installPlugin(
      //     dependency.pluginId,
      //     version: dependency.versionConstraint,
      //   );
      //   return result.isSuccess;
      // }

      // 当前使用模拟安装
      await Future<void>.delayed(const Duration(milliseconds: 500));

      // 模拟安装成功率（必需依赖90%，可选依赖70%）
      final successRate = required ? 0.9 : 0.7;
      final success = DateTime.now().millisecond / 1000 < successRate;

      if (success) {
        print('依赖安装成功: ${dependency.pluginId}');
      } else {
        print('依赖安装失败: ${dependency.pluginId}');
      }

      return success;
    } catch (e) {
      print('安装依赖时发生错误: ${dependency.pluginId} - $e');
      return false;
    }
  }

  /// 更新依赖图
  ///
  /// [plugin] 插件
  void updateDependencyGraph(Plugin plugin) {
    try {
      // 1. 获取旧的依赖关系用于清理
      final Set<String>? oldDependencies = _dependencyGraph[plugin.id];

      // 2. 获取新的依赖关系
      final Set<String> newDependencies = plugin.dependencies
          .map((PluginDependency dep) => dep.pluginId)
          .toSet();

      // 3. 清理旧的反向依赖关系
      if (oldDependencies != null) {
        for (final String oldDepId in oldDependencies) {
          _reverseDependencyGraph[oldDepId]?.remove(plugin.id);
          // 如果反向依赖集合为空，移除该条目
          if (_reverseDependencyGraph[oldDepId]?.isEmpty == true) {
            _reverseDependencyGraph.remove(oldDepId);
          }
        }
      }

      // 4. 更新依赖关系
      if (newDependencies.isNotEmpty) {
        _dependencyGraph[plugin.id] = newDependencies;
      } else {
        _dependencyGraph.remove(plugin.id);
      }

      // 5. 建立新的反向依赖关系
      for (final String depId in newDependencies) {
        _reverseDependencyGraph.putIfAbsent(depId, () => <String>{});
        _reverseDependencyGraph[depId]!.add(plugin.id);
      }

      // 6. 图结构优化 - 移除孤立节点
      _optimizeDependencyGraph();

      // 7. 缓存失效处理
      _invalidateCache(plugin.id);
    } catch (e) {
      print('更新依赖图失败: ${plugin.id} - $e');
    }
  }

  /// 优化依赖图结构
  void _optimizeDependencyGraph() {
    // 移除空的依赖集合
    _dependencyGraph.removeWhere((key, value) => value.isEmpty);

    // 移除空的反向依赖集合
    _reverseDependencyGraph.removeWhere((key, value) => value.isEmpty);
  }

  /// 缓存失效处理
  void _invalidateCache(String pluginId) {
    // 清理与该插件相关的缓存
    // 这里可以扩展为更复杂的缓存管理
    print('缓存失效: $pluginId');
  }

  /// 清理插件依赖信息
  ///
  /// [pluginId] 插件ID
  void cleanupPlugin(String pluginId) {
    try {
      // 1. 移除插件的依赖关系
      final dependencies = _dependencyGraph.remove(pluginId);

      // 2. 从反向依赖图中移除该插件的依赖
      if (dependencies != null) {
        for (final depId in dependencies) {
          _reverseDependencyGraph[depId]?.remove(pluginId);
          // 如果反向依赖集合为空，移除该条目
          if (_reverseDependencyGraph[depId]?.isEmpty == true) {
            _reverseDependencyGraph.remove(depId);
          }
        }
      }

      // 3. 移除其他插件对此插件的反向依赖
      _reverseDependencyGraph.remove(pluginId);

      // 4. 清理其他插件对此插件的依赖引用
      _dependencyGraph.forEach((key, deps) {
        deps.remove(pluginId);
      });

      // 5. 图结构优化
      _optimizeDependencyGraph();

      // 6. 缓存失效处理
      _invalidateCache(pluginId);
    } catch (e) {
      print('清理插件依赖信息失败: $pluginId - $e');
    }
  }

  /// 构建高级依赖图 (集成Creative Workshop算法)
  Map<String, DependencyNode> _buildDependencyGraphAdvanced(
    List<Plugin> plugins,
    Map<String, PluginInstallInfo> installedPlugins,
    Map<String, PluginInstallInfo> availablePlugins,
  ) {
    final Map<String, DependencyNode> graph = <String, DependencyNode>{};
    final Set<String> visited = <String>{};

    void buildNode(PluginInstallInfo plugin) {
      if (visited.contains(plugin.id)) return;
      visited.add(plugin.id);

      // 创建节点
      final DependencyNode node = DependencyNode(
        pluginId: plugin.id,
        version: plugin.version,
        dependencies: plugin.dependencies,
      );
      graph[plugin.id] = node;

      // 递归处理依赖
      for (final PluginDependency dep in plugin.dependencies) {
        final PluginInstallInfo? depPlugin =
            installedPlugins[dep.pluginId] ?? availablePlugins[dep.pluginId];
        if (depPlugin != null) {
          buildNode(depPlugin);
        }
      }
    }

    // 为所有插件构建节点
    for (final Plugin plugin in plugins) {
      final PluginInstallInfo pluginInfo = PluginInstallInfo.fromPlugin(plugin);
      buildNode(pluginInfo);
    }

    return graph;
  }

  /// 查找缺失依赖 (集成Creative Workshop算法)
  List<PluginDependency> _findMissingDependencies(
    List<Plugin> plugins,
    Map<String, PluginInstallInfo> installedPlugins,
    Map<String, PluginInstallInfo> availablePlugins,
  ) {
    final List<PluginDependency> missing = <PluginDependency>[];
    final Set<String> checked = <String>{};

    void checkDependencies(List<PluginDependency> dependencies) {
      for (final PluginDependency dep in dependencies) {
        if (checked.contains(dep.pluginId)) continue;
        checked.add(dep.pluginId);

        // 检查是否在已安装或可用插件中
        final bool isInstalled = installedPlugins.containsKey(dep.pluginId);
        final bool isAvailable = availablePlugins.containsKey(dep.pluginId);

        if (!isInstalled && !isAvailable && !dep.optional) {
          missing.add(dep);
        } else if (isInstalled || isAvailable) {
          // 递归检查依赖的依赖
          final PluginInstallInfo? plugin =
              installedPlugins[dep.pluginId] ?? availablePlugins[dep.pluginId];
          if (plugin != null) {
            checkDependencies(plugin.dependencies);
          }
        }
      }
    }

    // 检查所有插件的依赖
    for (final Plugin plugin in plugins) {
      checkDependencies(plugin.dependencies);
    }

    return missing;
  }

  /// 查找版本冲突 (集成Creative Workshop算法)
  List<DependencyConflict> _findVersionConflicts(
    Map<String, DependencyNode> dependencyGraph,
    Map<String, PluginInstallInfo> installedPlugins,
  ) {
    final List<DependencyConflict> conflicts = <DependencyConflict>[];
    final Map<String, Set<String>> versionRequirements =
        <String, Set<String>>{};

    // 收集所有版本要求
    for (final DependencyNode node in dependencyGraph.values) {
      for (final PluginDependency dep in node.dependencies) {
        versionRequirements.putIfAbsent(dep.pluginId, () => <String>{});
        versionRequirements[dep.pluginId]!.add(dep.versionConstraint);
      }
    }

    // 检查版本冲突
    for (final MapEntry<String, Set<String>> entry
        in versionRequirements.entries) {
      final String pluginId = entry.key;
      final Set<String> requiredVersions = entry.value;
      final PluginInstallInfo? installedPlugin = installedPlugins[pluginId];

      if (installedPlugin != null) {
        // 检查每个版本要求是否与已安装版本兼容
        for (final String requiredVersion in requiredVersions) {
          if (!_isVersionCompatible(
            installedPlugin.version,
            requiredVersion,
          )) {
            conflicts.add(
              DependencyConflict(
                pluginId: 'unknown',
                dependencyId: pluginId,
                requiredVersion: requiredVersion,
                availableVersion: installedPlugin.version,
                conflictType: DependencyConflictType.versionIncompatible,
              ),
            );
          }
        }
      }
    }

    return conflicts;
  }

  /// 检查版本兼容性 (集成Creative Workshop算法)
  bool _isVersionCompatible(String availableVersion, String requiredVersion) {
    try {
      final Version available = Version.parse(availableVersion);
      final VersionConstraint constraint =
          VersionConstraint.parse(requiredVersion);
      return constraint.allows(available);
    } catch (e) {
      // 如果解析失败，使用简单字符串比较
      return availableVersion == requiredVersion;
    }
  }

  /// 查找循环依赖 (集成Creative Workshop算法)
  List<List<String>> _findCircularDependencies(
    Map<String, DependencyNode> dependencyGraph,
  ) {
    final List<List<String>> cycles = <List<String>>[];
    final Set<String> visited = <String>{};
    final Set<String> recursionStack = <String>{};
    final List<String> currentPath = <String>[];

    bool dfs(String pluginId) {
      if (recursionStack.contains(pluginId)) {
        // 找到循环，提取循环路径
        final int cycleStart = currentPath.indexOf(pluginId);
        if (cycleStart >= 0) {
          final List<String> cycle = currentPath.sublist(cycleStart)
            ..add(pluginId);
          cycles.add(cycle);
        }
        return true;
      }

      if (visited.contains(pluginId)) {
        return false;
      }

      visited.add(pluginId);
      recursionStack.add(pluginId);
      currentPath.add(pluginId);

      final DependencyNode? node = dependencyGraph[pluginId];
      if (node != null) {
        for (final PluginDependency dep in node.dependencies) {
          if (dfs(dep.pluginId)) {
            // 继续搜索其他可能的循环
          }
        }
      }

      recursionStack.remove(pluginId);
      currentPath.removeLast();
      return false;
    }

    // 对所有节点进行DFS
    for (final String pluginId in dependencyGraph.keys) {
      if (!visited.contains(pluginId)) {
        dfs(pluginId);
      }
    }

    return cycles;
  }

  /// 拓扑排序 (集成Creative Workshop算法)
  List<String> _topologicalSort(Map<String, DependencyNode> dependencyGraph) {
    final List<String> result = <String>[];
    final Map<String, int> inDegree = <String, int>{};
    final Queue<String> queue = Queue<String>();

    // 计算入度
    for (final String pluginId in dependencyGraph.keys) {
      inDegree[pluginId] = 0;
    }

    for (final DependencyNode node in dependencyGraph.values) {
      for (final PluginDependency dep in node.dependencies) {
        if (dependencyGraph.containsKey(dep.pluginId)) {
          // 修复：当node依赖dep时，应该是node的入度增加，而不是dep的入度增加
          inDegree[node.pluginId] = (inDegree[node.pluginId] ?? 0) + 1;
        }
      }
    }

    // 将入度为0的节点加入队列
    for (final MapEntry<String, int> entry in inDegree.entries) {
      if (entry.value == 0) {
        queue.add(entry.key);
      }
    }

    // Kahn算法
    while (queue.isNotEmpty) {
      final String current = queue.removeFirst();
      result.add(current);

      // 修复：当处理current节点时，需要减少依赖于current的其他节点的入度
      for (final DependencyNode otherNode in dependencyGraph.values) {
        for (final PluginDependency dep in otherNode.dependencies) {
          if (dep.pluginId == current) {
            // otherNode依赖current，所以current被处理后，otherNode的入度减1
            inDegree[otherNode.pluginId] =
                (inDegree[otherNode.pluginId] ?? 1) - 1;
            if (inDegree[otherNode.pluginId] == 0) {
              queue.add(otherNode.pluginId);
            }
          }
        }
      }
    }

    return result;
  }

  /// 检查版本兼容性
  Future<bool> _checkVersionCompatibility(
    Plugin plugin,
    PluginDependency dependency,
  ) async {
    try {
      final Version pluginVersion = Version.parse(plugin.version);
      final VersionConstraint constraint =
          VersionConstraint.parse(dependency.versionConstraint);

      final bool isCompatible = constraint.allows(pluginVersion);

      if (!isCompatible) {
        print(
            '版本不兼容: ${plugin.id} v${plugin.version} 不满足约束 ${dependency.versionConstraint}');
      }

      return isCompatible;
    } catch (e) {
      print('版本兼容性检查失败: $e');
      // 如果解析失败，使用简单字符串比较
      return plugin.version == dependency.versionConstraint;
    }
  }

  /// 检查平台兼容性
  Future<bool> _checkPlatformCompatibility(Plugin plugin) async {
    try {
      // 获取当前平台信息
      final String currentPlatform = _getCurrentPlatform();

      // 检查插件是否支持当前平台
      final supportedPlatforms = plugin.supportedPlatforms
          .map((platform) => platform.toString().split('.').last)
          .toList();

      if (supportedPlatforms.isEmpty) {
        // 如果没有指定支持的平台，默认支持所有平台
        return true;
      }

      final isSupported = supportedPlatforms.contains(currentPlatform) ||
          supportedPlatforms.contains('all');

      if (!isSupported) {
        print('平台不兼容: ${plugin.id} 不支持平台 $currentPlatform');
        print('支持的平台: ${supportedPlatforms.join(', ')}');
      }

      return isSupported;
    } catch (e) {
      print('平台兼容性检查失败: $e');
      // 发生错误时默认兼容
      return true;
    }
  }

  /// 获取当前平台
  String _getCurrentPlatform() {
    try {
      // 使用条件导入来检测平台
      // 这是一个更准确的平台检测实现

      // 检查是否为Web平台
      if (identical(0, 0.0)) {
        // 在Web平台上，这个条件总是true
        // 但我们需要更准确的检测方法
        try {
          // 尝试检测Web环境的其他方式
          // 在纯Dart环境中，我们使用编译时常量
          return 'web';
        } catch (e) {
          // 如果检测失败，可能不是Web平台
        }
      }

      // 检查移动平台特征
      // 这里可以通过检查屏幕尺寸、触摸支持等来判断
      // 但为了简化，我们使用编译时常量

      // 默认假设为桌面平台
      return 'desktop';
    } catch (e) {
      print('平台检测失败: $e');
      return 'unknown';
    }
  }

  /// 按加载顺序排序依赖
  List<String> _sortDependenciesByLoadOrder(List<String> dependencies) {
    try {
      // 创建子图，只包含指定的依赖
      final subGraph = <String, Set<String>>{};
      for (final dep in dependencies) {
        final depDeps = _dependencyGraph[dep] ?? <String>{};
        // 只包含在dependencies列表中的依赖
        final filteredDeps =
            depDeps.where((d) => dependencies.contains(d)).toSet();
        subGraph[dep] = filteredDeps;
      }

      // 对子图进行拓扑排序
      final result = <String>[];
      final inDegree = <String, int>{};
      final queue = <String>[];

      // 计算入度
      for (final dep in dependencies) {
        inDegree[dep] = 0;
      }

      for (final entry in subGraph.entries) {
        for (final targetDep in entry.value) {
          inDegree[targetDep] = (inDegree[targetDep] ?? 0) + 1;
        }
      }

      // 将入度为0的节点加入队列
      for (final entry in inDegree.entries) {
        if (entry.value == 0) {
          queue.add(entry.key);
        }
      }

      // Kahn算法
      while (queue.isNotEmpty) {
        final current = queue.removeAt(0);
        result.add(current);

        final currentDeps = subGraph[current] ?? <String>{};
        for (final dep in currentDeps) {
          inDegree[dep] = (inDegree[dep] ?? 1) - 1;
          if (inDegree[dep] == 0) {
            queue.add(dep);
          }
        }
      }

      return result;
    } catch (e) {
      print('依赖排序失败: $e');
      // 如果排序失败，返回原始顺序
      return dependencies;
    }
  }
}
