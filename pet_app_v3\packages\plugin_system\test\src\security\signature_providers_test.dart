/*
---------------------------------------------------------------
File name:          signature_providers_test.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        签名提供者测试 - 纯Dart测试
---------------------------------------------------------------
*/

import 'dart:typed_data';

import 'package:test/test.dart';
import 'package:plugin_system/src/security/signature/plugin_signature_core.dart';
import 'package:plugin_system/src/security/signature/rsa_signature_provider.dart';
import 'package:plugin_system/src/security/signature/ecdsa_signature_provider.dart';
import 'package:plugin_system/src/security/signature/ed25519_signature_provider.dart';

void main() {
  group('RSA签名提供者测试', () {
    late RSASignatureProvider provider;

    setUp(() {
      provider = RSASignatureProvider();
    });

    test('应该支持RSA算法', () {
      expect(provider.algorithm, equals(PluginSignatureAlgorithm.rsa2048));
    });

    test('应该生成和验证RSA签名', () async {
      final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
      
      // 生成签名
      final signature = await provider.generateSignature(testData, 'test_key');
      expect(signature.isNotEmpty, isTrue);
      
      // 验证签名
      final isValid = await provider.verifySignature(signature, testData, 'test_key');
      expect(isValid, isTrue);
    });

    test('应该生成密钥对', () async {
      await provider.generateKeyPair('test_key_pair');
      
      final publicKey = await provider.getPublicKey('test_key_pair');
      final privateKey = await provider.getPrivateKey('test_key_pair');
      
      expect(publicKey, isNotNull);
      expect(privateKey, isNotNull);
    });

    test('应该验证密钥对匹配', () async {
      final isMatch = await provider.verifyKeyPairMatch('test_match');
      expect(isMatch, isTrue);
    });

    test('应该获取密钥信息', () async {
      final keyInfo = await provider.getKeyInfo('test_info');
      expect(keyInfo['algorithm'], equals('RSA'));
      expect(keyInfo['keySize'], equals(2048));
    });
  });

  group('ECDSA签名提供者测试', () {
    late ECDSASignatureProvider provider;

    setUp(() {
      provider = ECDSASignatureProvider();
    });

    test('应该支持ECDSA算法', () {
      expect(provider.algorithm, equals(PluginSignatureAlgorithm.ecdsaP256));
    });

    test('应该生成和验证ECDSA签名', () async {
      final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
      
      // 生成签名
      final signature = await provider.generateSignature(testData, 'test_key');
      expect(signature.isNotEmpty, isTrue);
      
      // 验证签名
      final isValid = await provider.verifySignature(signature, testData, 'test_key');
      expect(isValid, isTrue);
    });

    test('应该生成密钥对', () async {
      await provider.generateKeyPair('test_key_pair');
      
      final publicKey = await provider.getPublicKey('test_key_pair');
      final privateKey = await provider.getPrivateKey('test_key_pair');
      
      expect(publicKey, isNotNull);
      expect(privateKey, isNotNull);
    });

    test('应该验证密钥对匹配', () async {
      final isMatch = await provider.verifyKeyPairMatch('test_match');
      expect(isMatch, isTrue);
    });

    test('应该获取密钥信息', () async {
      final keyInfo = await provider.getKeyInfo('test_info');
      expect(keyInfo['algorithm'], equals('ECDSA'));
      expect(keyInfo['curve'], equals('P-256'));
      expect(keyInfo['keySize'], equals(256));
    });
  });

  group('Ed25519签名提供者测试', () {
    late Ed25519SignatureProvider provider;

    setUp(() {
      provider = Ed25519SignatureProvider();
    });

    test('应该支持Ed25519算法', () {
      expect(provider.algorithm, equals(PluginSignatureAlgorithm.ed25519));
    });

    test('应该生成和验证Ed25519签名', () async {
      final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
      
      // 生成签名
      final signature = await provider.generateSignature(testData, 'test_key');
      expect(signature.isNotEmpty, isTrue);
      expect(signature.length, equals(64)); // Ed25519签名长度
      
      // 验证签名
      final isValid = await provider.verifySignature(signature, testData, 'test_key');
      expect(isValid, isTrue);
    });

    test('应该生成密钥对', () async {
      await provider.generateKeyPair('test_key_pair');
      
      final publicKey = await provider.getPublicKey('test_key_pair');
      final privateKey = await provider.getPrivateKey('test_key_pair');
      
      expect(publicKey, isNotNull);
      expect(privateKey, isNotNull);
      expect(publicKey!.length, equals(32)); // Ed25519公钥长度
      expect(privateKey!.length, equals(32)); // Ed25519私钥长度
    });

    test('应该验证密钥对匹配', () async {
      final isMatch = await provider.verifyKeyPairMatch('test_match');
      expect(isMatch, isTrue);
    });

    test('应该获取密钥信息', () async {
      final keyInfo = await provider.getKeyInfo('test_info');
      expect(keyInfo['algorithm'], equals('Ed25519'));
      expect(keyInfo['keySize'], equals(256));
    });

    test('应该导出和导入公钥', () async {
      await provider.generateKeyPair('test_export');
      
      final hexKey = await provider.exportPublicKeyHex('test_export');
      expect(hexKey.length, equals(64)); // 32字节 * 2 hex字符
      
      await provider.importPublicKeyHex('test_import', hexKey);
      final importedKey = await provider.getPublicKey('test_import');
      expect(importedKey, isNotNull);
    });

    test('应该检查私钥存在性', () async {
      await provider.generateKeyPair('test_private');
      final hasPrivate = await provider.hasPrivateKey('test_private');
      expect(hasPrivate, isTrue);
    });

    test('应该获取算法特性', () {
      final algorithmInfo = provider.getAlgorithmInfo();
      expect(algorithmInfo['name'], equals('Ed25519'));
      expect(algorithmInfo['type'], equals('EdDSA'));
      expect(algorithmInfo['keySize'], equals(256));
      expect(algorithmInfo['signatureSize'], equals(512));
    });
  });

  group('性能测试', () {
    test('RSA签名性能测试', () async {
      final provider = RSASignatureProvider();
      final testData = Uint8List.fromList(List.generate(1000, (i) => i % 256));
      
      final stopwatch = Stopwatch()..start();
      await provider.generateSignature(testData, 'perf_test');
      stopwatch.stop();
      
      expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5秒内完成
    });

    test('ECDSA签名性能测试', () async {
      final provider = ECDSASignatureProvider();
      final testData = Uint8List.fromList(List.generate(1000, (i) => i % 256));
      
      final stopwatch = Stopwatch()..start();
      await provider.generateSignature(testData, 'perf_test');
      stopwatch.stop();
      
      expect(stopwatch.elapsedMilliseconds, lessThan(3000)); // 3秒内完成
    });

    test('Ed25519签名性能测试', () async {
      final provider = Ed25519SignatureProvider();
      final testData = Uint8List.fromList(List.generate(1000, (i) => i % 256));
      
      final stopwatch = Stopwatch()..start();
      await provider.generateSignature(testData, 'perf_test');
      stopwatch.stop();
      
      expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // 1秒内完成
    });
  });

  group('错误处理测试', () {
    test('应该处理空数据', () async {
      final provider = RSASignatureProvider();
      final emptyData = Uint8List(0);
      
      expect(
        () => provider.generateSignature(emptyData, 'empty_test'),
        throwsA(isA<Exception>()),
      );
    });

    test('应该处理无效签名', () async {
      final provider = ECDSASignatureProvider();
      final testData = Uint8List.fromList([1, 2, 3, 4, 5]);
      final invalidSignature = Uint8List.fromList([9, 8, 7, 6, 5]);
      
      final isValid = await provider.verifySignature(invalidSignature, testData, 'invalid_test');
      expect(isValid, isFalse);
    });
  });
}
