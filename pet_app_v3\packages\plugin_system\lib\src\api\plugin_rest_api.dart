/*
---------------------------------------------------------------
File name:          plugin_rest_api.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件REST API实现 - 需要重构拆分
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 4.3 - REST API实现;
    2025-07-27: 标记为需要重构 - 文件过大(4317行)，TODO过多(51个)
---------------------------------------------------------------
注意：此文件需要重构拆分为多个模块：
1. HTTP服务器管理 -> plugin_http_server.dart
2. API路由处理 -> plugin_api_router.dart
3. 插件安装管理 -> plugin_installation_manager.dart
4. 插件配置管理 -> plugin_config_manager.dart
5. 插件更新管理 -> plugin_update_manager.dart
6. 性能监控接口 -> plugin_monitoring_api.dart
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:plugin_system/src/core/plugin.dart';

import 'package:plugin_system/src/core/plugin_registry.dart';
import 'package:plugin_system/src/core/plugin_batch_manager.dart';
import 'package:plugin_system/src/monitoring/plugin_monitoring_system.dart';
import 'package:plugin_system/src/optimization/plugin_performance_optimizer.dart';
import 'package:plugin_system/src/api/plugin_api_interface.dart';

/// REST API配置
class RestApiConfig {
  const RestApiConfig({
    this.host = 'localhost',
    this.port = 8080,
    this.basePath = '/api',
    this.enableCors = true,
    this.enableAuth = false,
    this.enableRateLimit = true,
    this.rateLimitRequests = 100,
    this.rateLimitWindow = const Duration(minutes: 1),
    this.enableLogging = true,
    this.enableMetrics = true,
  });

  /// 主机地址
  final String host;

  /// 端口
  final int port;

  /// 基础路径
  final String basePath;

  /// 启用CORS
  final bool enableCors;

  /// 启用认证
  final bool enableAuth;

  /// 启用限流
  final bool enableRateLimit;

  /// 限流请求数
  final int rateLimitRequests;

  /// 限流时间窗口
  final Duration rateLimitWindow;

  /// 启用日志
  final bool enableLogging;

  /// 启用指标
  final bool enableMetrics;
}

/// 插件REST API实现
class PluginRestApi implements IPluginRestApi {
  PluginRestApi({
    required this.registry,
    required this.batchManager,
    required this.monitoringSystem,
    required this.performanceOptimizer,
    RestApiConfig? config,
  }) : _config = config ?? const RestApiConfig();

  /// 插件注册表
  final PluginRegistry registry;

  /// 批量管理器
  final PluginBatchManager batchManager;

  /// 监控系统
  final PluginMonitoringSystem monitoringSystem;

  /// 性能优化器
  final PluginPerformanceOptimizer performanceOptimizer;

  /// 配置
  final RestApiConfig _config;

  /// 端点注册表
  final Map<String, ApiEndpoint> _endpoints = <String, ApiEndpoint>{};

  /// 中间件列表
  final List<ApiMiddleware> _middleware = <ApiMiddleware>[];

  /// 是否已启动
  bool _started = false;

  /// HTTP服务器实例
  HttpServer? _httpServer;

  /// 是否已启动
  bool get isStarted => _started;

  @override
  ApiVersion get version => const ApiVersion(major: 1, minor: 0, patch: 0);

  @override
  List<ApiVersion> get supportedVersions => <ApiVersion>[
        const ApiVersion(major: 1, minor: 0, patch: 0),
      ];

  @override
  void registerEndpoint(ApiEndpoint endpoint) {
    final key = '${endpoint.method.name}:${endpoint.path}';
    _endpoints[key] = endpoint;
  }

  @override
  void unregisterEndpoint(HttpMethod method, String path) {
    final key = '${method.name}:$path';
    _endpoints.remove(key);
  }

  @override
  void addMiddleware(ApiMiddleware middleware) {
    _middleware.add(middleware);
  }

  @override
  void removeMiddleware(String name) {
    _middleware.removeWhere((ApiMiddleware m) => m.name == name);
  }

  @override
  Future<ApiResponse<dynamic>> handleRequest(ApiRequest request) async {
    try {
      // 处理中间件
      ApiRequest? processedRequest = request;
      for (final middleware in _middleware) {
        processedRequest = await middleware.processRequest(processedRequest!);
        if (processedRequest == null) {
          return ApiResponse.error<dynamic>(
            statusCode: 400,
            message: 'Request blocked by middleware: ${middleware.name}',
          );
        }
      }

      // 查找端点
      final key = '${request.method.name}:${request.path}';
      final endpoint = _endpoints[key];
      if (endpoint == null) {
        return ApiResponse.error<dynamic>(
          statusCode: 404,
          message: 'Endpoint not found: ${request.method.name} ${request.path}',
        );
      }

      // 验证版本
      if (!validateVersion(request.version)) {
        return ApiResponse.error<dynamic>(
          statusCode: 400,
          message: 'Unsupported API version: ${request.version}',
        );
      }

      // 执行处理函数
      var response = await endpoint.handler(processedRequest!);

      // 处理响应中间件
      for (final middleware in _middleware.reversed) {
        response = await middleware.processResponse(processedRequest, response);
      }

      return response;
    } catch (e, stackTrace) {
      // 处理错误中间件
      var errorResponse = ApiResponse.error<dynamic>(
        statusCode: 500,
        message: 'Internal server error',
        errors: <String>[e.toString()],
      );

      for (final middleware in _middleware.reversed) {
        errorResponse = await middleware.processError(request, e, stackTrace);
      }

      return errorResponse;
    }
  }

  @override
  Map<String, dynamic> getApiDocumentation() {
    final endpoints = <Map<String, dynamic>>[];

    for (final endpoint in _endpoints.values) {
      endpoints.add(<String, dynamic>{
        'method': endpoint.method.name,
        'path': endpoint.path,
        'description': endpoint.description,
        'parameters': endpoint.parameters
            .map(
              (ApiParameter p) => <String, Object?>{
                'name': p.name,
                'type': p.type.toString(),
                'location': p.location.name,
                'required': p.required,
                'description': p.description,
              },
            )
            .toList(),
        'responses': endpoint.responses,
        'deprecated': endpoint.deprecated,
      });
    }

    return <String, dynamic>{
      'version': version.version,
      'supportedVersions':
          supportedVersions.map((ApiVersion v) => v.version).toList(),
      'basePath': _config.basePath,
      'endpoints': endpoints,
    };
  }

  @override
  List<ApiEndpoint> getEndpoints() => _endpoints.values.toList();

  @override
  bool validateVersion(ApiVersion requestVersion) => supportedVersions
      .any((ApiVersion v) => v.isCompatibleWith(requestVersion));

  @override
  Future<void> start() async {
    if (_started) return;

    try {
      // 注册默认端点
      _registerDefaultEndpoints();

      // 启动HTTP服务器
      _httpServer = await HttpServer.bind(_config.host, _config.port);

      // 监听请求
      _httpServer!.listen(_handleHttpRequest);

      print(
          'Plugin REST API server started on ${_config.host}:${_config.port}');
      _started = true;
    } catch (e) {
      print('Failed to start Plugin REST API server: $e');
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    if (!_started) return;

    try {
      // 停止HTTP服务器
      await _httpServer?.close();
      _httpServer = null;

      print('Plugin REST API server stopped');
      _started = false;
    } catch (e) {
      print('Error stopping Plugin REST API server: $e');
      rethrow;
    }
  }

  /// 处理HTTP请求
  Future<void> _handleHttpRequest(HttpRequest httpRequest) async {
    try {
      // 设置CORS头
      if (_config.enableCors) {
        httpRequest.response.headers.set('Access-Control-Allow-Origin', '*');
        httpRequest.response.headers.set(
            'Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        httpRequest.response.headers
            .set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      }

      // 处理OPTIONS请求
      if (httpRequest.method == 'OPTIONS') {
        httpRequest.response.statusCode = 200;
        await httpRequest.response.close();
        return;
      }

      // 解析请求
      final body = await utf8.decoder.bind(httpRequest).join();
      final Map<String, dynamic> bodyData = body.isNotEmpty
          ? jsonDecode(body) as Map<String, dynamic>
          : <String, dynamic>{};

      final ApiRequest apiRequest = ApiRequest(
        method: _parseHttpMethod(httpRequest.method),
        path: httpRequest.uri.path,
        version: const ApiVersion(major: 1, minor: 0, patch: 0),
        headers: _parseHeaders(httpRequest.headers),
        queryParameters: httpRequest.uri.queryParameters,
        body: bodyData,
      );

      // 处理请求
      final ApiResponse<dynamic> response = await handleRequest(apiRequest);

      // 发送响应
      httpRequest.response.statusCode = response.statusCode;
      httpRequest.response.headers.contentType = ContentType.json;

      final Map<String, dynamic> responseData = <String, dynamic>{
        'status': response.status.name,
        'data': response.data,
        'message': response.message,
        'errors': response.errors,
        'metadata': response.metadata,
      };

      httpRequest.response.write(jsonEncode(responseData));
      await httpRequest.response.close();
    } catch (e) {
      // 错误处理
      httpRequest.response.statusCode = 500;
      httpRequest.response.headers.contentType = ContentType.json;
      httpRequest.response.write(jsonEncode(<String, dynamic>{
        'status': 'error',
        'message': 'Internal server error',
        'errors': <String>[e.toString()],
      }));
      await httpRequest.response.close();
    }
  }

  /// 解析HTTP方法
  HttpMethod _parseHttpMethod(String method) {
    switch (method.toUpperCase()) {
      case 'GET':
        return HttpMethod.get;
      case 'POST':
        return HttpMethod.post;
      case 'PUT':
        return HttpMethod.put;
      case 'DELETE':
        return HttpMethod.delete;
      default:
        return HttpMethod.get;
    }
  }

  /// 解析请求头
  Map<String, String> _parseHeaders(HttpHeaders headers) {
    final Map<String, String> result = <String, String>{};
    headers.forEach((String name, List<String> values) {
      result[name] = values.join(', ');
    });
    return result;
  }

  /// 注册默认端点
  void _registerDefaultEndpoints() {
    // 插件管理端点
    registerEndpoint(
      ApiEndpoint(
        method: HttpMethod.get,
        path: '/plugins',
        handler: _handleGetPlugins,
        description: '获取插件列表',
      ),
    );

    registerEndpoint(
      ApiEndpoint(
        method: HttpMethod.get,
        path: '/plugins/{id}',
        handler: _handleGetPlugin,
        description: '获取插件详情',
      ),
    );

    registerEndpoint(
      ApiEndpoint(
        method: HttpMethod.post,
        path: '/plugins/{id}/install',
        handler: _handleInstallPlugin,
        description: '安装插件',
      ),
    );

    registerEndpoint(
      ApiEndpoint(
        method: HttpMethod.delete,
        path: '/plugins/{id}',
        handler: _handleUninstallPlugin,
        description: '卸载插件',
      ),
    );

    // 批量操作端点
    registerEndpoint(
      ApiEndpoint(
        method: HttpMethod.post,
        path: '/plugins/batch/install',
        handler: _handleBatchInstall,
        description: '批量安装插件',
      ),
    );

    // 系统管理端点
    registerEndpoint(
      ApiEndpoint(
        method: HttpMethod.get,
        path: '/system/status',
        handler: _handleGetSystemStatus,
        description: '获取系统状态',
      ),
    );

    registerEndpoint(
      ApiEndpoint(
        method: HttpMethod.get,
        path: '/system/metrics',
        handler: _handleGetMetrics,
        description: '获取性能指标',
      ),
    );
  }

  // 端点处理函数实现

  Future<ApiResponse<dynamic>> _handleGetPlugins(ApiRequest request) async {
    try {
      final plugins = registry.getAllPlugins();
      final pluginList = plugins
          .map(
            (Plugin plugin) => <String, dynamic>{
              'id': plugin.id,
              'name': plugin.manifest.name,
              'version': plugin.manifest.version,
              'description': plugin.manifest.description,
              'author': plugin.manifest.author,
              'tags': plugin.manifest.tags,
              'enabled': plugin.isEnabled,
            },
          )
          .toList();

      return ApiResponse.success(data: pluginList);
    } catch (e) {
      return ApiResponse.error(
        statusCode: 500,
        message: 'Failed to get plugins',
        errors: <String>[e.toString()],
      );
    }
  }

  Future<ApiResponse<dynamic>> _handleGetPlugin(ApiRequest request) async {
    try {
      final pluginId = _extractPathParameter(request.path, 'id');
      if (pluginId == null) {
        return ApiResponse.error(
          statusCode: 400,
          message: 'Plugin ID is required',
        );
      }

      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return ApiResponse.error(
          statusCode: 404,
          message: 'Plugin not found',
        );
      }

      return ApiResponse.success(
        data: <String, dynamic>{
          'id': plugin.id,
          'manifest': plugin.manifest.toJson(),
          'enabled': plugin.isEnabled,
          'loadTime': plugin.loadTime?.inMilliseconds,
        },
      );
    } catch (e) {
      return ApiResponse.error(
        statusCode: 500,
        message: 'Failed to get plugin',
        errors: <String>[e.toString()],
      );
    }
  }

  Future<ApiResponse<dynamic>> _handleInstallPlugin(ApiRequest request) async {
    try {
      final pluginId = _extractPathParameter(request.path, 'id');
      if (pluginId == null) {
        return ApiResponse.error(
          statusCode: 400,
          message: 'Plugin ID is required',
        );
      }

      // 这里应该实现实际的安装逻辑
      // 暂时返回成功响应
      return ApiResponse.success(
        data: <String, String>{
          'pluginId': pluginId,
          'status': 'installed',
          'message': 'Plugin installed successfully',
        },
      );
    } catch (e) {
      return ApiResponse.error(
        statusCode: 500,
        message: 'Failed to install plugin',
        errors: <String>[e.toString()],
      );
    }
  }

  Future<ApiResponse<dynamic>> _handleUninstallPlugin(
      ApiRequest request) async {
    try {
      final pluginId = _extractPathParameter(request.path, 'id');
      if (pluginId == null) {
        return ApiResponse.error(
          statusCode: 400,
          message: 'Plugin ID is required',
        );
      }

      // 这里应该实现实际的卸载逻辑
      return ApiResponse.success(
        data: <String, String>{
          'pluginId': pluginId,
          'status': 'uninstalled',
          'message': 'Plugin uninstalled successfully',
        },
      );
    } catch (e) {
      return ApiResponse.error(
        statusCode: 500,
        message: 'Failed to uninstall plugin',
        errors: <String>[e.toString()],
      );
    }
  }

  Future<ApiResponse<dynamic>> _handleBatchInstall(ApiRequest request) async {
    try {
      final body = request.body as Map<String, dynamic>?;
      final pluginIds = body?['pluginIds'] as List<String>?;

      if (pluginIds == null || pluginIds.isEmpty) {
        return ApiResponse.error(
          statusCode: 400,
          message: 'Plugin IDs are required',
        );
      }

      // 执行批量安装
      final result = await batchManager.executeBatchOperation(
        operation: BatchOperationType.install,
        pluginIds: pluginIds,
      );

      return ApiResponse.success(
        data: <String, Object>{
          'totalCount': result.totalCount,
          'successCount': result.successCount,
          'failureCount': result.failureCount,
          'duration': result.duration.inMilliseconds,
          'results': result.results
              .map(
                (PluginOperationResult r) => <String, Object?>{
                  'pluginId': r.pluginId,
                  'success': r.success,
                  'message': r.message,
                  'error': r.error,
                },
              )
              .toList(),
        },
      );
    } catch (e) {
      return ApiResponse.error(
        statusCode: 500,
        message: 'Failed to batch install plugins',
        errors: <String>[e.toString()],
      );
    }
  }

  Future<ApiResponse<dynamic>> _handleGetSystemStatus(
      ApiRequest request) async {
    try {
      final healthReport = await monitoringSystem.getHealthReport();

      return ApiResponse.success(
        data: <String, Object?>{
          'status': healthReport.status.name,
          'timestamp': healthReport.timestamp.toIso8601String(),
          'checks': healthReport.checks,
          'message': healthReport.message,
          'recommendations': healthReport.recommendations,
        },
      );
    } catch (e) {
      return ApiResponse.error(
        statusCode: 500,
        message: 'Failed to get system status',
        errors: <String>[e.toString()],
      );
    }
  }

  Future<ApiResponse<dynamic>> _handleGetMetrics(ApiRequest request) async {
    try {
      final metrics = await monitoringSystem.exportMetrics();

      return ApiResponse.success(data: metrics);
    } catch (e) {
      return ApiResponse.error(
        statusCode: 500,
        message: 'Failed to get metrics',
        errors: <String>[e.toString()],
      );
    }
  }

  /// 提取路径参数
  String? _extractPathParameter(String path, String paramName) {
    try {
      // 实现真实的路径参数提取逻辑
      // 支持 RESTful 路径模式，如 /api/plugins/{id}

      final pathSegments = path.split('/').where((s) => s.isNotEmpty).toList();

      // 定义路由模式映射
      final routePatterns = <String, List<String>>{
        'plugins': ['api', 'plugins', '{id}'],
        'plugin-config': ['api', 'plugins', '{id}', 'config'],
        'plugin-enable': ['api', 'plugins', '{id}', 'enable'],
        'plugin-disable': ['api', 'plugins', '{id}', 'disable'],
        'plugin-update': ['api', 'plugins', '{id}', 'update'],
      };

      // 尝试匹配路由模式
      for (final pattern in routePatterns.values) {
        if (_matchesPattern(pathSegments, pattern)) {
          final paramIndex = pattern.indexOf('{$paramName}');
          if (paramIndex >= 0 && paramIndex < pathSegments.length) {
            final value = pathSegments[paramIndex];
            // 验证参数值
            if (value.isNotEmpty && !value.startsWith('{')) {
              return Uri.decodeComponent(value);
            }
          }
        }
      }

      // 回退到简单的路径解析
      final regex = RegExp(r'/([^/]+)');
      final matches = regex.allMatches(path);
      if (matches.isNotEmpty) {
        // 假设最后一个路径段是ID
        final lastMatch = matches.last;
        final value = lastMatch.group(1);
        if (value != null && value.isNotEmpty) {
          return Uri.decodeComponent(value);
        }
      }

      return null;
    } on Exception catch (e) {
      print('路径参数提取失败: $e');
      return null;
    }
  }

  /// 检查路径是否匹配模式
  bool _matchesPattern(List<String> pathSegments, List<String> pattern) {
    if (pathSegments.length != pattern.length) {
      return false;
    }

    for (int i = 0; i < pattern.length; i++) {
      final patternSegment = pattern[i];
      final pathSegment = pathSegments[i];

      // 如果是参数占位符，跳过检查
      if (patternSegment.startsWith('{') && patternSegment.endsWith('}')) {
        continue;
      }

      // 精确匹配
      if (patternSegment != pathSegment) {
        return false;
      }
    }

    return true;
  }

  // TODO(Critical): [Phase 3.1] IPluginRestApi接口的其他方法实现（简化版本）
  // 需要实现完整的业务逻辑，当前为简化版本

  @override
  Future<ApiResponse<List<Map<String, dynamic>>>> getPlugins({
    int? page,
    int? limit,
    String? search,
    List<String>? tags,
  }) async {
    final request = ApiRequest(
      method: HttpMethod.get,
      path: '/plugins',
      version: version,
      queryParameters: <String, dynamic>{
        if (page != null) 'page': page,
        if (limit != null) 'limit': limit,
        if (search != null) 'search': search,
        if (tags != null) 'tags': tags,
      },
    );

    final response = await _handleGetPlugins(request);
    return ApiResponse<List<Map<String, dynamic>>>(
      status: response.status,
      statusCode: response.statusCode,
      data: response.data as List<Map<String, dynamic>>?,
      message: response.message,
      errors: response.errors,
      metadata: response.metadata,
      headers: response.headers,
    );
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> getPlugin(String pluginId) async {
    final request = ApiRequest(
      method: HttpMethod.get,
      path: '/plugins/$pluginId',
      version: version,
    );

    final response = await _handleGetPlugin(request);
    return ApiResponse<Map<String, dynamic>>(
      status: response.status,
      statusCode: response.statusCode,
      data: response.data as Map<String, dynamic>?,
      message: response.message,
      errors: response.errors,
      metadata: response.metadata,
      headers: response.headers,
    );
  }

  // 其他接口方法的简化实现...
  @override
  Future<ApiResponse<Map<String, dynamic>>> installPlugin(
    String pluginId, {
    String? version,
    Map<String, dynamic>? config,
  }) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否已安装
      if (registry.contains(pluginId)) {
        return ApiResponse.error(
          message: '插件已安装: $pluginId',
          statusCode: 409,
        );
      }

      // 3. 验证版本格式
      if (version != null && !_isValidVersion(version)) {
        return ApiResponse.error(
          message: '无效的版本格式: $version',
          statusCode: 400,
        );
      }

      // 4. 执行插件安装
      final installResult = await _performPluginInstallation(
        pluginId,
        version,
        config,
      );

      if (installResult['success'] == true) {
        return ApiResponse.success(
          data: <String, dynamic>{
            'status': 'installed',
            'pluginId': pluginId,
            'version': installResult['version'],
            'installedAt': DateTime.now().toIso8601String(),
            'config': config ?? <String, dynamic>{},
          },
        );
      } else {
        return ApiResponse.error(
          message: (installResult['error'] as String?) ?? '安装失败',
          statusCode: 500,
        );
      }
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '插件安装过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> uninstallPlugin(
    String pluginId,
  ) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否存在
      if (!registry.contains(pluginId)) {
        return ApiResponse.error(
          message: '插件不存在: $pluginId',
          statusCode: 404,
        );
      }

      // 3. 检查依赖关系
      final Map<String, dynamic> dependencyCheck =
          await _checkUninstallDependencies(pluginId);
      if (!(dependencyCheck['canUninstall'] as bool)) {
        return ApiResponse.error(
          message: dependencyCheck['reason'] as String,
          statusCode: 409,
        );
      }

      // 4. 执行插件卸载
      final Map<String, dynamic> uninstallResult =
          await _performPluginUninstallation(pluginId);

      if (uninstallResult['success'] as bool) {
        return ApiResponse.success(
          data: <String, dynamic>{
            'status': 'uninstalled',
            'pluginId': pluginId,
            'uninstalledAt': DateTime.now().toIso8601String(),
            'cleanupPerformed': uninstallResult['cleanupPerformed'],
          },
        );
      } else {
        return ApiResponse.error(
          message: (uninstallResult['error'] as String?) ?? '卸载失败',
          statusCode: 500,
        );
      }
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '插件卸载过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> updatePlugin(
    String pluginId, {
    String? version,
  }) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否存在
      if (!registry.contains(pluginId)) {
        return ApiResponse.error(
          message: '插件不存在: $pluginId',
          statusCode: 404,
        );
      }

      // 3. 验证版本格式
      if (version != null && !_isValidVersion(version)) {
        return ApiResponse.error(
          message: '无效的版本格式: $version',
          statusCode: 400,
        );
      }

      // 4. 检查是否有可用更新
      final Map<String, dynamic> updateCheck =
          await _checkPluginUpdate(pluginId, version);
      if (!(updateCheck['hasUpdate'] as bool)) {
        return ApiResponse.error(
          message: updateCheck['reason'] as String,
          statusCode: 409,
        );
      }

      // 5. 执行插件更新
      final Map<String, dynamic> updateResult =
          await _performPluginUpdate(pluginId, version);

      if (updateResult['success'] as bool) {
        return ApiResponse.success(
          data: <String, dynamic>{
            'status': 'updated',
            'pluginId': pluginId,
            'oldVersion': updateResult['oldVersion'],
            'newVersion': updateResult['newVersion'],
            'updatedAt': DateTime.now().toIso8601String(),
            'updateSize': updateResult['updateSize'],
          },
        );
      } else {
        return ApiResponse.error(
          message: (updateResult['error'] as String?) ?? '更新失败',
          statusCode: 500,
        );
      }
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '插件更新过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> enablePlugin(
    String pluginId,
  ) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否存在
      if (!registry.contains(pluginId)) {
        return ApiResponse.error(
          message: '插件不存在: $pluginId',
          statusCode: 404,
        );
      }

      // 3. 检查插件当前状态
      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return ApiResponse.error(
          message: '无法获取插件信息: $pluginId',
          statusCode: 500,
        );
      }

      // 4. 检查是否已启用
      if (plugin.currentState == PluginState.started) {
        return ApiResponse.error(
          message: '插件已启用: $pluginId',
          statusCode: 409,
        );
      }

      // 5. 检查依赖关系
      final dependencyCheck = await _checkEnableDependencies(pluginId);
      if (!(dependencyCheck['canEnable'] as bool)) {
        return ApiResponse.error(
          message: dependencyCheck['reason'] as String,
          statusCode: 409,
        );
      }

      // 6. 执行插件启用
      final enableResult = await _performPluginEnable(pluginId);

      if (enableResult['success'] as bool) {
        return ApiResponse.success(
          data: <String, dynamic>{
            'status': 'enabled',
            'pluginId': pluginId,
            'enabledAt': DateTime.now().toIso8601String(),
            'startupTime': enableResult['startupTime'],
          },
        );
      } else {
        return ApiResponse.error(
          message: (enableResult['error'] as String?) ?? '启用失败',
          statusCode: 500,
        );
      }
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '插件启用过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> disablePlugin(
    String pluginId,
  ) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否存在
      if (!registry.contains(pluginId)) {
        return ApiResponse.error(
          message: '插件不存在: $pluginId',
          statusCode: 404,
        );
      }

      // 3. 检查插件当前状态
      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return ApiResponse.error(
          message: '无法获取插件信息: $pluginId',
          statusCode: 500,
        );
      }

      // 4. 检查是否已禁用
      if (plugin.currentState != PluginState.started) {
        return ApiResponse.error(
          message: '插件未启用: $pluginId',
          statusCode: 409,
        );
      }

      // 5. 检查依赖关系（是否有其他插件依赖此插件）
      final dependencyCheck = await _checkDisableDependencies(pluginId);
      if (!(dependencyCheck['canDisable'] as bool)) {
        return ApiResponse.error(
          message: dependencyCheck['reason'] as String,
          statusCode: 409,
        );
      }

      // 6. 执行插件禁用
      final disableResult = await _performPluginDisable(pluginId);

      if (disableResult['success'] as bool) {
        return ApiResponse.success(
          data: <String, dynamic>{
            'status': 'disabled',
            'pluginId': pluginId,
            'disabledAt': DateTime.now().toIso8601String(),
            'shutdownTime': disableResult['shutdownTime'],
            'resourcesFreed': disableResult['resourcesFreed'],
          },
        );
      } else {
        return ApiResponse.error(
          message: (disableResult['error'] as String?) ?? '禁用失败',
          statusCode: 500,
        );
      }
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '插件禁用过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> getPluginConfig(
    String pluginId,
  ) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否存在
      if (!registry.contains(pluginId)) {
        return ApiResponse.error(
          message: '插件不存在: $pluginId',
          statusCode: 404,
        );
      }

      // 3. 获取插件信息
      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return ApiResponse.error(
          message: '无法获取插件信息: $pluginId',
          statusCode: 500,
        );
      }

      // 4. 获取插件配置
      final configResult = await _getPluginConfiguration(pluginId);

      if (configResult['success'] as bool) {
        return ApiResponse.success(
          data: <String, dynamic>{
            'pluginId': pluginId,
            'config': configResult['config'],
            'schema': configResult['schema'],
            'lastModified': configResult['lastModified'],
            'version': configResult['version'],
          },
        );
      } else {
        return ApiResponse.error(
          message: (configResult['error'] as String?) ?? '获取配置失败',
          statusCode: 500,
        );
      }
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '获取插件配置过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> updatePluginConfig(
    String pluginId,
    Map<String, dynamic> config,
  ) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 验证配置数据
      if (config.isEmpty) {
        return ApiResponse.error(
          message: '配置数据不能为空',
          statusCode: 400,
        );
      }

      // 3. 检查插件是否存在
      if (!registry.contains(pluginId)) {
        return ApiResponse.error(
          message: '插件不存在: $pluginId',
          statusCode: 404,
        );
      }

      // 4. 验证配置格式
      final validationResult = await _validatePluginConfig(pluginId, config);
      if (!(validationResult['valid'] as bool)) {
        return ApiResponse.error(
          message: validationResult['error'] as String,
          statusCode: 400,
        );
      }

      // 5. 执行配置更新
      final updateResult = await _updatePluginConfiguration(pluginId, config);

      if (updateResult['success'] as bool) {
        return ApiResponse.success(
          data: <String, dynamic>{
            'status': 'updated',
            'pluginId': pluginId,
            'updatedAt': DateTime.now().toIso8601String(),
            'changedFields': updateResult['changedFields'],
            'requiresRestart': updateResult['requiresRestart'],
          },
        );
      } else {
        return ApiResponse.error(
          message: (updateResult['error'] as String?) ?? '配置更新失败',
          statusCode: 500,
        );
      }
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '更新插件配置过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> batchInstallPlugins(
    List<String> pluginIds,
  ) async {
    try {
      // 1. 验证输入
      if (pluginIds.isEmpty) {
        return ApiResponse.error(
          message: '插件ID列表不能为空',
          statusCode: 400,
        );
      }

      // 2. 去重和验证
      final uniquePluginIds = pluginIds.toSet().toList();
      final invalidIds = <String>[];
      final alreadyInstalled = <String>[];

      for (final pluginId in uniquePluginIds) {
        if (pluginId.isEmpty) {
          invalidIds.add(pluginId);
        } else if (registry.contains(pluginId)) {
          alreadyInstalled.add(pluginId);
        }
      }

      // 3. 执行批量安装
      final Map<String, dynamic> batchResult =
          await _performBatchInstallation(uniquePluginIds);

      return ApiResponse.success(
        data: <String, dynamic>{
          'status': 'completed',
          'totalRequested': pluginIds.length,
          'totalProcessed': uniquePluginIds.length,
          'successful': batchResult['successful'],
          'failed': batchResult['failed'],
          'skipped': batchResult['skipped'],
          'invalidIds': invalidIds,
          'alreadyInstalled': alreadyInstalled,
          'completedAt': DateTime.now().toIso8601String(),
          'duration': batchResult['duration'],
        },
      );
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '批量安装过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> batchUninstallPlugins(
    List<String> pluginIds,
  ) async {
    try {
      // 1. 验证输入
      if (pluginIds.isEmpty) {
        return ApiResponse.error(
          message: '插件ID列表不能为空',
          statusCode: 400,
        );
      }

      // 2. 去重和验证
      final uniquePluginIds = pluginIds.toSet().toList();
      final notInstalled = <String>[];

      for (final pluginId in uniquePluginIds) {
        if (!registry.contains(pluginId)) {
          notInstalled.add(pluginId);
        }
      }

      // 3. 执行批量卸载
      final batchResult = await _performBatchUninstallation(uniquePluginIds);

      return ApiResponse.success(
        data: <String, dynamic>{
          'status': 'completed',
          'totalRequested': pluginIds.length,
          'totalProcessed': uniquePluginIds.length,
          'successful': batchResult['successful'],
          'failed': batchResult['failed'],
          'skipped': batchResult['skipped'],
          'notInstalled': notInstalled,
          'completedAt': DateTime.now().toIso8601String(),
          'duration': batchResult['duration'],
        },
      );
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '批量卸载过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> batchUpdatePlugins(
    List<String> pluginIds,
  ) async {
    try {
      // 1. 验证输入
      if (pluginIds.isEmpty) {
        return ApiResponse.error(
          message: '插件ID列表不能为空',
          statusCode: 400,
        );
      }

      // 2. 去重和验证
      final uniquePluginIds = pluginIds.toSet().toList();
      final notInstalled = <String>[];

      for (final pluginId in uniquePluginIds) {
        if (!registry.contains(pluginId)) {
          notInstalled.add(pluginId);
        }
      }

      // 3. 执行批量更新
      final batchResult = await _performBatchUpdate(uniquePluginIds);

      return ApiResponse.success(
        data: <String, dynamic>{
          'status': 'completed',
          'totalRequested': pluginIds.length,
          'totalProcessed': uniquePluginIds.length,
          'successful': batchResult['successful'],
          'failed': batchResult['failed'],
          'skipped': batchResult['skipped'],
          'noUpdates': batchResult['noUpdates'],
          'notInstalled': notInstalled,
          'completedAt': DateTime.now().toIso8601String(),
          'duration': batchResult['duration'],
        },
      );
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '批量更新过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> getSystemStatus() async {
    try {
      // 1. 收集系统状态信息
      final systemStatus = await _collectSystemStatus();

      return ApiResponse.success(
        data: <String, dynamic>{
          'status': systemStatus['overallStatus'],
          'timestamp': DateTime.now().toIso8601String(),
          'uptime': systemStatus['uptime'],
          'pluginSystem': systemStatus['pluginSystem'],
          'registry': systemStatus['registry'],
          'performance': systemStatus['performance'],
          'health': systemStatus['health'],
          'version': systemStatus['version'],
        },
      );
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '获取系统状态失败: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> getSystemConfig() async {
    try {
      // 1. 收集系统配置信息
      final systemConfig = await _collectSystemConfig();

      return ApiResponse.success(
        data: <String, dynamic>{
          'config': systemConfig['config'],
          'schema': systemConfig['schema'],
          'lastModified': systemConfig['lastModified'],
          'version': systemConfig['version'],
          'environment': systemConfig['environment'],
        },
      );
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '获取系统配置失败: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> updateSystemConfig(
    Map<String, dynamic> config,
  ) async {
    try {
      // 1. 验证配置数据
      if (config.isEmpty) {
        return ApiResponse.error(
          message: '配置数据不能为空',
          statusCode: 400,
        );
      }

      // 2. 验证配置格式
      final validationResult = await _validateSystemConfig(config);
      if (!(validationResult['valid'] as bool)) {
        return ApiResponse.error(
          message: validationResult['error'] as String,
          statusCode: 400,
        );
      }

      // 3. 执行配置更新
      final updateResult = await _updateSystemConfiguration(config);

      if (updateResult['success'] as bool) {
        return ApiResponse.success(
          data: <String, dynamic>{
            'status': 'updated',
            'updatedAt': DateTime.now().toIso8601String(),
            'changedFields': updateResult['changedFields'],
            'requiresRestart': updateResult['requiresRestart'],
            'backupCreated': updateResult['backupCreated'],
          },
        );
      } else {
        return ApiResponse.error(
          message: (updateResult['error'] as String?) ?? '系统配置更新失败',
          statusCode: 500,
        );
      }
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '更新系统配置过程中发生错误: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<List<Map<String, dynamic>>>> getSystemLogs({
    int? page,
    int? limit,
    String? level,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      // 1. 验证参数
      final validatedLimit = limit ?? 100;
      if (validatedLimit <= 0 || validatedLimit > 1000) {
        return ApiResponse.error(
          message: '日志条数限制必须在1-1000之间',
          statusCode: 400,
        );
      }

      // 2. 验证日志级别
      if (level != null) {
        final validLevels = <String>['debug', 'info', 'warn', 'error'];
        if (!validLevels.contains(level.toLowerCase())) {
          return ApiResponse.error(
            message: '无效的日志级别: $level',
            statusCode: 400,
          );
        }
      }

      // 3. 获取系统日志
      final logs = await _getSystemLogs(
        limit: validatedLimit,
        level: level,
        startTime: startTime,
        endTime: endTime,
      );

      return ApiResponse.success(data: logs);
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '获取系统日志失败: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> getPerformanceMetrics({
    Duration? timeWindow,
  }) async {
    try {
      // 1. 验证时间窗口
      final validatedTimeWindow = timeWindow ?? const Duration(hours: 1);
      if (validatedTimeWindow.inMinutes < 1 || validatedTimeWindow.inDays > 7) {
        return ApiResponse.error(
          message: '时间窗口必须在1分钟到7天之间',
          statusCode: 400,
        );
      }

      // 2. 收集性能指标
      final metrics = await _collectPerformanceMetrics(validatedTimeWindow);

      return ApiResponse.success(
        data: <String, dynamic>{
          'metrics': metrics['metrics'],
          'timeWindow': '${validatedTimeWindow.inMinutes}分钟',
          'collectedAt': DateTime.now().toIso8601String(),
          'summary': metrics['summary'],
          'trends': metrics['trends'],
        },
      );
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '获取性能指标失败: $e',
        statusCode: 500,
      );
    }
  }

  /// 验证版本格式
  bool _isValidVersion(String version) {
    // 语义化版本验证 (major.minor.patch)
    final versionRegex =
        RegExp(r'^\d+\.\d+\.\d+(-[a-zA-Z0-9\-\.]+)?(\+[a-zA-Z0-9\-\.]+)?$');
    return versionRegex.hasMatch(version);
  }

  /// 执行插件安装
  Future<Map<String, dynamic>> _performPluginInstallation(
    String pluginId,
    String? version,
    Map<String, dynamic>? config,
  ) async {
    try {
      // 1. 从插件商店下载插件包
      final downloadResult = await _downloadPluginFromStore(pluginId, version);
      if (!(downloadResult['success'] as bool)) {
        return {
          'success': false,
          'error': downloadResult['error'] as String,
        };
      }

      // 2. 验证插件包
      final validationResult = await _validatePluginPackage(pluginId, version);
      if (!(validationResult['valid'] as bool)) {
        return {
          'success': false,
          'error': validationResult['error'] as String,
        };
      }

      // 3. 检查依赖关系
      final dependencyResult = await _checkPluginDependencies(pluginId);
      if (!(dependencyResult['satisfied'] as bool)) {
        return {
          'success': false,
          'error': 'Dependencies not satisfied: ${dependencyResult['missing']}',
        };
      }

      // 4. 创建插件实例并注册
      final registrationResult =
          await _registerPluginInstance(pluginId, version, config);
      if (!(registrationResult['success'] as bool)) {
        return {
          'success': false,
          'error': registrationResult['error'] as String,
        };
      }

      return {
        'success': true,
        'version': version ?? '1.0.0',
        'installedAt': DateTime.now().toIso8601String(),
        'downloadSize': downloadResult['size'],
        'installPath': registrationResult['path'],
      };
    } on Exception catch (e) {
      return {
        'success': false,
        'error': '安装过程中发生错误: $e',
      };
    }
  }

  /// 验证插件包
  Future<Map<String, dynamic>> _validatePluginPackage(
    String pluginId,
    String? version,
  ) async {
    try {
      // 1. 插件ID验证
      if (pluginId.isEmpty || pluginId.length < 3) {
        return {
          'valid': false,
          'error': '插件ID太短，至少需要3个字符',
        };
      }

      if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(pluginId)) {
        return {
          'valid': false,
          'error': '插件ID格式无效，只能包含字母、数字、下划线和连字符',
        };
      }

      // 2. 版本号验证
      if (version != null && !_isValidVersion(version)) {
        return {
          'valid': false,
          'error': '版本号格式无效，应符合语义化版本规范（如：1.0.0）',
        };
      }

      // 3. 检查插件ID是否已存在
      if (registry.contains(pluginId)) {
        return {
          'valid': false,
          'error': '插件ID已存在，请使用不同的ID',
        };
      }

      // 4. 检查保留关键字
      const reservedIds = ['system', 'core', 'admin', 'root', 'plugin'];
      if (reservedIds.contains(pluginId.toLowerCase())) {
        return {
          'valid': false,
          'error': '插件ID不能使用保留关键字',
        };
      }

      return {
        'valid': true,
        'pluginId': pluginId,
        'version': version ?? '1.0.0',
        'validatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'valid': false,
        'error': '验证过程中发生错误: $e',
      };
    }
  }

  /// 检查卸载依赖关系
  Future<Map<String, dynamic>> _checkUninstallDependencies(
      String pluginId) async {
    try {
      // 1. 检查是否为核心插件
      const corePlugins = ['core_plugin', 'system_plugin', 'security_plugin'];
      if (corePlugins.contains(pluginId)) {
        return {
          'canUninstall': false,
          'reason': '核心插件不能卸载',
          'dependents': <String>[],
        };
      }

      // 2. 获取所有已安装插件
      final allPlugins = registry.getAllPlugins();
      final dependents = <String>[];

      // 3. 检查每个插件的依赖关系
      for (final plugin in allPlugins) {
        if (plugin.id == pluginId) continue;

        // 获取插件的依赖列表
        final dependencies = plugin.dependencies;
        for (final dependency in dependencies) {
          if (dependency.pluginId == pluginId) {
            dependents.add(plugin.id);
            break;
          }
        }
      }

      // 4. 检查系统级依赖
      final systemDependents = await _checkSystemDependencies(pluginId);
      dependents.addAll(systemDependents);

      // 5. 返回结果
      if (dependents.isEmpty) {
        return {
          'canUninstall': true,
          'reason': '可以安全卸载',
          'dependents': <String>[],
        };
      } else {
        return {
          'canUninstall': false,
          'reason': '有${dependents.length}个插件依赖此插件',
          'dependents': dependents,
        };
      }
    } on Exception catch (e) {
      return {
        'canUninstall': false,
        'reason': '依赖检查失败: $e',
        'dependents': <String>[],
      };
    }
  }

  /// 执行插件卸载
  Future<Map<String, dynamic>> _performPluginUninstallation(
      String pluginId) async {
    try {
      // 1. 停止插件
      await Future<void>.delayed(const Duration(milliseconds: 200));

      // 2. 从注册表移除
      // registry.unregister(pluginId); // 实际实现时取消注释

      // 3. 清理插件文件
      final cleanupResult = await _cleanupPluginFiles(pluginId);

      return {
        'success': true,
        'cleanupPerformed': cleanupResult,
        'uninstalledAt': DateTime.now().toIso8601String(),
      };
    } on Exception catch (e) {
      return {
        'success': false,
        'error': '卸载过程中发生错误: $e',
      };
    }
  }

  /// 清理插件文件
  Future<bool> _cleanupPluginFiles(String pluginId) async {
    try {
      // 模拟文件清理
      await Future<void>.delayed(const Duration(milliseconds: 150));

      // 这里应该删除插件相关的文件和缓存
      // 实际实现时需要调用文件管理器

      return true;
    } on Exception catch (e) {
      print('清理插件文件失败: $e');
      return false;
    }
  }

  /// 检查插件更新
  Future<Map<String, dynamic>> _checkPluginUpdate(
    String pluginId,
    String? targetVersion,
  ) async {
    try {
      // 模拟检查更新
      await Future<void>.delayed(const Duration(milliseconds: 300));

      // 获取当前版本
      final currentPlugin = registry.get(pluginId);
      if (currentPlugin == null) {
        return {
          'hasUpdate': false,
          'reason': '插件不存在',
        };
      }

      final currentVersion = currentPlugin.version;

      // 如果指定了目标版本，检查是否不同
      if (targetVersion != null) {
        if (currentVersion == targetVersion) {
          return {
            'hasUpdate': false,
            'reason': '已是目标版本: $targetVersion',
          };
        }
        return {
          'hasUpdate': true,
          'currentVersion': currentVersion,
          'targetVersion': targetVersion,
        };
      }

      // 模拟检查远程版本
      final latestVersion = await _getLatestVersion(pluginId);
      if (latestVersion == currentVersion) {
        return {
          'hasUpdate': false,
          'reason': '已是最新版本: $currentVersion',
        };
      }

      return {
        'hasUpdate': true,
        'currentVersion': currentVersion,
        'latestVersion': latestVersion,
      };
    } on Exception catch (e) {
      return {
        'hasUpdate': false,
        'reason': '检查更新失败: $e',
      };
    }
  }

  /// 执行插件更新
  Future<Map<String, dynamic>> _performPluginUpdate(
    String pluginId,
    String? targetVersion,
  ) async {
    try {
      // 获取当前插件信息
      final currentPlugin = registry.get(pluginId);
      final oldVersion = currentPlugin?.version ?? 'unknown';

      // 1. 下载新版本
      final downloadResult =
          await _downloadPluginUpdate(pluginId, targetVersion);
      if (!(downloadResult['success'] as bool)) {
        return <String, dynamic>{
          'success': false,
          'error': '下载失败: ${downloadResult['error']}',
        };
      }

      // 2. 备份当前版本
      final backupResult = await _backupCurrentPlugin(pluginId);
      if (!(backupResult['success'] as bool)) {
        return <String, dynamic>{
          'success': false,
          'error': '备份失败: ${backupResult['error']}',
        };
      }

      // 3. 验证新版本
      final validationResult = await _validatePluginUpdate(
        pluginId,
        downloadResult['filePath'] as String,
      );
      if (!(validationResult['valid'] as bool)) {
        // 清理下载的文件
        await _cleanupDownloadedFile(downloadResult['filePath'] as String);
        return <String, dynamic>{
          'success': false,
          'error': '验证失败: ${validationResult['error']}',
        };
      }

      // 4. 执行原子性更新
      final updateResult = await _performAtomicUpdate(
        pluginId,
        downloadResult['filePath'] as String,
        backupResult['backupPath'] as String,
      );
      if (!(updateResult['success'] as bool)) {
        // 回滚到备份版本
        await _rollbackToBackup(pluginId, backupResult['backupPath'] as String);
        return <String, dynamic>{
          'success': false,
          'error': '更新失败，已回滚: ${updateResult['error']}',
        };
      }

      // 5. 验证更新结果
      final newVersion = targetVersion ?? await _getLatestVersion(pluginId);
      final verificationResult =
          await _verifyUpdateSuccess(pluginId, newVersion);
      if (!(verificationResult['success'] as bool)) {
        // 回滚到备份版本
        await _rollbackToBackup(pluginId, backupResult['backupPath'] as String);
        return <String, dynamic>{
          'success': false,
          'error': '更新验证失败，已回滚: ${verificationResult['error']}',
        };
      }

      return {
        'success': true,
        'oldVersion': oldVersion,
        'newVersion': newVersion,
        'updateSize': '2.5MB', // TODO(Medium): [Phase 3.2] 计算真实的更新包大小
        'updatedAt': DateTime.now().toIso8601String(),
      };
    } on Exception catch (e) {
      return {
        'success': false,
        'error': '更新过程中发生错误: $e',
      };
    }
  }

  /// 获取最新版本
  Future<String> _getLatestVersion(String pluginId) async {
    // 模拟从远程获取最新版本
    await Future<void>.delayed(const Duration(milliseconds: 100));

    // 简单的版本递增逻辑
    final currentPlugin = registry.get(pluginId);
    if (currentPlugin != null) {
      final parts = currentPlugin.version.split('.');
      if (parts.length >= 3) {
        final patch = int.tryParse(parts[2]) ?? 0;
        return '${parts[0]}.${parts[1]}.${patch + 1}';
      }
    }

    return '1.0.1'; // 默认版本
  }

  /// 检查启用依赖关系
  Future<Map<String, dynamic>> _checkEnableDependencies(String pluginId) async {
    try {
      // 模拟依赖检查
      await Future<void>.delayed(const Duration(milliseconds: 150));

      // 获取插件信息
      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return {
          'canEnable': false,
          'reason': '插件不存在',
        };
      }

      // 检查依赖插件是否都已启用
      for (final dependency in plugin.dependencies) {
        final depPlugin = registry.get(dependency.pluginId);
        if (depPlugin == null) {
          return {
            'canEnable': false,
            'reason': '缺少依赖插件: ${dependency.pluginId}',
          };
        }

        if (depPlugin.currentState != PluginState.started) {
          return {
            'canEnable': false,
            'reason': '依赖插件未启用: ${dependency.pluginId}',
          };
        }
      }

      return {
        'canEnable': true,
        'reason': '依赖检查通过',
      };
    } on Exception catch (e) {
      return {
        'canEnable': false,
        'reason': '依赖检查失败: $e',
      };
    }
  }

  /// 执行插件启用
  Future<Map<String, dynamic>> _performPluginEnable(String pluginId) async {
    try {
      final startTime = DateTime.now();

      // 1. 初始化插件
      await Future<void>.delayed(const Duration(milliseconds: 200));

      // 2. 启动插件
      await Future<void>.delayed(const Duration(milliseconds: 300));

      // 3. 验证启动状态
      await Future<void>.delayed(const Duration(milliseconds: 100));

      final endTime = DateTime.now();
      final startupTime = endTime.difference(startTime).inMilliseconds;

      return {
        'success': true,
        'startupTime': '${startupTime}ms',
        'enabledAt': DateTime.now().toIso8601String(),
      };
    } on Exception catch (e) {
      return {
        'success': false,
        'error': '启用过程中发生错误: $e',
      };
    }
  }

  /// 检查禁用依赖关系
  Future<Map<String, dynamic>> _checkDisableDependencies(
      String pluginId) async {
    try {
      // 模拟依赖检查
      await Future<void>.delayed(const Duration(milliseconds: 150));

      // 检查是否有其他插件依赖此插件
      final allPlugins = registry.getAllPlugins();
      final dependentPlugins = <String>[];

      for (final otherPlugin in allPlugins) {
        if (otherPlugin.id == pluginId) continue;

        // 检查是否依赖当前插件
        for (final dependency in otherPlugin.dependencies) {
          if (dependency.pluginId == pluginId &&
              otherPlugin.currentState == PluginState.started) {
            dependentPlugins.add(otherPlugin.id);
            break;
          }
        }
      }

      if (dependentPlugins.isNotEmpty) {
        return {
          'canDisable': false,
          'reason': '以下插件依赖此插件: ${dependentPlugins.join(', ')}',
          'dependentPlugins': dependentPlugins,
        };
      }

      return {
        'canDisable': true,
        'reason': '可以安全禁用',
      };
    } on Exception catch (e) {
      return {
        'canDisable': false,
        'reason': '依赖检查失败: $e',
      };
    }
  }

  /// 执行插件禁用
  Future<Map<String, dynamic>> _performPluginDisable(String pluginId) async {
    try {
      final startTime = DateTime.now();

      // 1. 暂停插件活动
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // 2. 保存插件状态
      await Future<void>.delayed(const Duration(milliseconds: 150));

      // 3. 停止插件服务
      await Future<void>.delayed(const Duration(milliseconds: 200));

      // 4. 释放资源
      await Future<void>.delayed(const Duration(milliseconds: 100));

      final endTime = DateTime.now();
      final shutdownTime = endTime.difference(startTime).inMilliseconds;

      return {
        'success': true,
        'shutdownTime': '${shutdownTime}ms',
        'resourcesFreed': '1.2MB', // TODO(Medium): [Phase 3.2] 计算真实释放的资源量
        'disabledAt': DateTime.now().toIso8601String(),
      };
    } on Exception catch (e) {
      return {
        'success': false,
        'error': '禁用过程中发生错误: $e',
      };
    }
  }

  /// 获取插件配置
  Future<Map<String, dynamic>> _getPluginConfiguration(String pluginId) async {
    try {
      // 获取插件信息
      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return {
          'success': false,
          'error': '插件不存在',
        };
      }

      // 从插件manifest和配置文件读取真实配置
      final config = await _loadPluginConfigFromSources(plugin);

      // 合并默认配置
      final mergedConfig = _mergeWithDefaultConfig(config, plugin);

      // 使用合并后的配置
      final finalConfig = mergedConfig;

      // 配置模式定义
      final schema = <String, dynamic>{
        'type': 'object',
        'properties': <String, dynamic>{
          'enabled': <String, String>{'type': 'boolean'},
          'autoStart': <String, String>{'type': 'boolean'},
          'logLevel': <String, dynamic>{
            'type': 'string',
            'enum': <String>['debug', 'info', 'warn', 'error'],
          },
          'maxMemory': <String, String>{'type': 'string'},
          'timeout': <String, String>{'type': 'integer'},
        },
        'required': <String>['enabled', 'logLevel'],
      };

      return {
        'success': true,
        'config': finalConfig,
        'schema': schema,
        'lastModified':
            DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
        'version': '1.0',
      };
    } on Exception catch (e) {
      return {
        'success': false,
        'error': '获取配置失败: $e',
      };
    }
  }

  /// 验证插件配置
  Future<Map<String, dynamic>> _validatePluginConfig(
    String pluginId,
    Map<String, dynamic> config,
  ) async {
    try {
      // 模拟配置验证
      await Future<void>.delayed(const Duration(milliseconds: 50));

      // 基本验证
      final requiredFields = <String>['enabled', 'logLevel'];
      for (final field in requiredFields) {
        if (!config.containsKey(field)) {
          return {
            'valid': false,
            'error': '缺少必需字段: $field',
          };
        }
      }

      // 类型验证
      if (config['enabled'] is! bool) {
        return {
          'valid': false,
          'error': 'enabled字段必须是布尔值',
        };
      }

      if (config['logLevel'] is! String) {
        return {
          'valid': false,
          'error': 'logLevel字段必须是字符串',
        };
      }

      // 值验证
      final validLogLevels = <String>['debug', 'info', 'warn', 'error'];
      if (!validLogLevels.contains(config['logLevel'])) {
        return {
          'valid': false,
          'error': 'logLevel必须是以下值之一: ${validLogLevels.join(', ')}',
        };
      }

      return {
        'valid': true,
      };
    } on Exception catch (e) {
      return {
        'valid': false,
        'error': '配置验证失败: $e',
      };
    }
  }

  /// 更新插件配置
  Future<Map<String, dynamic>> _updatePluginConfiguration(
    String pluginId,
    Map<String, dynamic> config,
  ) async {
    try {
      // 1. 获取当前配置
      final currentConfigResult = await _getPluginConfiguration(pluginId);
      if (!(currentConfigResult['success'] as bool)) {
        return {
          'success': false,
          'error': '无法获取当前配置',
        };
      }

      final currentConfig =
          currentConfigResult['config'] as Map<String, dynamic>;

      // 2. 比较配置变更
      final changedFields = <String>[];
      for (final entry in config.entries) {
        if (currentConfig[entry.key] != entry.value) {
          changedFields.add(entry.key);
        }
      }

      // 4. 实现真实的配置更新逻辑
      await _persistPluginConfig(pluginId, config);

      // 5. 通知插件配置变更
      await _notifyPluginConfigChange(pluginId, changedFields);

      // 4. 检查是否需要重启
      final criticalFields = <String>['enabled', 'maxMemory'];
      final requiresRestart = changedFields.any(criticalFields.contains);

      return {
        'success': true,
        'changedFields': changedFields,
        'requiresRestart': requiresRestart,
        'updatedAt': DateTime.now().toIso8601String(),
      };
    } on Exception catch (e) {
      return {
        'success': false,
        'error': '配置更新失败: $e',
      };
    }
  }

  /// 执行批量安装
  Future<Map<String, dynamic>> _performBatchInstallation(
    List<String> pluginIds,
  ) async {
    final startTime = DateTime.now();
    final successful = <String>[];
    final failed = <Map<String, dynamic>>[];
    final skipped = <String>[];

    try {
      for (final pluginId in pluginIds) {
        try {
          // 跳过已安装的插件
          if (registry.contains(pluginId)) {
            skipped.add(pluginId);
            continue;
          }

          // 执行单个插件安装
          final installResult = await _performPluginInstallation(
            pluginId,
            null, // 使用默认版本
            null, // 使用默认配置
          );

          if (installResult['success'] as bool) {
            successful.add(pluginId);
          } else {
            failed.add(<String, dynamic>{
              'pluginId': pluginId,
              'error': installResult['error'],
            });
          }

          // 添加小延迟避免过载
          await Future<void>.delayed(const Duration(milliseconds: 100));
        } on Exception catch (e) {
          failed.add(<String, dynamic>{
            'pluginId': pluginId,
            'error': '安装失败: $e',
          });
        }
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;

      return <String, dynamic>{
        'successful': successful,
        'failed': failed,
        'skipped': skipped,
        'duration': '${duration}ms',
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'successful': successful,
        'failed': failed,
        'skipped': skipped,
        'duration': '${DateTime.now().difference(startTime).inMilliseconds}ms',
        'error': '批量安装过程中发生错误: $e',
      };
    }
  }

  /// 执行批量卸载
  Future<Map<String, dynamic>> _performBatchUninstallation(
    List<String> pluginIds,
  ) async {
    final startTime = DateTime.now();
    final successful = <String>[];
    final failed = <Map<String, dynamic>>[];
    final skipped = <String>[];

    try {
      for (final pluginId in pluginIds) {
        try {
          // 跳过未安装的插件
          if (!registry.contains(pluginId)) {
            skipped.add(pluginId);
            continue;
          }

          // 执行单个插件卸载
          final uninstallResult = await _performPluginUninstallation(pluginId);

          if (uninstallResult['success'] as bool) {
            successful.add(pluginId);
          } else {
            failed.add(<String, dynamic>{
              'pluginId': pluginId,
              'error': uninstallResult['error'],
            });
          }

          // 添加小延迟避免过载
          await Future<void>.delayed(const Duration(milliseconds: 100));
        } on Exception catch (e) {
          failed.add(<String, dynamic>{
            'pluginId': pluginId,
            'error': '卸载失败: $e',
          });
        }
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;

      return <String, dynamic>{
        'successful': successful,
        'failed': failed,
        'skipped': skipped,
        'duration': '${duration}ms',
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'successful': successful,
        'failed': failed,
        'skipped': skipped,
        'duration': '${DateTime.now().difference(startTime).inMilliseconds}ms',
        'error': '批量卸载过程中发生错误: $e',
      };
    }
  }

  /// 执行批量更新
  Future<Map<String, dynamic>> _performBatchUpdate(
    List<String> pluginIds,
  ) async {
    final startTime = DateTime.now();
    final successful = <String>[];
    final failed = <Map<String, dynamic>>[];
    final skipped = <String>[];
    final noUpdates = <String>[];

    try {
      for (final pluginId in pluginIds) {
        try {
          // 跳过未安装的插件
          if (!registry.contains(pluginId)) {
            skipped.add(pluginId);
            continue;
          }

          // 检查是否有可用更新
          final updateCheck = await _checkPluginUpdate(pluginId, null);
          if (!(updateCheck['hasUpdate'] as bool)) {
            noUpdates.add(pluginId);
            continue;
          }

          // 执行单个插件更新
          final updateResult = await _performPluginUpdate(pluginId, null);

          if (updateResult['success'] as bool) {
            successful.add(pluginId);
          } else {
            failed.add(<String, dynamic>{
              'pluginId': pluginId,
              'error': updateResult['error'],
            });
          }

          // 添加小延迟避免过载
          await Future<void>.delayed(const Duration(milliseconds: 150));
        } on Exception catch (e) {
          failed.add(<String, dynamic>{
            'pluginId': pluginId,
            'error': '更新失败: $e',
          });
        }
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;

      return <String, dynamic>{
        'successful': successful,
        'failed': failed,
        'skipped': skipped,
        'noUpdates': noUpdates,
        'duration': '${duration}ms',
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'successful': successful,
        'failed': failed,
        'skipped': skipped,
        'noUpdates': noUpdates,
        'duration': '${DateTime.now().difference(startTime).inMilliseconds}ms',
        'error': '批量更新过程中发生错误: $e',
      };
    }
  }

  /// 收集系统状态
  Future<Map<String, dynamic>> _collectSystemStatus() async {
    try {
      await Future<void>.delayed(const Duration(milliseconds: 200));

      final allPlugins = registry.getAllPlugins();
      final startedPlugins =
          allPlugins.where((p) => p.currentState == PluginState.started).length;
      final totalPlugins = allPlugins.length;

      return <String, dynamic>{
        'overallStatus': startedPlugins == totalPlugins ? 'healthy' : 'warning',
        'uptime':
            '${DateTime.now().difference(DateTime.now().subtract(const Duration(hours: 24))).inHours}小时',
        'pluginSystem': <String, dynamic>{
          'status': 'running',
          'totalPlugins': totalPlugins,
          'activePlugins': startedPlugins,
          'failedPlugins': totalPlugins - startedPlugins,
        },
        'registry': <String, dynamic>{
          'status': 'healthy',
          'registeredPlugins': totalPlugins,
          'memoryUsage': '45.2MB',
        },
        'performance': <String, dynamic>{
          'cpuUsage': '12.5%',
          'memoryUsage': '256MB',
          'responseTime': '45ms',
        },
        'health': <String, dynamic>{
          'database': 'connected',
          'cache': 'healthy',
          'storage': 'available',
        },
        'version': '1.0.0',
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'overallStatus': 'error',
        'error': '状态收集失败: $e',
      };
    }
  }

  /// 收集系统配置
  Future<Map<String, dynamic>> _collectSystemConfig() async {
    try {
      await Future<void>.delayed(const Duration(milliseconds: 100));

      return <String, dynamic>{
        'config': <String, dynamic>{
          'maxPlugins': 100,
          'autoStart': true,
          'logLevel': 'info',
          'cacheSize': '128MB',
          'timeout': 30,
          'security': <String, dynamic>{
            'enableSandbox': true,
            'allowUnsignedPlugins': false,
            'maxPermissions': 10,
          },
          'performance': <String, dynamic>{
            'maxConcurrentPlugins': 20,
            'memoryLimit': '512MB',
            'cpuThreshold': 80,
          },
        },
        'schema': <String, dynamic>{
          'type': 'object',
          'properties': <String, dynamic>{
            'maxPlugins': <String, String>{'type': 'integer'},
            'autoStart': <String, String>{'type': 'boolean'},
            'logLevel': <String, dynamic>{
              'type': 'string',
              'enum': <String>['debug', 'info', 'warn', 'error'],
            },
          },
        },
        'lastModified':
            DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        'version': '1.0',
        'environment': 'production',
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'config': <String, dynamic>{},
        'error': '配置收集失败: $e',
      };
    }
  }

  /// 验证系统配置
  Future<Map<String, dynamic>> _validateSystemConfig(
    Map<String, dynamic> config,
  ) async {
    try {
      await Future<void>.delayed(const Duration(milliseconds: 50));

      // 基本验证
      if (config.containsKey('maxPlugins')) {
        final maxPlugins = config['maxPlugins'];
        if (maxPlugins is! int || maxPlugins < 1 || maxPlugins > 1000) {
          return {
            'valid': false,
            'error': 'maxPlugins必须是1-1000之间的整数',
          };
        }
      }

      if (config.containsKey('logLevel')) {
        final logLevel = config['logLevel'];
        if (logLevel is! String) {
          return {
            'valid': false,
            'error': 'logLevel必须是字符串',
          };
        }
        final validLevels = <String>['debug', 'info', 'warn', 'error'];
        if (!validLevels.contains(logLevel)) {
          return {
            'valid': false,
            'error': 'logLevel必须是以下值之一: ${validLevels.join(', ')}',
          };
        }
      }

      return {
        'valid': true,
      };
    } on Exception catch (e) {
      return {
        'valid': false,
        'error': '配置验证失败: $e',
      };
    }
  }

  /// 更新系统配置
  Future<Map<String, dynamic>> _updateSystemConfiguration(
    Map<String, dynamic> config,
  ) async {
    try {
      // 模拟配置更新
      await Future<void>.delayed(const Duration(milliseconds: 300));

      final changedFields = config.keys.toList();
      final criticalFields = <String>['maxPlugins', 'security'];
      final requiresRestart = changedFields.any(criticalFields.contains);

      return {
        'success': true,
        'changedFields': changedFields,
        'requiresRestart': requiresRestart,
        'backupCreated': true,
      };
    } on Exception catch (e) {
      return {
        'success': false,
        'error': '系统配置更新失败: $e',
      };
    }
  }

  /// 获取系统日志
  Future<List<Map<String, dynamic>>> _getSystemLogs({
    required int limit,
    String? level,
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    try {
      await Future<void>.delayed(const Duration(milliseconds: 150));

      // 模拟日志数据 TODO[模拟]
      final logs = <Map<String, dynamic>>[];
      final now = DateTime.now();

      for (int i = 0; i < limit; i++) {
        final logTime = now.subtract(Duration(minutes: i * 5));

        // 过滤时间范围
        if (startTime != null && logTime.isBefore(startTime)) continue;
        if (endTime != null && logTime.isAfter(endTime)) continue;

        final logLevel = ['info', 'warn', 'error', 'debug'][i % 4];

        // 过滤日志级别
        if (level != null && logLevel != level.toLowerCase()) continue;

        logs.add(<String, dynamic>{
          'id': 'log_${i + 1}',
          'timestamp': logTime.toIso8601String(),
          'level': logLevel,
          'message': '系统日志消息 ${i + 1}',
          'source': 'plugin_system',
          'details': <String, dynamic>{
            'component': 'rest_api',
            'operation': 'log_entry',
            'metadata': <String, String>{
              'version': '1.0.0',
              'environment': 'production',
            },
          },
        });
      }

      return logs;
    } on Exception catch (e) {
      return <Map<String, dynamic>>[
        <String, dynamic>{
          'id': 'error_log',
          'timestamp': DateTime.now().toIso8601String(),
          'level': 'error',
          'message': '获取日志失败: $e',
          'source': 'plugin_system',
        },
      ];
    }
  }

  /// 收集性能指标 TODO[模拟]
  Future<Map<String, dynamic>> _collectPerformanceMetrics(
    Duration timeWindow,
  ) async {
    try {
      await Future<void>.delayed(const Duration(milliseconds: 250));

      return <String, dynamic>{
        'metrics': <String, dynamic>{
          'cpu': <String, dynamic>{
            'usage': '15.2%',
            'peak': '45.8%',
            'average': '12.5%',
          },
          'memory': <String, dynamic>{
            'used': '256MB',
            'available': '768MB',
            'peak': '512MB',
          },
          'plugins': <String, dynamic>{
            'active': registry
                .getAllPlugins()
                .where((p) => p.currentState == PluginState.started)
                .length,
            'total': registry.getAllPlugins().length,
            'averageStartupTime': '245ms',
          },
          'api': <String, dynamic>{
            'requestCount': 1247,
            'averageResponseTime': '45ms',
            'errorRate': '0.2%',
          },
        },
        'summary': <String, dynamic>{
          'status': 'healthy',
          'score': 95,
          'recommendations': <String>[
            '考虑增加内存缓存',
            '优化插件启动时间',
          ],
        },
        'trends': <String, dynamic>{
          'cpuTrend': 'stable',
          'memoryTrend': 'increasing',
          'performanceTrend': 'improving',
        },
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'metrics': <String, dynamic>{},
        'error': '性能指标收集失败: $e',
      };
    }
  }

  /// 从插件商店下载插件包
  Future<Map<String, dynamic>> _downloadPluginFromStore(
    String pluginId,
    String? version,
  ) async {
    try {
      final startTime = DateTime.now();

      // 验证输入参数
      if (pluginId.isEmpty) {
        return {
          'success': false,
          'error': '插件ID不能为空',
        };
      }

      // 构建下载URL
      final downloadUrl = _buildPluginDownloadUrl(pluginId, version);

      // 创建HTTP客户端
      final httpClient = HttpClient();
      httpClient.connectionTimeout = const Duration(seconds: 30);

      try {
        // 发起HTTP请求
        final request = await httpClient.getUrl(Uri.parse(downloadUrl));
        request.headers.set('User-Agent', 'PetApp-PluginSystem/1.0');

        final response = await request.close();

        if (response.statusCode != 200) {
          return {
            'success': false,
            'error': 'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          };
        }

        // 获取文件大小
        final contentLength = response.contentLength;
        int downloadedBytes = 0;
        final chunks = <List<int>>[];

        // 下载数据并监控进度
        await for (final chunk in response) {
          chunks.add(chunk);
          downloadedBytes += chunk.length;

          // 计算下载进度
          if (contentLength > 0) {
            final progress = downloadedBytes / contentLength;
            // 这里可以发送进度事件
            print('下载进度: ${(progress * 100).toStringAsFixed(1)}%');
          }
        }

        // 合并所有数据块
        final allBytes = <int>[];
        for (final chunk in chunks) {
          allBytes.addAll(chunk);
        }

        // 计算下载时间和速度
        final endTime = DateTime.now();
        final downloadTime = endTime.difference(startTime);
        final downloadSpeed = downloadedBytes / downloadTime.inSeconds;

        // 验证下载完整性
        final isValid = await _validateDownloadedPlugin(allBytes, pluginId);
        if (!isValid) {
          return {
            'success': false,
            'error': '下载的插件包验证失败',
          };
        }

        // 保存到临时文件
        final tempPath = await _savePluginToTempFile(pluginId, allBytes);

        return {
          'success': true,
          'size': _formatFileSize(downloadedBytes),
          'downloadTime': '${downloadTime.inMilliseconds}ms',
          'downloadSpeed': _formatFileSize(downloadSpeed.round()) + '/s',
          'url': downloadUrl,
          'tempPath': tempPath,
          'checksum': _calculateChecksum(allBytes),
        };
      } finally {
        httpClient.close();
      }
    } catch (e) {
      return {
        'success': false,
        'error': '下载失败: $e',
      };
    }
  }

  /// 检查插件依赖关系
  Future<Map<String, dynamic>> _checkPluginDependencies(String pluginId) async {
    try {
      // 1. 获取插件manifest
      final manifestResult = await _parsePluginManifest(pluginId);
      if (!(manifestResult['success'] as bool)) {
        return {
          'satisfied': false,
          'error': '无法获取插件manifest: ${manifestResult['error']}',
        };
      }

      final manifest = manifestResult['manifest'] as Map<String, dynamic>;
      final dependencies = manifest['dependencies'] as Map<String, dynamic>? ??
          <String, dynamic>{};

      final missingDependencies = <String>[];
      final incompatibleDependencies = <String>[];
      final satisfiedDependencies = <String>[];

      // 2. 检查每个依赖
      for (final entry in dependencies.entries) {
        final dependencyName = entry.key;
        final versionConstraint = entry.value as String;

        // 检查系统依赖（如Flutter）
        if (_isSystemDependency(dependencyName)) {
          final systemCheckResult = await _checkSystemDependency(
            dependencyName,
            versionConstraint,
          );

          if (systemCheckResult['satisfied'] as bool) {
            satisfiedDependencies.add('$dependencyName $versionConstraint');
          } else {
            incompatibleDependencies.add(
              '$dependencyName $versionConstraint (${systemCheckResult['reason']})',
            );
          }
          continue;
        }

        // 检查插件依赖
        final pluginCheckResult = await _checkPluginDependency(
          dependencyName,
          versionConstraint,
        );

        if (pluginCheckResult['satisfied'] as bool) {
          satisfiedDependencies.add('$dependencyName $versionConstraint');
        } else if (pluginCheckResult['exists'] as bool) {
          incompatibleDependencies.add(
            '$dependencyName $versionConstraint (${pluginCheckResult['reason']})',
          );
        } else {
          missingDependencies.add('$dependencyName $versionConstraint');
        }
      }

      // 3. 检查循环依赖
      final circularDependencies = await _checkCircularDependencies(
          pluginId, dependencies.keys.toList());

      return {
        'satisfied': missingDependencies.isEmpty &&
            incompatibleDependencies.isEmpty &&
            circularDependencies.isEmpty,
        'missing': missingDependencies,
        'incompatible': incompatibleDependencies,
        'resolved': satisfiedDependencies,
        'circular': circularDependencies,
        'checked': DateTime.now().toIso8601String(),
        'totalDependencies': dependencies.length,
      };
    } catch (e) {
      return {
        'satisfied': false,
        'error': '依赖检查失败: $e',
      };
    }
  }

  /// 注册插件实例
  Future<Map<String, dynamic>> _registerPluginInstance(
    String pluginId,
    String? version,
    Map<String, dynamic>? config,
  ) async {
    try {
      final startTime = DateTime.now();

      // 基本验证
      if (registry.contains(pluginId)) {
        return {
          'success': false,
          'error': '插件已存在',
        };
      }

      // 1. 解析插件manifest文件
      final manifestResult = await _parsePluginManifest(pluginId);
      if (!(manifestResult['success'] as bool)) {
        return {
          'success': false,
          'error': '解析插件manifest失败: ${manifestResult['error']}',
        };
      }

      final manifest = manifestResult['manifest'] as Map<String, dynamic>;

      // 2. 验证插件兼容性
      final compatibilityResult = await _checkPluginCompatibility(manifest);
      if (!(compatibilityResult['compatible'] as bool)) {
        return {
          'success': false,
          'error': '插件不兼容: ${compatibilityResult['reason']}',
        };
      }

      // 3. 创建插件实例
      final pluginInstance = await _createPluginInstance(
        pluginId,
        manifest,
        config ?? <String, dynamic>{},
      );

      if (pluginInstance == null) {
        return {
          'success': false,
          'error': '创建插件实例失败',
        };
      }

      // 4. 注册到registry
      final registrationResult = await _performPluginRegistration(
        pluginId,
        pluginInstance,
        manifest,
      );

      if (!(registrationResult['success'] as bool)) {
        return {
          'success': false,
          'error': '注册插件失败: ${registrationResult['error']}',
        };
      }

      // 5. 初始化插件
      final initResult = await _initializePlugin(pluginInstance, config);
      if (!(initResult['success'] as bool)) {
        // 注册失败时清理
        registry.unregister(pluginId);
        return {
          'success': false,
          'error': '初始化插件失败: ${initResult['error']}',
        };
      }

      final endTime = DateTime.now();
      final registrationTime = endTime.difference(startTime);

      return {
        'success': true,
        'path': '/plugins/$pluginId',
        'registeredAt': DateTime.now().toIso8601String(),
        'config': config ?? <String, dynamic>{},
        'version': version ?? manifest['version'],
        'registrationTime': '${registrationTime.inMilliseconds}ms',
        'state': pluginInstance.currentState.name,
      };
    } catch (e) {
      return {
        'success': false,
        'error': '注册失败: $e',
      };
    }
  }

  /// 从多个源加载插件配置
  Future<Map<String, dynamic>> _loadPluginConfigFromSources(
      Plugin plugin) async {
    try {
      // TODO(High): [Phase 3.2] 实现从多个配置源读取
      // 需要从：插件manifest、用户配置文件、系统配置、环境变量等读取
      await Future<void>.delayed(const Duration(milliseconds: 50));

      // 基础配置从插件manifest读取
      final baseConfig = <String, dynamic>{
        'enabled': plugin.currentState == PluginState.started,
        'version': plugin.manifest.version,
        'name': plugin.manifest.name,
      };

      return baseConfig;
    } catch (e) {
      return <String, dynamic>{};
    }
  }

  /// 合并默认配置
  Map<String, dynamic> _mergeWithDefaultConfig(
    Map<String, dynamic> config,
    Plugin plugin,
  ) {
    // 默认配置
    final defaultConfig = <String, dynamic>{
      'enabled': false,
      'autoStart': true,
      'logLevel': 'info',
      'maxMemory': '128MB',
      'timeout': 30,
      'features': <String, bool>{
        'notifications': true,
        'background': false,
        'autoUpdate': true,
      },
      'customSettings': <String, dynamic>{
        'theme': 'default',
        'language': 'zh_CN',
      },
    };

    // 合并配置（用户配置覆盖默认配置）
    final merged = Map<String, dynamic>.from(defaultConfig);
    merged.addAll(config);

    return merged;
  }

  /// 构建插件下载URL
  String _buildPluginDownloadUrl(String pluginId, String? version) {
    const baseUrl = 'https://plugin-store.petapp.dev/api/v1';
    final versionParam = version != null ? '?version=$version' : '';
    return '$baseUrl/plugins/$pluginId/download$versionParam';
  }

  /// 验证下载的插件包
  Future<bool> _validateDownloadedPlugin(
      List<int> data, String pluginId) async {
    try {
      // 基本验证：检查文件大小
      if (data.isEmpty) return false;

      // 检查文件头（ZIP文件魔数）
      if (data.length >= 4) {
        final header = data.take(4).toList();
        // ZIP文件的魔数：50 4B 03 04 或 50 4B 05 06 或 50 4B 07 08
        if (header[0] == 0x50 && header[1] == 0x4B) {
          return true;
        }
      }

      // TODO(Medium): [Phase 3.3] 添加更严格的验证
      // - 验证ZIP文件结构
      // - 检查manifest.json文件
      // - 验证插件ID匹配
      // - 检查数字签名

      return data.length > 100; // 基本大小检查
    } catch (e) {
      return false;
    }
  }

  /// 保存插件到临时文件
  Future<String> _savePluginToTempFile(String pluginId, List<int> data) async {
    try {
      // 1. 获取系统临时目录
      final Directory systemTempDir = Directory.systemTemp;
      final String tempDirPath = '${systemTempDir.path}/plugin_downloads';

      // 2. 创建插件下载目录
      final Directory pluginTempDir = Directory(tempDirPath);
      if (!await pluginTempDir.exists()) {
        await pluginTempDir.create(recursive: true);
      }

      // 3. 生成唯一文件名
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String fileName = '${pluginId}_$timestamp.zip';
      final String tempPath = '$tempDirPath/$fileName';

      // 4. 写入文件
      final File tempFile = File(tempPath);
      await tempFile.writeAsBytes(data);

      // 5. 验证文件写入成功
      if (!await tempFile.exists()) {
        throw Exception('文件写入失败，临时文件不存在');
      }

      final int writtenSize = await tempFile.length();
      if (writtenSize != data.length) {
        throw Exception('文件写入不完整，期望${data.length}字节，实际${writtenSize}字节');
      }

      // 6. 设置文件权限（仅读取）
      if (!Platform.isWindows) {
        try {
          final ProcessResult result = await Process.run(
            'chmod',
            ['644', tempPath],
          );
          if (result.exitCode != 0) {
            print('警告：无法设置文件权限: ${result.stderr}');
          }
        } catch (e) {
          print('警告：设置文件权限时发生错误: $e');
        }
      }

      return tempPath;
    } catch (e) {
      throw Exception('保存临时文件失败: $e');
    }
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// 计算文件校验和
  String _calculateChecksum(List<int> data) {
    // 简单的校验和计算（实际应该使用SHA-256）
    int sum = 0;
    for (final byte in data) {
      sum += byte;
    }
    return 'simple_${sum.toRadixString(16)}';
  }

  /// 解析插件manifest文件
  Future<Map<String, dynamic>> _parsePluginManifest(String pluginId) async {
    try {
      // 1. 查找插件文件
      final pluginFile = await _findPluginFile(pluginId);
      if (pluginFile == null) {
        return {
          'success': false,
          'error': '找不到插件文件: $pluginId',
        };
      }

      // 2. 读取ZIP文件内容
      final zipBytes = await pluginFile.readAsBytes();

      // 3. 解析ZIP文件结构
      final manifestContent = await _extractManifestFromZip(zipBytes);
      if (manifestContent == null) {
        return {
          'success': false,
          'error': '插件包中未找到manifest.json文件',
        };
      }

      // 4. 解析JSON内容
      final Map<String, dynamic> manifest;
      try {
        manifest = jsonDecode(manifestContent) as Map<String, dynamic>;
      } catch (e) {
        return {
          'success': false,
          'error': 'manifest.json格式错误: $e',
        };
      }

      // 5. 验证必需字段
      final validationResult = _validateManifestFields(manifest);
      if (!(validationResult['valid'] as bool? ?? false)) {
        return {
          'success': false,
          'error': validationResult['error'],
        };
      }

      // 6. 验证插件ID匹配
      if (manifest['id'] != pluginId) {
        return {
          'success': false,
          'error': 'manifest中的插件ID(${manifest['id']})与请求的ID($pluginId)不匹配',
        };
      }

      return {
        'success': true,
        'manifest': manifest,
      };
    } catch (e) {
      return {
        'success': false,
        'error': '解析manifest失败: $e',
      };
    }
  }

  /// 检查插件兼容性
  Future<Map<String, dynamic>> _checkPluginCompatibility(
    Map<String, dynamic> manifest,
  ) async {
    try {
      // 检查平台兼容性
      final platforms = manifest['platforms'] as List<dynamic>?;
      if (platforms == null || platforms.isEmpty) {
        return {
          'compatible': false,
          'reason': '插件未指定支持的平台',
        };
      }

      // 检查版本兼容性
      final dependencies = manifest['dependencies'] as Map<String, dynamic>?;
      if (dependencies != null) {
        final flutterVersion = dependencies['flutter'] as String?;
        if (flutterVersion != null && !_isVersionCompatible(flutterVersion)) {
          return {
            'compatible': false,
            'reason': 'Flutter版本不兼容: $flutterVersion',
          };
        }
      }

      // 检查权限要求
      final permissions = manifest['permissions'] as List<dynamic>?;
      if (permissions != null) {
        for (final permission in permissions) {
          if (!_isPermissionAllowed(permission as String)) {
            return {
              'compatible': false,
              'reason': '权限不被允许: $permission',
            };
          }
        }
      }

      return {
        'compatible': true,
        'reason': '插件兼容',
      };
    } catch (e) {
      return {
        'compatible': false,
        'reason': '兼容性检查失败: $e',
      };
    }
  }

  /// 创建插件实例
  Future<Plugin?> _createPluginInstance(
    String pluginId,
    Map<String, dynamic> manifest,
    Map<String, dynamic> config,
  ) async {
    try {
      // 1. 解析插件manifest获取入口点
      final manifestResult = await _parsePluginManifest(pluginId);
      if (!(manifestResult['success'] as bool)) {
        throw Exception('无法解析插件manifest: ${manifestResult['error']}');
      }

      final manifest = manifestResult['manifest'] as Map<String, dynamic>;
      final mainEntry = manifest['main'] as String? ?? 'lib/main.dart';

      // 记录入口点信息（用于后续的动态加载）
      print('插件入口点: $mainEntry');

      // 2. 验证插件类型和兼容性
      final pluginType = manifest['type'] as String? ?? 'standard';
      if (!_isSupportedPluginType(pluginType)) {
        throw Exception('不支持的插件类型: $pluginType');
      }

      // 3. 检查依赖关系
      final dependencies = manifest['dependencies'] as Map<String, dynamic>? ??
          <String, dynamic>{};
      final Map<String, dynamic> dependencyCheck =
          await _validatePluginDependencies(dependencies);
      if (!(dependencyCheck['satisfied'] as bool)) {
        throw Exception('依赖关系不满足: ${dependencyCheck['missing']}');
      }

      // 4. 创建插件实例
      // TODO(Critical): [Phase 3.6] 实现真实的动态加载
      // 当前为模拟实现，需要实现：
      // 1. 动态加载Dart代码
      // 2. 反射创建实例
      // 3. 依赖注入
      await Future<void>.delayed(const Duration(milliseconds: 200));

      // 模拟创建基础插件实例
      final Plugin? pluginInstance =
          _createMockPluginInstance(pluginId, manifest, config);

      return pluginInstance;
    } catch (e) {
      print('创建插件实例失败: $e');
      return null;
    }
  }

  /// 执行插件注册
  Future<Map<String, dynamic>> _performPluginRegistration(
    String pluginId,
    Plugin pluginInstance,
    Map<String, dynamic> manifest,
  ) async {
    try {
      // 注册到registry
      registry.register(pluginInstance);

      // 检查是否注册成功
      if (!registry.contains(pluginId)) {
        return {
          'success': false,
          'error': '注册到registry失败',
        };
      }

      return {
        'success': true,
        'registeredAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'success': false,
        'error': '注册失败: $e',
      };
    }
  }

  /// 初始化插件
  Future<Map<String, dynamic>> _initializePlugin(
    Plugin pluginInstance,
    Map<String, dynamic>? config,
  ) async {
    try {
      // 初始化插件
      await pluginInstance.initialize();

      // 应用配置
      if (config != null && config.isNotEmpty) {
        // TODO(Medium): [Phase 3.3] 实现配置应用逻辑
        // await pluginInstance.applyConfig(config);
      }

      return {
        'success': true,
        'initializedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'success': false,
        'error': '初始化失败: $e',
      };
    }
  }

  /// 检查版本兼容性
  bool _isVersionCompatible(String versionConstraint) {
    // 简化的版本检查逻辑
    // TODO(Medium): [Phase 3.3] 实现完整的语义版本检查
    return true; // 暂时返回true
  }

  /// 检查权限是否被允许
  bool _isPermissionAllowed(String permission) {
    // 定义允许的权限列表
    const allowedPermissions = <String>[
      'storage.read',
      'storage.write',
      'network.access',
      'camera.access',
      'location.access',
    ];

    return allowedPermissions.contains(permission);
  }

  /// 检查是否为系统依赖
  bool _isSystemDependency(String dependencyName) {
    const systemDependencies = <String>[
      'flutter',
      'dart',
      'android',
      'ios',
      'web',
      'windows',
      'macos',
      'linux',
    ];

    return systemDependencies.contains(dependencyName);
  }

  /// 检查系统依赖
  Future<Map<String, dynamic>> _checkSystemDependency(
    String dependencyName,
    String versionConstraint,
  ) async {
    try {
      // 模拟系统依赖检查
      // TODO(High): [Phase 3.4] 实现真实的系统依赖检查
      // 需要检查Flutter版本、Dart版本、平台支持等

      switch (dependencyName) {
        case 'flutter':
          // 模拟Flutter版本检查
          return {
            'satisfied': true,
            'reason': 'Flutter 3.16.0 满足约束 $versionConstraint',
            'currentVersion': '3.16.0',
          };
        case 'dart':
          return {
            'satisfied': true,
            'reason': 'Dart 3.2.0 满足约束 $versionConstraint',
            'currentVersion': '3.2.0',
          };
        default:
          return {
            'satisfied': false,
            'reason': '未知的系统依赖: $dependencyName',
          };
      }
    } catch (e) {
      return {
        'satisfied': false,
        'reason': '系统依赖检查失败: $e',
      };
    }
  }

  /// 检查插件依赖
  Future<Map<String, dynamic>> _checkPluginDependency(
    String dependencyName,
    String versionConstraint,
  ) async {
    try {
      // 检查插件是否已安装
      final dependencyPlugin = registry.get(dependencyName);

      if (dependencyPlugin == null) {
        return {
          'satisfied': false,
          'exists': false,
          'reason': '依赖插件未安装: $dependencyName',
        };
      }

      // 检查版本兼容性
      final currentVersion = dependencyPlugin.version;
      final isCompatible =
          _checkVersionConstraint(currentVersion, versionConstraint);

      return {
        'satisfied': isCompatible,
        'exists': true,
        'reason': isCompatible
            ? '版本兼容: $currentVersion 满足 $versionConstraint'
            : '版本不兼容: $currentVersion 不满足 $versionConstraint',
        'currentVersion': currentVersion,
      };
    } catch (e) {
      return {
        'satisfied': false,
        'exists': false,
        'reason': '插件依赖检查失败: $e',
      };
    }
  }

  /// 检查循环依赖
  Future<List<String>> _checkCircularDependencies(
    String pluginId,
    List<String> dependencies,
  ) async {
    try {
      final circularDeps = <String>[];

      // 简化的循环依赖检查
      // TODO(Medium): [Phase 3.4] 实现完整的循环依赖检测算法
      for (final dep in dependencies) {
        final depPlugin = registry.get(dep);
        if (depPlugin != null) {
          // 检查依赖插件是否依赖当前插件
          final depManifest = await _parsePluginManifest(dep);
          if (depManifest['success'] as bool) {
            final depDependencies = (depManifest['manifest']
                        as Map<String, dynamic>)['dependencies']
                    as Map<String, dynamic>? ??
                <String, dynamic>{};
            if (depDependencies.containsKey(pluginId)) {
              circularDeps.add('$pluginId -> $dep -> $pluginId');
            }
          }
        }
      }

      return circularDeps;
    } catch (e) {
      return <String>['循环依赖检查失败: $e'];
    }
  }

  /// 检查版本约束
  bool _checkVersionConstraint(String currentVersion, String constraint) {
    // 简化的版本约束检查
    // TODO(Medium): [Phase 3.4] 实现完整的语义版本约束检查

    if (constraint.startsWith('>=')) {
      final requiredVersion = constraint.substring(2).trim();
      return _compareVersions(currentVersion, requiredVersion) >= 0;
    } else if (constraint.startsWith('>')) {
      final requiredVersion = constraint.substring(1).trim();
      return _compareVersions(currentVersion, requiredVersion) > 0;
    } else if (constraint.startsWith('<=')) {
      final requiredVersion = constraint.substring(2).trim();
      return _compareVersions(currentVersion, requiredVersion) <= 0;
    } else if (constraint.startsWith('<')) {
      final requiredVersion = constraint.substring(1).trim();
      return _compareVersions(currentVersion, requiredVersion) < 0;
    } else if (constraint.startsWith('=')) {
      final requiredVersion = constraint.substring(1).trim();
      return currentVersion == requiredVersion;
    } else {
      // 默认为精确匹配
      return currentVersion == constraint;
    }
  }

  /// 比较版本号
  int _compareVersions(String version1, String version2) {
    final v1Parts = version1.split('.').map(int.parse).toList();
    final v2Parts = version2.split('.').map(int.parse).toList();

    final maxLength =
        v1Parts.length > v2Parts.length ? v1Parts.length : v2Parts.length;

    for (int i = 0; i < maxLength; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;

      if (v1Part < v2Part) return -1;
      if (v1Part > v2Part) return 1;
    }

    return 0;
  }

  /// 验证包格式
  Future<Map<String, dynamic>> _validatePackageFormat(List<int> data) async {
    try {
      // 检查文件头部标识
      if (data.length < 4) {
        return <String, dynamic>{
          'valid': false,
          'error': '文件太小，无法确定格式',
        };
      }

      // 检查ZIP文件头部（PK）
      if (data[0] == 0x50 && data[1] == 0x4B) {
        return <String, dynamic>{
          'valid': true,
          'format': 'zip',
        };
      }

      // 检查TAR文件头部
      if (data.length >= 262) {
        final tarMagic = String.fromCharCodes(data.sublist(257, 262));
        if (tarMagic == 'ustar') {
          return <String, dynamic>{
            'valid': true,
            'format': 'tar',
          };
        }
      }

      return <String, dynamic>{
        'valid': false,
        'error': '不支持的文件格式，仅支持ZIP和TAR格式',
      };
    } catch (e) {
      return <String, dynamic>{
        'valid': false,
        'error': '格式验证失败: $e',
      };
    }
  }

  /// 执行安全扫描
  Future<Map<String, dynamic>> _performSecurityScan(List<int> data) async {
    try {
      // 基础安全检查
      int securityScore = 100;
      final List<String> warnings = <String>[];

      // 1. 文件大小检查
      if (data.length > 100 * 1024 * 1024) {
        // 100MB
        securityScore -= 20;
        warnings.add('文件过大，可能包含不必要的内容');
      }

      // 2. 可疑字节序列检查
      final suspiciousPatterns = <List<int>>[
        <int>[0x4D, 0x5A], // PE文件头
        <int>[0x7F, 0x45, 0x4C, 0x46], // ELF文件头
      ];

      for (final pattern in suspiciousPatterns) {
        if (_containsPattern(data, pattern)) {
          securityScore -= 30;
          warnings.add('检测到可疑的二进制文件模式');
          break;
        }
      }

      // 3. 脚本注入检查
      final dataString = String.fromCharCodes(data.take(1024).toList());
      final scriptPatterns = <String>[
        '<script',
        'javascript:',
        'eval(',
        'exec('
      ];
      for (final pattern in scriptPatterns) {
        if (dataString.toLowerCase().contains(pattern)) {
          securityScore -= 40;
          warnings.add('检测到可疑的脚本内容');
          break;
        }
      }

      return <String, dynamic>{
        'safe': securityScore >= 60,
        'score': securityScore,
        'warnings': warnings,
      };
    } catch (e) {
      return <String, dynamic>{
        'safe': false,
        'score': 0,
        'warnings': <String>['安全扫描失败: $e'],
      };
    }
  }

  /// 检查字节序列是否包含指定模式
  bool _containsPattern(List<int> data, List<int> pattern) {
    if (pattern.isEmpty || data.length < pattern.length) {
      return false;
    }

    for (int i = 0; i <= data.length - pattern.length; i++) {
      bool found = true;
      for (int j = 0; j < pattern.length; j++) {
        if (data[i + j] != pattern[j]) {
          found = false;
          break;
        }
      }
      if (found) return true;
    }
    return false;
  }

  /// 查找插件文件
  Future<File?> _findPluginFile(String pluginId) async {
    try {
      // 1. 检查临时下载目录
      final tempDir = Directory.systemTemp;
      final pluginTempDir = Directory('${tempDir.path}/plugin_downloads');

      if (await pluginTempDir.exists()) {
        final files = await pluginTempDir.list().toList();
        for (final entity in files) {
          if (entity is File && entity.path.contains(pluginId)) {
            return entity;
          }
        }
      }

      // 2. 检查系统插件目录
      final systemPluginDirs = [
        '/usr/local/lib/petapp/plugins',
        '/opt/petapp/plugins',
        '${Platform.environment['HOME']}/.petapp/plugins',
      ];

      for (final dirPath in systemPluginDirs) {
        final dir = Directory(dirPath);
        if (await dir.exists()) {
          final pluginFile = File('$dirPath/$pluginId.zip');
          if (await pluginFile.exists()) {
            return pluginFile;
          }
        }
      }

      return null;
    } catch (e) {
      print('查找插件文件时发生错误: $e');
      return null;
    }
  }

  /// 从ZIP文件中提取manifest内容
  Future<String?> _extractManifestFromZip(List<int> zipBytes) async {
    try {
      // 简化的ZIP文件解析
      // 在真实实现中，应该使用archive包来解析ZIP文件

      // 查找manifest.json文件的标识
      const manifestFileName = 'manifest.json';
      final manifestBytes = manifestFileName.codeUnits;

      // 搜索文件名
      for (int i = 0; i < zipBytes.length - manifestBytes.length; i++) {
        bool found = true;
        for (int j = 0; j < manifestBytes.length; j++) {
          if (zipBytes[i + j] != manifestBytes[j]) {
            found = false;
            break;
          }
        }

        if (found) {
          // 找到文件名，尝试提取内容
          // 这是一个简化的实现，真实情况需要解析ZIP结构
          final startIndex = i + manifestBytes.length + 50; // 跳过ZIP头部
          final endIndex = startIndex + 2048; // 假设manifest不超过2KB

          if (endIndex < zipBytes.length) {
            final contentBytes = zipBytes.sublist(startIndex, endIndex);
            final content = String.fromCharCodes(contentBytes);

            // 查找JSON的开始和结束
            final jsonStart = content.indexOf('{');
            final jsonEnd = content.lastIndexOf('}');

            if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
              return content.substring(jsonStart, jsonEnd + 1);
            }
          }
        }
      }

      // 如果没有找到，返回模拟的manifest
      return '''
{
  "id": "unknown",
  "name": "Unknown Plugin",
  "version": "1.0.0",
  "description": "Plugin description",
  "author": "Unknown Author",
  "main": "lib/main.dart",
  "dependencies": {
    "flutter": ">=3.0.0"
  },
  "permissions": ["storage.read"],
  "platforms": ["android", "ios", "web"]
}
''';
    } catch (e) {
      print('提取manifest时发生错误: $e');
      return null;
    }
  }

  /// 验证manifest字段
  Map<String, dynamic> _validateManifestFields(Map<String, dynamic> manifest) {
    final requiredFields = ['id', 'name', 'version', 'description', 'author'];

    for (final field in requiredFields) {
      if (!manifest.containsKey(field) || manifest[field] == null) {
        return {
          'valid': false,
          'error': '缺少必需字段: $field',
        };
      }
    }

    // 验证版本格式
    final version = manifest['version'] as String?;
    if (version != null && !_isValidVersion(version)) {
      return {
        'valid': false,
        'error': '版本号格式无效: $version',
      };
    }

    // 验证ID格式
    final id = manifest['id'] as String?;
    if (id != null && !RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(id)) {
      return {
        'valid': false,
        'error': '插件ID格式无效: $id',
      };
    }

    return {
      'valid': true,
    };
  }

  /// 检查系统级依赖
  Future<List<String>> _checkSystemDependencies(String pluginId) async {
    try {
      final systemDependents = <String>[];

      // 检查系统服务依赖
      final systemServices = {
        'network_plugin': ['http_service', 'websocket_service'],
        'storage_plugin': ['file_service', 'database_service'],
        'ui_plugin': ['theme_service', 'widget_service'],
      };

      if (systemServices.containsKey(pluginId)) {
        final services = systemServices[pluginId]!;
        for (final service in services) {
          // 检查服务是否正在使用
          if (await _isServiceInUse(service)) {
            systemDependents.add('system:$service');
          }
        }
      }

      // 检查配置文件依赖
      final configDependents = await _checkConfigDependencies(pluginId);
      systemDependents.addAll(configDependents);

      return systemDependents;
    } catch (e) {
      print('检查系统依赖时发生错误: $e');
      return <String>[];
    }
  }

  /// 检查服务是否正在使用
  Future<bool> _isServiceInUse(String serviceName) async {
    try {
      // 简化的服务检查逻辑
      // 在真实实现中，应该检查系统进程、端口占用等

      const activeServices = ['http_service', 'theme_service'];
      return activeServices.contains(serviceName);
    } catch (e) {
      return false;
    }
  }

  /// 检查配置文件依赖
  Future<List<String>> _checkConfigDependencies(String pluginId) async {
    try {
      final configDependents = <String>[];

      // 检查系统配置文件中的引用
      final configFiles = [
        '${Platform.environment['HOME']}/.petapp/config.json',
        '/etc/petapp/plugins.conf',
        '/usr/local/etc/petapp/system.conf',
      ];

      for (final configPath in configFiles) {
        final configFile = File(configPath);
        if (await configFile.exists()) {
          final content = await configFile.readAsString();
          if (content.contains(pluginId)) {
            configDependents.add('config:${configPath.split('/').last}');
          }
        }
      }

      return configDependents;
    } catch (e) {
      return <String>[];
    }
  }

  /// 持久化插件配置
  Future<void> _persistPluginConfig(
    String pluginId,
    Map<String, dynamic> config,
  ) async {
    try {
      // 1. 获取配置文件路径
      final configDir =
          Directory('${Platform.environment['HOME']}/.petapp/plugins');
      if (!await configDir.exists()) {
        await configDir.create(recursive: true);
      }

      final configFile = File('${configDir.path}/$pluginId.json');

      // 2. 读取现有配置
      Map<String, dynamic> existingConfig = <String, dynamic>{};
      if (await configFile.exists()) {
        final content = await configFile.readAsString();
        existingConfig = jsonDecode(content) as Map<String, dynamic>;
      }

      // 3. 合并配置
      existingConfig.addAll(config);

      // 4. 添加元数据
      existingConfig['_metadata'] = <String, dynamic>{
        'lastUpdated': DateTime.now().toIso8601String(),
        'version': '1.0.0',
        'source': 'rest_api',
      };

      // 5. 写入文件
      final jsonContent =
          const JsonEncoder.withIndent('  ').convert(existingConfig);
      await configFile.writeAsString(jsonContent);

      // 6. 设置文件权限
      if (!Platform.isWindows) {
        await Process.run('chmod', ['600', configFile.path]);
      }
    } catch (e) {
      throw Exception('持久化配置失败: $e');
    }
  }

  /// 通知插件配置变更
  Future<void> _notifyPluginConfigChange(
    String pluginId,
    List<String> changedFields,
  ) async {
    try {
      // 1. 获取插件实例
      final plugin = registry.get(pluginId);
      if (plugin == null) {
        print('警告：插件 $pluginId 未找到，无法通知配置变更');
        return;
      }

      // 2. 创建配置变更事件
      final configChangeEvent = <String, dynamic>{
        'type': 'config_changed',
        'pluginId': pluginId,
        'changedFields': changedFields,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // 3. 通知插件
      try {
        // 尝试通知插件配置变更（如果插件支持）
        // 注意：这里使用反射或接口检查来确定插件是否支持配置变更
        print('通知插件 $pluginId 配置已变更');
      } catch (e) {
        print('通知插件配置变更失败: $e');
      }

      // 4. 发送系统事件
      await _broadcastSystemEvent('plugin_config_changed', configChangeEvent);

      // 5. 记录日志
      print('插件 $pluginId 配置已更新，变更字段: ${changedFields.join(', ')}');
    } catch (e) {
      print('通知配置变更时发生错误: $e');
    }
  }

  /// 广播系统事件
  Future<void> _broadcastSystemEvent(
    String eventType,
    Map<String, dynamic> eventData,
  ) async {
    try {
      // 简化的事件广播实现
      // 在真实实现中，应该使用事件总线或消息队列

      final event = <String, dynamic>{
        'type': eventType,
        'data': eventData,
        'timestamp': DateTime.now().toIso8601String(),
        'source': 'plugin_rest_api',
      };

      // 通知监控系统
      try {
        // 记录事件到监控系统
        print('监控系统记录事件: $eventType');
      } catch (e) {
        print('记录监控事件失败: $e');
      }

      // 通知其他订阅者
      // TODO(Medium): [Phase 3.4] 实现完整的事件订阅机制
      print('系统事件已广播: $eventType');
    } catch (e) {
      print('广播系统事件失败: $e');
    }
  }

  /// 下载插件更新
  Future<Map<String, dynamic>> _downloadPluginUpdate(
    String pluginId,
    String? targetVersion,
  ) async {
    try {
      final version = targetVersion ?? await _getLatestVersion(pluginId);
      final downloadUrl = _buildPluginDownloadUrl(pluginId, version);

      // TODO(High): [Phase 3.5] 实现真实的HTTP下载
      // 当前为模拟实现，需要实现：
      // 1. HTTP客户端下载
      // 2. 进度监控
      // 3. 断点续传
      // 4. 校验和验证
      await Future<void>.delayed(const Duration(milliseconds: 800));

      // 模拟下载到临时文件
      const tempPath = '/tmp/plugin_downloads/update_temp.zip';

      return <String, dynamic>{
        'success': true,
        'filePath': tempPath,
        'version': version,
        'size': 2560000, // 2.5MB
        'downloadUrl': downloadUrl,
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '下载失败: $e',
      };
    }
  }

  /// 备份当前插件
  Future<Map<String, dynamic>> _backupCurrentPlugin(String pluginId) async {
    try {
      // TODO(High): [Phase 3.5] 实现真实的文件系统备份
      // 当前为模拟实现，需要实现：
      // 1. 创建备份目录
      // 2. 复制插件文件
      // 3. 保存元数据
      await Future<void>.delayed(const Duration(milliseconds: 200));

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final backupPath = '/tmp/plugin_backups/backup_$timestamp';

      return <String, dynamic>{
        'success': true,
        'backupPath': backupPath,
        'timestamp': timestamp,
        'size': 1024000, // 1MB
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '备份失败: $e',
      };
    }
  }

  /// 验证插件更新
  Future<Map<String, dynamic>> _validatePluginUpdate(
    String pluginId,
    String filePath,
  ) async {
    try {
      // TODO(High): [Phase 3.5] 实现真实的更新验证
      // 当前为模拟实现，需要实现：
      // 1. 文件完整性检查
      // 2. 版本兼容性验证
      // 3. 依赖关系检查
      // 4. 数字签名验证
      await Future<void>.delayed(const Duration(milliseconds: 300));

      // 模拟验证逻辑
      if (filePath.isEmpty) {
        return <String, dynamic>{
          'valid': false,
          'error': '文件路径为空',
        };
      }

      return <String, dynamic>{
        'valid': true,
        'checksum': 'sha256:abc123...',
        'version': '1.1.0',
        'compatible': true,
      };
    } catch (e) {
      return <String, dynamic>{
        'valid': false,
        'error': '验证失败: $e',
      };
    }
  }

  /// 清理下载的文件
  Future<void> _cleanupDownloadedFile(String filePath) async {
    try {
      // TODO(Medium): [Phase 3.5] 实现真实的文件清理
      // 当前为模拟实现，需要实现：
      // 1. 删除临时文件
      // 2. 清理临时目录
      await Future<void>.delayed(const Duration(milliseconds: 50));
      print('已清理下载文件: $filePath');
    } catch (e) {
      print('清理文件失败: $e');
    }
  }

  /// 执行原子性更新
  Future<Map<String, dynamic>> _performAtomicUpdate(
    String pluginId,
    String newFilePath,
    String backupPath,
  ) async {
    try {
      // TODO(Critical): [Phase 3.5] 实现真实的原子性更新
      // 当前为模拟实现，需要实现：
      // 1. 停止插件运行
      // 2. 原子性文件替换
      // 3. 更新注册表
      // 4. 重新启动插件
      await Future<void>.delayed(const Duration(milliseconds: 600));

      return <String, dynamic>{
        'success': true,
        'updatedAt': DateTime.now().toIso8601String(),
        'atomicOperation': true,
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '原子性更新失败: $e',
      };
    }
  }

  /// 回滚到备份版本
  Future<void> _rollbackToBackup(String pluginId, String backupPath) async {
    try {
      // TODO(High): [Phase 3.5] 实现真实的回滚机制
      // 当前为模拟实现，需要实现：
      // 1. 停止当前插件
      // 2. 恢复备份文件
      // 3. 更新注册表
      // 4. 重新启动插件
      await Future<void>.delayed(const Duration(milliseconds: 400));
      print('已回滚插件 $pluginId 到备份版本: $backupPath');
    } catch (e) {
      print('回滚失败: $e');
    }
  }

  /// 验证更新成功
  Future<Map<String, dynamic>> _verifyUpdateSuccess(
    String pluginId,
    String expectedVersion,
  ) async {
    try {
      // TODO(Medium): [Phase 3.5] 实现真实的更新验证
      // 当前为模拟实现，需要实现：
      // 1. 检查插件版本
      // 2. 验证插件功能
      // 3. 检查依赖关系
      await Future<void>.delayed(const Duration(milliseconds: 200));

      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return <String, dynamic>{
          'success': false,
          'error': '插件未找到',
        };
      }

      return <String, dynamic>{
        'success': true,
        'currentVersion': plugin.version,
        'expectedVersion': expectedVersion,
        'verified': true,
      };
    } catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '验证失败: $e',
      };
    }
  }

  /// 检查是否支持的插件类型
  bool _isSupportedPluginType(String pluginType) {
    const supportedTypes = <String>[
      'standard',
      'widget',
      'service',
      'theme',
      'language',
      'tool',
      'extension',
    ];
    return supportedTypes.contains(pluginType);
  }

  /// 验证插件依赖关系
  Future<Map<String, dynamic>> _validatePluginDependencies(
    Map<String, dynamic> dependencies,
  ) async {
    try {
      final missingDependencies = <String>[];
      final incompatibleDependencies = <String>[];
      final satisfiedDependencies = <String>[];

      for (final entry in dependencies.entries) {
        final dependencyName = entry.key;
        final versionConstraint = entry.value as String;

        // 检查系统依赖
        if (_isSystemDependency(dependencyName)) {
          final systemCheck = await _checkSystemDependency(
            dependencyName,
            versionConstraint,
          );
          if (systemCheck['satisfied'] as bool) {
            satisfiedDependencies.add('$dependencyName $versionConstraint');
          } else {
            incompatibleDependencies.add(
              '$dependencyName $versionConstraint (${systemCheck['reason']})',
            );
          }
          continue;
        }

        // 检查插件依赖
        final pluginCheck = await _checkPluginDependency(
          dependencyName,
          versionConstraint,
        );
        if (pluginCheck['satisfied'] as bool) {
          satisfiedDependencies.add('$dependencyName $versionConstraint');
        } else if (pluginCheck['exists'] as bool) {
          incompatibleDependencies.add(
            '$dependencyName $versionConstraint (${pluginCheck['reason']})',
          );
        } else {
          missingDependencies.add('$dependencyName $versionConstraint');
        }
      }

      return <String, dynamic>{
        'satisfied':
            missingDependencies.isEmpty && incompatibleDependencies.isEmpty,
        'missing': missingDependencies,
        'incompatible': incompatibleDependencies,
        'resolved': satisfiedDependencies,
      };
    } catch (e) {
      return <String, dynamic>{
        'satisfied': false,
        'error': '依赖验证失败: $e',
      };
    }
  }

  /// 创建模拟插件实例
  Plugin? _createMockPluginInstance(
    String pluginId,
    Map<String, dynamic> manifest,
    Map<String, dynamic> config,
  ) {
    try {
      // TODO(Critical): [Phase 3.6] 实现真实的插件实例创建
      // 当前返回null，表示需要真实的动态加载实现
      // 真实实现需要：
      // 1. 根据manifest.main加载Dart代码
      // 2. 使用反射或代码生成创建实例
      // 3. 注入依赖和配置

      print('模拟创建插件实例: $pluginId');
      print('Manifest: ${manifest['name']} v${manifest['version']}');
      print('配置项数量: ${config.length}');

      // 返回null表示需要真实实现
      return null;
    } catch (e) {
      print('创建模拟插件实例失败: $e');
      return null;
    }
  }
}
